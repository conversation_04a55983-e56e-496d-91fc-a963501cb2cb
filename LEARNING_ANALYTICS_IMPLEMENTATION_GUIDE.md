# Hướng Dẫn Triển Khai Hệ Thống Phân Tích Chuẩn Đầu Ra

## 📋 Tổng Quan

Tài liệu này hướng dẫn triển khai các tính năng phân tích dữ liệu về chuẩn đầu ra của sinh viên trong hệ thống quản lý chương trình đào tạo.

## 🎯 Mục Tiêu

- Theo dõi tiến độ sinh viên trong toàn bộ chương trình đào tạo
- Phân tích việc đạt được các PO (Program Outcomes) và PLO (Program Learning Outcomes)
- Cung cấp báo cáo chi tiết về hiệu suất học tập
- Hỗ trợ ra quyết định dựa trên dữ liệu

## 🏗️ Kiến Trúc Hệ Thống

### Models Mới Được Thêm

1. **StudentProgramProgress**: <PERSON> dõi tiến độ tổng thể của sinh viên
2. **SubjectOutcomeAnalysis**: <PERSON>ân tích chuẩn đầu ra theo từng môn học
3. **ProgramOutcomeTracking**: Theo dõi việc đạt PO/PLO của sinh viên
4. **LearningAnalytics**: Tổng hợp dữ liệu phân tích đa chiều

### Controllers Mới

1. **learningAnalyticsController**: Xử lý phân tích dữ liệu tổng hợp
2. **reportController**: Tạo các báo cáo chi tiết

## 🚀 Hướng Dẫn Triển Khai

### Bước 1: Cập Nhật Database

```bash
# Chạy script tối ưu hóa database
psql -U your_username -d your_database -f backend/database_optimization.sql
```

### Bước 2: Cài Đặt Dependencies

```bash
cd backend
npm install
```

### Bước 3: Khởi Động Server

```bash
npm start
```

## 📊 API Endpoints Mới

### Learning Analytics

#### 1. Tạo Phân Tích Chương Trình
```http
POST /api/learning-analytics/program-analysis
Authorization: Bearer <token>
Content-Type: application/json

{
  "program_id": 1,
  "time_period": {
    "start_date": "2024-01-01",
    "end_date": "2024-12-31",
    "semester": "2024-1",
    "academic_year": "2023-2024"
  },
  "analysis_config": {
    "include_predictions": true,
    "detailed_breakdown": true
  }
}
```

#### 2. Lấy Danh Sách Sinh Viên Theo Tiến Độ
```http
GET /api/learning-analytics/program/1/students?status=active&page=1&limit=20
Authorization: Bearer <token>
```

#### 3. Phân Tích Chi Tiết Sinh Viên
```http
GET /api/learning-analytics/student/123/program/1/analysis
Authorization: Bearer <token>
```

### Reports

#### 1. Báo Cáo Tổng Quan Chương Trình
```http
GET /api/reports/program/1/overview?semester=2024-1&academic_year=2023-2024
Authorization: Bearer <token>
```

#### 2. Báo Cáo Chi Tiết Sinh Viên
```http
GET /api/reports/student/123/program/1/detail
Authorization: Bearer <token>
```

#### 3. Báo Cáo So Sánh Môn Học
```http
GET /api/reports/program/1/subjects/comparison?subject_ids=1,2,3&semester=2024-1
Authorization: Bearer <token>
```

## 📈 Cấu Trúc Dữ Liệu

### StudentProgramProgress
```json
{
  "progress_id": 1,
  "user_id": 123,
  "program_id": 1,
  "overall_progress": {
    "total_subjects": 40,
    "completed_subjects": 25,
    "completion_percentage": 62.5,
    "gpa": 3.2,
    "credits_earned": 75,
    "total_credits_required": 120
  },
  "po_progress": {
    "1": {"average_score": 75, "completion_rate": 80},
    "2": {"average_score": 68, "completion_rate": 70}
  },
  "plo_progress": {
    "1": {"average_score": 72, "mastery_level": "good"},
    "2": {"average_score": 85, "mastery_level": "excellent"}
  },
  "predictions": {
    "graduation_probability": 85,
    "expected_graduation_date": "2025-06-30",
    "at_risk_subjects": ["Math Advanced"],
    "recommended_actions": ["Extra tutoring in Math"]
  }
}
```

### ProgramOutcomeTracking
```json
{
  "tracking_id": 1,
  "user_id": 123,
  "program_id": 1,
  "po_id": 1,
  "outcome_type": "PO",
  "current_score": 75.5,
  "target_score": 70,
  "achievement_status": "achieved",
  "score_history": [
    {"date": "2024-01-15", "score": 65, "source_subject_id": 1},
    {"date": "2024-03-20", "score": 72, "source_subject_id": 2},
    {"date": "2024-05-10", "score": 75.5, "source_subject_id": 3}
  ],
  "predictions": {
    "predicted_final_score": 78,
    "probability_of_achievement": 90,
    "recommended_interventions": ["Continue current pace"]
  }
}
```

## 🔧 Tối Ưu Hóa Performance

### Database Indexes
- Đã tạo indexes cho các truy vấn phân tích thường xuyên
- Sử dụng composite indexes cho queries phức tạp
- GIN indexes cho JSON fields

### Materialized Views
- `mv_program_statistics`: Thống kê tổng quan chương trình
- `mv_subject_performance`: Hiệu suất môn học

### Refresh Materialized Views
```sql
SELECT refresh_analytics_views();
```

## 📊 Dashboard và Visualization

### Dữ Liệu Radar Chart
```json
{
  "po_scores": {
    "1": 75,
    "2": 68,
    "3": 82
  },
  "plo_scores": {
    "1": 72,
    "2": 85,
    "3": 79
  }
}
```

### Temporal Trends
```json
{
  "weekly": {
    "2024-W01": {"avg_score": 70, "participation": 85},
    "2024-W02": {"avg_score": 72, "participation": 88}
  },
  "monthly": {
    "2024-01": {"avg_score": 71, "completion_rate": 80},
    "2024-02": {"avg_score": 74, "completion_rate": 85}
  }
}
```

## 🔐 Phân Quyền

- **Admin**: Truy cập tất cả dữ liệu phân tích
- **Teacher**: Truy cập dữ liệu của các lớp/môn mình dạy
- **Student**: Chỉ xem được dữ liệu của chính mình

## 🧪 Testing

### Test API Endpoints
```bash
# Test tạo phân tích chương trình
curl -X POST http://localhost:3000/api/learning-analytics/program-analysis \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"program_id": 1, "time_period": {}}'

# Test báo cáo tổng quan
curl -X GET http://localhost:3000/api/reports/program/1/overview \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 📝 Monitoring và Maintenance

### Kiểm Tra Performance
```sql
-- Check index usage
SELECT schemaname, tablename, indexname, idx_scan 
FROM pg_stat_user_indexes 
WHERE schemaname = 'public' 
ORDER BY idx_scan DESC;

-- Check table sizes
SELECT tablename, pg_size_pretty(pg_total_relation_size(tablename)) 
FROM pg_tables 
WHERE schemaname = 'public';
```

### Scheduled Tasks
- Refresh materialized views hàng ngày
- Cleanup old analytics data hàng tháng
- Update student progress weekly

## 🚨 Troubleshooting

### Common Issues

1. **Slow Queries**: Kiểm tra indexes và consider partitioning
2. **Memory Issues**: Tăng work_mem cho analytics queries
3. **Disk Space**: Monitor và archive old data

### Performance Tuning
```sql
-- Tăng work_mem cho session hiện tại
SET work_mem = '256MB';

-- Enable parallel processing
SET max_parallel_workers_per_gather = 4;
```

## 📚 Tài Liệu Tham Khảo

- [PostgreSQL Performance Tuning](https://www.postgresql.org/docs/current/performance-tips.html)
- [Sequelize Optimization](https://sequelize.org/docs/v6/other-topics/optimizations/)
- [Node.js Best Practices](https://github.com/goldbergyoni/nodebestpractices)

## 🔄 Roadmap

### Phase 2 Features
- Machine Learning predictions
- Real-time analytics dashboard
- Advanced visualization components
- Mobile app integration
- Export to Excel/PDF reports
