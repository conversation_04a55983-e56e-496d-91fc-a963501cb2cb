version: '3.8'

services:
  # PostgreSQL database
  postgres:
    image: postgres:15
    container_name: ql_ctdt_postgres
    environment:
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_DB: ${DB_NAME}
    command: postgres -c 'max_connections=1000'
    ports:
      - "5433:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - ql_ctdt_network
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U ${DB_USER} -d ${DB_NAME}" ]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis cache
  redis:
    image: redis:7
    container_name: ql_ctdt_redis
    command: redis-server --requirepass ${REDIS_PASSWORD} --appendonly yes
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - ql_ctdt_network
    healthcheck:
      test: [ "CMD", "redis-cli", "-a", "${REDIS_PASSWORD}", "ping" ]
      interval: 10s
      timeout: 5s
      retries: 5

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: ql_ctdt_backend
    environment:
      - NODE_ENV=production
      - PORT=8888
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USER=${DB_USER}
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_NAME=${DB_NAME}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    ports:
      - "8888:8888"
    volumes:
      - ./backend:/app
      - /app/node_modules
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - ql_ctdt_network
    restart: unless-stopped

  # Frontend Application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: ql_ctdt_frontend
    environment:
      - NEXT_PUBLIC_API_URL=http://backend:8888
      - NODE_ENV=production
    ports:
      - "3000:3000"
    volumes:
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - ql_ctdt_network
    restart: unless-stopped

  nginx:
    image: nginx:latest
    container_name: ql_ctdt_nginx
    ports:
      - "80:80"
      - "443:443" # Bật nếu dùng HTTPS
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - /etc/letsencrypt:/etc/letsencrypt:ro
    depends_on:
      - frontend
      - backend
    networks:
      - ql_ctdt_network
    restart: unless-stopped

networks:
  ql_ctdt_network:
    driver: bridge
    ipam:
      driver: default
      config:
        - subnet: **********/16

volumes:
  postgres_data:
  redis_data:
