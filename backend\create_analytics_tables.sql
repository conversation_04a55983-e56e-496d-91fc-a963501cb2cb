-- =====================================================
-- CREATE ANALYTICS TABLES FOR POSTGRESQL
-- =====================================================

-- 1. StudentProgramProgress Table
-- <PERSON> dõi tiến độ sinh viên trong toàn bộ chương trình đào tạo
CREATE TABLE IF NOT EXISTS "StudentProgramProgress" (
    "progress_id" SERIAL PRIMARY KEY,
    "user_id" INTEGER NOT NULL,
    "program_id" INTEGER NOT NULL,
    
    -- Tiến độ tổng thể
    "overall_progress" JSONB NOT NULL DEFAULT '{
        "total_subjects": 0,
        "completed_subjects": 0,
        "in_progress_subjects": 0,
        "completion_percentage": 0,
        "gpa": 0,
        "credits_earned": 0,
        "total_credits_required": 0
    }'::jsonb,
    
    -- Tiến độ theo PO (Program Outcomes)
    "po_progress" JSONB NOT NULL DEFAULT '{}'::jsonb,
    
    -- Tiến độ theo PLO (Program Learning Outcomes)
    "plo_progress" JSONB NOT NULL DEFAULT '{}'::jsonb,
    
    -- Phân tích theo thời gian
    "semester_progress" JSONB NOT NULL DEFAULT '{}'::jsonb,
    
    -- Điểm mạnh và yếu
    "strengths_weaknesses" JSONB NOT NULL DEFAULT '{
        "strong_areas": [],
        "weak_areas": [],
        "improvement_suggestions": []
    }'::jsonb,
    
    -- Dự đoán và khuyến nghị
    "predictions" JSONB NOT NULL DEFAULT '{
        "graduation_probability": 0,
        "expected_graduation_date": null,
        "at_risk_subjects": [],
        "recommended_actions": []
    }'::jsonb,
    
    -- Thời gian cập nhật cuối
    "last_updated" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    
    -- Trạng thái sinh viên
    "student_status" VARCHAR(20) NOT NULL DEFAULT 'active',
    
    -- Ngày bắt đầu chương trình
    "program_start_date" TIMESTAMP WITH TIME ZONE,
    
    -- Ngày dự kiến tốt nghiệp
    "expected_graduation_date" TIMESTAMP WITH TIME ZONE,
    
    -- Timestamps
    "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT "StudentProgramProgress_user_program_unique" UNIQUE("user_id", "program_id"),
    CONSTRAINT "StudentProgramProgress_status_check" CHECK ("student_status" IN ('active', 'on_leave', 'graduated', 'dropped_out')),
    CONSTRAINT "StudentProgramProgress_user_fk" FOREIGN KEY ("user_id") REFERENCES "Users"("user_id") ON DELETE CASCADE,
    CONSTRAINT "StudentProgramProgress_program_fk" FOREIGN KEY ("program_id") REFERENCES "Programs"("program_id") ON DELETE CASCADE
);

-- 2. SubjectOutcomeAnalysis Table
-- Phân tích chuẩn đầu ra theo từng môn học
CREATE TABLE IF NOT EXISTS "SubjectOutcomeAnalysis" (
    "analysis_id" SERIAL PRIMARY KEY,
    "subject_id" INTEGER NOT NULL,
    "program_id" INTEGER NOT NULL,
    
    -- Thống kê tổng quan môn học
    "subject_statistics" JSONB NOT NULL DEFAULT '{
        "total_students_enrolled": 0,
        "total_students_completed": 0,
        "completion_rate": 0,
        "average_score": 0,
        "pass_rate": 0,
        "dropout_rate": 0
    }'::jsonb,
    
    -- Phân tích theo PO (Program Outcomes)
    "po_achievement" JSONB NOT NULL DEFAULT '{}'::jsonb,
    
    -- Phân tích theo PLO (Program Learning Outcomes)
    "plo_achievement" JSONB NOT NULL DEFAULT '{}'::jsonb,
    
    -- Phân tích theo Learning Outcomes của môn học
    "lo_performance" JSONB NOT NULL DEFAULT '{}'::jsonb,
    
    -- Phân tích theo độ khó
    "difficulty_analysis" JSONB NOT NULL DEFAULT '{
        "easy": {"question_count": 0, "average_score": 0, "pass_rate": 0},
        "medium": {"question_count": 0, "average_score": 0, "pass_rate": 0},
        "hard": {"question_count": 0, "average_score": 0, "pass_rate": 0}
    }'::jsonb,
    
    -- Xu hướng theo thời gian
    "temporal_trends" JSONB NOT NULL DEFAULT '{}'::jsonb,
    
    -- Phân tích so sánh
    "comparative_analysis" JSONB NOT NULL DEFAULT '{
        "vs_program_average": 0,
        "vs_previous_semester": 0,
        "ranking_in_program": 0,
        "benchmark_comparison": {}
    }'::jsonb,
    
    -- Khuyến nghị cải thiện
    "improvement_recommendations" JSONB NOT NULL DEFAULT '{
        "weak_areas": [],
        "suggested_interventions": [],
        "resource_recommendations": [],
        "teaching_method_suggestions": []
    }'::jsonb,
    
    -- Dữ liệu chất lượng
    "data_quality_metrics" JSONB NOT NULL DEFAULT '{
        "sample_size": 0,
        "data_completeness": 0,
        "confidence_level": 0,
        "last_assessment_date": null
    }'::jsonb,
    
    -- Kỳ học phân tích
    "analysis_semester" VARCHAR(10) NOT NULL,
    
    -- Năm học
    "academic_year" VARCHAR(10) NOT NULL,
    
    -- Ngày tạo phân tích
    "analysis_date" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    
    -- Trạng thái phân tích
    "analysis_status" VARCHAR(20) NOT NULL DEFAULT 'draft',
    
    -- Người thực hiện phân tích
    "analyzed_by" INTEGER,
    
    -- Timestamps
    "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT "SubjectOutcomeAnalysis_subject_semester_unique" UNIQUE("subject_id", "analysis_semester", "academic_year"),
    CONSTRAINT "SubjectOutcomeAnalysis_status_check" CHECK ("analysis_status" IN ('draft', 'completed', 'reviewed', 'approved')),
    CONSTRAINT "SubjectOutcomeAnalysis_subject_fk" FOREIGN KEY ("subject_id") REFERENCES "Subjects"("subject_id") ON DELETE CASCADE,
    CONSTRAINT "SubjectOutcomeAnalysis_program_fk" FOREIGN KEY ("program_id") REFERENCES "Programs"("program_id") ON DELETE CASCADE,
    CONSTRAINT "SubjectOutcomeAnalysis_analyzer_fk" FOREIGN KEY ("analyzed_by") REFERENCES "Users"("user_id") ON DELETE SET NULL
);

-- 3. ProgramOutcomeTracking Table
-- Theo dõi việc đạt được các PO/PLO của sinh viên qua thời gian
CREATE TABLE IF NOT EXISTS "ProgramOutcomeTracking" (
    "tracking_id" SERIAL PRIMARY KEY,
    "user_id" INTEGER NOT NULL,
    "program_id" INTEGER NOT NULL,
    "po_id" INTEGER,
    "plo_id" INTEGER,
    
    -- Loại outcome đang track
    "outcome_type" VARCHAR(10) NOT NULL,
    
    -- Điểm số hiện tại
    "current_score" FLOAT NOT NULL DEFAULT 0,
    
    -- Điểm mục tiêu
    "target_score" FLOAT NOT NULL DEFAULT 70,
    
    -- Trạng thái đạt được
    "achievement_status" VARCHAR(20) NOT NULL DEFAULT 'not_started',
    
    -- Lịch sử điểm số theo thời gian
    "score_history" JSONB NOT NULL DEFAULT '[]'::jsonb,
    
    -- Phân tích chi tiết
    "detailed_analysis" JSONB NOT NULL DEFAULT '{
        "contributing_subjects": {},
        "assessment_breakdown": {},
        "improvement_trend": 0,
        "consistency_score": 0
    }'::jsonb,
    
    -- Dự đoán và khuyến nghị
    "predictions" JSONB NOT NULL DEFAULT '{
        "predicted_final_score": 0,
        "probability_of_achievement": 0,
        "estimated_completion_date": null,
        "risk_factors": [],
        "recommended_interventions": []
    }'::jsonb,
    
    -- Milestone tracking
    "milestones" JSONB NOT NULL DEFAULT '{
        "checkpoints": [],
        "next_milestone": null,
        "completion_percentage": 0
    }'::jsonb,
    
    -- Metadata
    "evidence_count" INTEGER NOT NULL DEFAULT 0,
    "last_assessment_date" TIMESTAMP WITH TIME ZONE,
    "first_assessment_date" TIMESTAMP WITH TIME ZONE,
    
    -- Trọng số trong chương trình
    "program_weight" FLOAT NOT NULL DEFAULT 1.0,
    
    -- Ghi chú và nhận xét
    "notes" TEXT,
    
    -- Trạng thái active
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    
    -- Thời gian cập nhật cuối
    "last_updated" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    
    -- Timestamps
    "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT "ProgramOutcomeTracking_outcome_type_check" CHECK ("outcome_type" IN ('PO', 'PLO')),
    CONSTRAINT "ProgramOutcomeTracking_score_check" CHECK ("current_score" >= 0 AND "current_score" <= 100),
    CONSTRAINT "ProgramOutcomeTracking_target_check" CHECK ("target_score" >= 0 AND "target_score" <= 100),
    CONSTRAINT "ProgramOutcomeTracking_status_check" CHECK ("achievement_status" IN ('not_started', 'in_progress', 'achieved', 'exceeded', 'at_risk')),
    CONSTRAINT "ProgramOutcomeTracking_po_plo_check" CHECK (
        ("po_id" IS NOT NULL AND "plo_id" IS NULL AND "outcome_type" = 'PO') OR 
        ("po_id" IS NULL AND "plo_id" IS NOT NULL AND "outcome_type" = 'PLO')
    ),
    CONSTRAINT "ProgramOutcomeTracking_user_fk" FOREIGN KEY ("user_id") REFERENCES "Users"("user_id") ON DELETE CASCADE,
    CONSTRAINT "ProgramOutcomeTracking_program_fk" FOREIGN KEY ("program_id") REFERENCES "Programs"("program_id") ON DELETE CASCADE,
    CONSTRAINT "ProgramOutcomeTracking_po_fk" FOREIGN KEY ("po_id") REFERENCES "POs"("po_id") ON DELETE CASCADE,
    CONSTRAINT "ProgramOutcomeTracking_plo_fk" FOREIGN KEY ("plo_id") REFERENCES "PLOs"("plo_id") ON DELETE CASCADE
);

-- 4. LearningAnalytics Table
-- Tổng hợp dữ liệu phân tích học tập đa chiều
CREATE TABLE IF NOT EXISTS "LearningAnalytics" (
    "analytics_id" SERIAL PRIMARY KEY,
    "program_id" INTEGER NOT NULL,
    "subject_id" INTEGER,
    
    -- Loại phân tích
    "analysis_type" VARCHAR(30) NOT NULL,
    
    -- Phạm vi thời gian
    "time_period" JSONB NOT NULL DEFAULT '{}'::jsonb,
    
    -- Dữ liệu tổng quan
    "overview_metrics" JSONB NOT NULL DEFAULT '{
        "total_students": 0,
        "total_assessments": 0,
        "average_performance": 0,
        "completion_rate": 0,
        "engagement_score": 0
    }'::jsonb,
    
    -- Phân tích theo PO/PLO
    "outcome_analysis" JSONB NOT NULL DEFAULT '{}'::jsonb,
    
    -- Phân tích theo Learning Outcomes
    "lo_performance" JSONB NOT NULL DEFAULT '{}'::jsonb,
    
    -- Phân tích theo độ khó
    "difficulty_distribution" JSONB NOT NULL DEFAULT '{
        "easy": {"count": 0, "avg_score": 0, "pass_rate": 0},
        "medium": {"count": 0, "avg_score": 0, "pass_rate": 0},
        "hard": {"count": 0, "avg_score": 0, "pass_rate": 0}
    }'::jsonb,
    
    -- Phân tích xu hướng thời gian
    "temporal_trends" JSONB NOT NULL DEFAULT '{}'::jsonb,
    
    -- Phân tích nhóm sinh viên
    "student_segmentation" JSONB NOT NULL DEFAULT '{
        "high_performers": {"count": 0, "characteristics": []},
        "average_performers": {"count": 0, "characteristics": []},
        "at_risk_students": {"count": 0, "characteristics": []},
        "improvement_needed": {"count": 0, "characteristics": []}
    }'::jsonb,
    
    -- Phân tích tương quan
    "correlation_analysis" JSONB NOT NULL DEFAULT '{}'::jsonb,
    
    -- Dự đoán và khuyến nghị
    "predictive_insights" JSONB NOT NULL DEFAULT '{
        "performance_predictions": {},
        "risk_assessments": {},
        "intervention_recommendations": [],
        "resource_optimization": {}
    }'::jsonb,
    
    -- Benchmarking
    "benchmark_comparisons" JSONB NOT NULL DEFAULT '{
        "vs_previous_periods": {},
        "vs_other_programs": {},
        "vs_national_standards": {},
        "improvement_areas": []
    }'::jsonb,
    
    -- Chất lượng dữ liệu
    "data_quality" JSONB NOT NULL DEFAULT '{
        "completeness_score": 0,
        "reliability_score": 0,
        "sample_size": 0,
        "confidence_intervals": {},
        "data_sources": []
    }'::jsonb,
    
    -- Visualizations metadata
    "visualization_config" JSONB NOT NULL DEFAULT '{
        "charts_generated": [],
        "dashboard_layout": {},
        "export_formats": []
    }'::jsonb,
    
    -- Trạng thái phân tích
    "analysis_status" VARCHAR(20) NOT NULL DEFAULT 'processing',
    
    -- Metadata
    "created_by" INTEGER NOT NULL,
    "processing_time" INTEGER,
    "data_snapshot_date" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    
    -- Cấu hình phân tích
    "analysis_config" JSONB NOT NULL DEFAULT '{}'::jsonb,
    
    -- Tags để phân loại
    "tags" JSONB NOT NULL DEFAULT '[]'::jsonb,
    
    -- Ghi chú
    "notes" TEXT,
    
    -- Timestamps
    "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT "LearningAnalytics_analysis_type_check" CHECK ("analysis_type" IN ('program_overview', 'subject_analysis', 'student_cohort', 'temporal_analysis', 'comparative_analysis')),
    CONSTRAINT "LearningAnalytics_status_check" CHECK ("analysis_status" IN ('processing', 'completed', 'error', 'archived')),
    CONSTRAINT "LearningAnalytics_program_fk" FOREIGN KEY ("program_id") REFERENCES "Programs"("program_id") ON DELETE CASCADE,
    CONSTRAINT "LearningAnalytics_subject_fk" FOREIGN KEY ("subject_id") REFERENCES "Subjects"("subject_id") ON DELETE CASCADE,
    CONSTRAINT "LearningAnalytics_creator_fk" FOREIGN KEY ("created_by") REFERENCES "Users"("user_id") ON DELETE CASCADE
);

-- =====================================================
-- CREATE INDEXES FOR PERFORMANCE OPTIMIZATION
-- =====================================================

-- Indexes cho StudentProgramProgress
CREATE INDEX IF NOT EXISTS "idx_student_program_progress_user_program"
ON "StudentProgramProgress" ("user_id", "program_id");

CREATE INDEX IF NOT EXISTS "idx_student_program_progress_status"
ON "StudentProgramProgress" ("student_status", "last_updated");

CREATE INDEX IF NOT EXISTS "idx_student_program_progress_gpa"
ON "StudentProgramProgress" ("program_id")
WHERE (("overall_progress"->>'gpa')::float > 0);

-- Indexes cho SubjectOutcomeAnalysis
CREATE INDEX IF NOT EXISTS "idx_subject_outcome_analysis_semester"
ON "SubjectOutcomeAnalysis" ("program_id", "analysis_semester", "academic_year");

CREATE INDEX IF NOT EXISTS "idx_subject_outcome_analysis_status"
ON "SubjectOutcomeAnalysis" ("analysis_status", "analysis_date");

CREATE INDEX IF NOT EXISTS "idx_subject_outcome_analysis_subject"
ON "SubjectOutcomeAnalysis" ("subject_id", "analysis_date");

-- Indexes cho ProgramOutcomeTracking
CREATE INDEX IF NOT EXISTS "idx_program_outcome_tracking_active"
ON "ProgramOutcomeTracking" ("program_id", "outcome_type", "is_active");

CREATE INDEX IF NOT EXISTS "idx_program_outcome_tracking_user_program"
ON "ProgramOutcomeTracking" ("user_id", "program_id", "outcome_type")
WHERE "is_active" = true;

CREATE INDEX IF NOT EXISTS "idx_program_outcome_tracking_score"
ON "ProgramOutcomeTracking" ("user_id", "current_score")
WHERE "is_active" = true;

CREATE INDEX IF NOT EXISTS "idx_program_outcome_tracking_status"
ON "ProgramOutcomeTracking" ("achievement_status", "last_updated");

-- Indexes cho LearningAnalytics
CREATE INDEX IF NOT EXISTS "idx_learning_analytics_type_status"
ON "LearningAnalytics" ("analysis_type", "analysis_status");

CREATE INDEX IF NOT EXISTS "idx_learning_analytics_program_date"
ON "LearningAnalytics" ("program_id", "data_snapshot_date");

CREATE INDEX IF NOT EXISTS "idx_learning_analytics_creator"
ON "LearningAnalytics" ("created_by", "createdAt");

-- =====================================================
-- CREATE GIN INDEXES FOR JSON FIELDS
-- =====================================================

-- GIN indexes cho StudentProgramProgress JSON fields
CREATE INDEX IF NOT EXISTS "idx_student_progress_po_gin"
ON "StudentProgramProgress" USING GIN ("po_progress");

CREATE INDEX IF NOT EXISTS "idx_student_progress_plo_gin"
ON "StudentProgramProgress" USING GIN ("plo_progress");

CREATE INDEX IF NOT EXISTS "idx_student_progress_overall_gin"
ON "StudentProgramProgress" USING GIN ("overall_progress");

-- GIN indexes cho ProgramOutcomeTracking JSON fields
CREATE INDEX IF NOT EXISTS "idx_outcome_tracking_history_gin"
ON "ProgramOutcomeTracking" USING GIN ("score_history");

CREATE INDEX IF NOT EXISTS "idx_outcome_tracking_analysis_gin"
ON "ProgramOutcomeTracking" USING GIN ("detailed_analysis");

-- GIN indexes cho LearningAnalytics JSON fields
CREATE INDEX IF NOT EXISTS "idx_learning_analytics_overview_gin"
ON "LearningAnalytics" USING GIN ("overview_metrics");

CREATE INDEX IF NOT EXISTS "idx_learning_analytics_outcomes_gin"
ON "LearningAnalytics" USING GIN ("outcome_analysis");

-- GIN indexes cho SubjectOutcomeAnalysis JSON fields
CREATE INDEX IF NOT EXISTS "idx_subject_analysis_statistics_gin"
ON "SubjectOutcomeAnalysis" USING GIN ("subject_statistics");

CREATE INDEX IF NOT EXISTS "idx_subject_analysis_lo_gin"
ON "SubjectOutcomeAnalysis" USING GIN ("lo_performance");

-- =====================================================
-- CREATE COMMENTS FOR DOCUMENTATION
-- =====================================================

COMMENT ON TABLE "StudentProgramProgress" IS 'Theo dõi tiến độ sinh viên trong toàn bộ chương trình đào tạo';
COMMENT ON TABLE "SubjectOutcomeAnalysis" IS 'Phân tích chuẩn đầu ra theo từng môn học';
COMMENT ON TABLE "ProgramOutcomeTracking" IS 'Theo dõi việc đạt được các PO/PLO của sinh viên qua thời gian';
COMMENT ON TABLE "LearningAnalytics" IS 'Tổng hợp dữ liệu phân tích học tập đa chiều';

-- Column comments for StudentProgramProgress
COMMENT ON COLUMN "StudentProgramProgress"."po_progress" IS 'Tiến độ theo từng PO: {po_id: {average_score, completion_rate, subjects_count}}';
COMMENT ON COLUMN "StudentProgramProgress"."plo_progress" IS 'Tiến độ theo từng PLO: {plo_id: {average_score, mastery_level, assessment_count}}';
COMMENT ON COLUMN "StudentProgramProgress"."semester_progress" IS 'Tiến độ theo học kỳ: {semester: {subjects, gpa, credits}}';

-- Column comments for ProgramOutcomeTracking
COMMENT ON COLUMN "ProgramOutcomeTracking"."score_history" IS 'Array of {date, score, source_subject_id, assessment_type}';
COMMENT ON COLUMN "ProgramOutcomeTracking"."evidence_count" IS 'Số lượng bằng chứng đánh giá';
COMMENT ON COLUMN "ProgramOutcomeTracking"."program_weight" IS 'Trọng số của PO/PLO này trong chương trình';

-- Column comments for SubjectOutcomeAnalysis
COMMENT ON COLUMN "SubjectOutcomeAnalysis"."po_achievement" IS 'Tỷ lệ đạt PO: {po_id: {target_score, actual_average, achievement_rate, student_count}}';
COMMENT ON COLUMN "SubjectOutcomeAnalysis"."plo_achievement" IS 'Tỷ lệ đạt PLO: {plo_id: {target_score, actual_average, achievement_rate, mastery_distribution}}';
COMMENT ON COLUMN "SubjectOutcomeAnalysis"."lo_performance" IS 'Hiệu suất theo LO: {lo_id: {average_score, difficulty_level, question_count, correct_rate}}';

-- Column comments for LearningAnalytics
COMMENT ON COLUMN "LearningAnalytics"."time_period" IS '{start_date, end_date, semester, academic_year}';
COMMENT ON COLUMN "LearningAnalytics"."analysis_config" IS 'Cấu hình parameters cho phân tích';
COMMENT ON COLUMN "LearningAnalytics"."tags" IS 'Tags để dễ tìm kiếm và phân loại';
COMMENT ON COLUMN "LearningAnalytics"."processing_time" IS 'Thời gian xử lý tính bằng milliseconds';
COMMENT ON COLUMN "LearningAnalytics"."data_snapshot_date" IS 'Thời điểm snapshot dữ liệu để phân tích';

-- =====================================================
-- VERIFY TABLES CREATED
-- =====================================================

-- Check if all tables were created successfully
DO $$
DECLARE
    table_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO table_count
    FROM information_schema.tables
    WHERE table_schema = 'public'
    AND table_name IN ('StudentProgramProgress', 'SubjectOutcomeAnalysis', 'ProgramOutcomeTracking', 'LearningAnalytics');

    IF table_count = 4 THEN
        RAISE NOTICE '✅ All 4 analytics tables created successfully!';
    ELSE
        RAISE NOTICE '❌ Only % out of 4 tables were created', table_count;
    END IF;
END $$;
