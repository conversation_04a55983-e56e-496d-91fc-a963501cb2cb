import pandas as pd
import csv
import re


# Đọc file Excel
def preprocess_excel_to_csv(input_file, output_file):
    # Đọc file Excel (sheet đầu tiên)
    df = pd.read_excel(input_file, header=None)

    # Danh sách để lưu dữ liệu đã xử lý
    processed_data = []
    current_kqht = None

    # Bắt đầu xử lý từ dòng thứ 2 (index 1 trong pandas)
    i = 1
    while i < len(df):
        # Kiểm tra nếu dòng hiện tại là tiêu đề KQHT (ví dụ: "KQHT 1:")
        if isinstance(df.iloc[i, 0], str) and "KQHT" in df.iloc[i, 0]:
            # Lấy giá trị KQHT từ tiêu đề (ví dụ: "KQHT 1:" -> 1)
            match = re.search(r"KQHT (\d+)", df.iloc[i, 0])
            if match:
                current_kqht = int(match.group(1))
            i += 1  # Bỏ qua dòng tiêu đề KQHT
            continue

        # <PERSON><PERSON><PERSON> tra nếu dòng hiện tại là dòng tiêu đề cột (<PERSON><PERSON><PERSON> độ, TT, Nội dung câu hỏi, ...)
        if df.iloc[i, 0] == "Mức độ":
            i += 1  # Bỏ qua dòng tiêu đề cột
            continue

        # Xử lý nhóm 4 dòng cho 1 câu hỏi
        if i + 3 < len(df):  # Đảm bảo có đủ 4 dòng để xử lý
            # Lấy dữ liệu từ dòng chính (Mức độ, TT, Nội dung câu hỏi, Đáp án, Hướng dẫn trả lời)
            row = df.iloc[i]
            level = row[0]  # Mức độ
            question_text = row[2]  # Nội dung câu hỏi
            correct_answer = row[4]  # Đáp án
            explanation = row[5]  # Hướng dẫn trả lời

            # Lấy các phương án A, B, C, D từ 4 dòng tiếp theo
            option_a = df.iloc[i, 1]  # Dòng i: Phương án A
            option_b = df.iloc[i + 1, 1]  # Dòng i+1: Phương án B
            option_c = df.iloc[i + 2, 1]  # Dòng i+2: Phương án C
            option_d = df.iloc[i + 3, 1]  # Dòng i+3: Phương án D

            # Loại bỏ tiền tố "A.", "B.", "C.", "D." trong các phương án
            if isinstance(option_a, str):
                option_a = re.sub(r"^[A-D]\.\s*", "", option_a).strip()
            if isinstance(option_b, str):
                option_b = re.sub(r"^[A-D]\.\s*", "", option_b).strip()
            if isinstance(option_c, str):
                option_c = re.sub(r"^[A-D]\.\s*", "", option_c).strip()
            if isinstance(option_d, str):
                option_d = re.sub(r"^[A-D]\.\s*", "", option_d).strip()

            # Thêm dữ liệu vào danh sách
            processed_data.append(
                {
                    "KQHT": current_kqht,
                    "Mức độ": level,
                    "Nội dung câu hỏi": question_text,
                    "A": option_a,
                    "B": option_b,
                    "C": option_c,
                    "D": option_d,
                    "Đáp án": correct_answer,
                    "Hướng dẫn trả lời": explanation if pd.notna(explanation) else "",
                }
            )

            i += 4  # Nhảy qua 4 dòng để xử lý câu hỏi tiếp theo
        else:
            break

    # Ghi dữ liệu vào file CSV
    headers = [
        "KQHT",
        "Mức độ",
        "Nội dung câu hỏi",
        "A",
        "B",
        "C",
        "D",
        "Đáp án",
        "Hướng dẫn trả lời",
    ]
    with open(output_file, mode="w", newline="", encoding="utf-8") as file:
        writer = csv.DictWriter(file, fieldnames=headers)
        writer.writeheader()
        for row in processed_data:
            writer.writerow(row)

    print(f"Đã chuyển đổi thành công file Excel thành CSV: {output_file}")


# Sử dụng tool
if __name__ == "__main__":
    input_file = "tkw_questions.xlsx"  # Đường dẫn tới file Excel
    output_file = "processed_questions.csv"  # Tên file CSV đầu ra
    preprocess_excel_to_csv(input_file, output_file)
