# Radar Chart API Documentation

## Tổng quan
API Radar Chart cung cấp dữ liệu để hiển thị biểu đồ radar cho kết quả quiz, bao gồm:
- <PERSON><PERSON> tích theo mức độ khó (d<PERSON>, trung bình, kh<PERSON>)
- <PERSON><PERSON> tích theo Learning Outcomes (LO)
- <PERSON><PERSON><PERSON> metrics hiệu suất tổng thể

## Cấu trúc dữ liệu trả về

### Radar Data Structure
```json
{
  "difficulty_levels": {
    "easy": {
      "accuracy": 85,
      "questions_count": 5,
      "average_response_time": 12000
    },
    "medium": {
      "accuracy": 70,
      "questions_count": 3,
      "average_response_time": 18000
    },
    "hard": {
      "accuracy": 60,
      "questions_count": 2,
      "average_response_time": 25000
    }
  },
  "learning_outcomes": {
    "LO1 - Hiểu biết cơ bản": {
      "accuracy": 80,
      "questions_count": 4,
      "average_response_time": 15000
    },
    "LO2 - Áp dụng kiến thức": {
      "accuracy": 75,
      "questions_count": 6,
      "average_response_time": 20000
    }
  },
  "performance_metrics": {
    "average_response_time": 17500,
    "completion_rate": 100,
    "first_attempt_accuracy": 75,
    "overall_accuracy": 78
  }
}
```

## API Endpoints

### 1. API Tổng hợp - Lấy tất cả dữ liệu radar
```
GET /api/quiz-results/quiz/:quizId/radar/all
```

**Quyền truy cập:** Admin, Teacher, Student

**Response:**
```json
{
  "quiz_id": 1,
  "quiz_name": "Quiz Test",
  "total_questions": 10,
  "radar_data": {
    "current_user": {
      "user_id": 1,
      "data": {
        "difficulty_levels": {
          "easy": {
            "accuracy": 80,
            "questions_count": 3,
            "average_response_time": 15000
          },
          "medium": {
            "accuracy": 70,
            "questions_count": 4,
            "average_response_time": 20000
          },
          "hard": {
            "accuracy": 60,
            "questions_count": 3,
            "average_response_time": 25000
          }
        },
        "learning_outcomes": {
          "LO1": {
            "accuracy": 75,
            "questions_count": 5,
            "average_response_time": 18000
          }
        },
        "performance_metrics": {
          "average_response_time": 20000,
          "completion_rate": 100,
          "first_attempt_accuracy": 70,
          "overall_accuracy": 70
        }
      }
    },
    "average": {
      // Dữ liệu trung bình của tất cả người tham gia
    },
    "top_performer": {
      "user_info": {
        "user_id": 2,
        "name": "Top Student",
        "score": 90
      },
      "data": {
        // Dữ liệu của người xếp hạng 1
      }
    }
  },
  "summary": {
    "total_participants": 25,
    "total_answers": 250,
    "difficulty_levels": ["easy", "medium", "hard"],
    "learning_outcomes": ["LO1", "LO2", "LO3"]
  }
}
```

### 2. API Dữ liệu người dùng hiện tại
```
GET /api/quiz-results/quiz/:quizId/radar/current-user
```

**Quyền truy cập:** Student (chỉ xem dữ liệu của mình)

**Response:**
```json
{
  "user_id": 1,
  "quiz_id": 1,
  "radar_data": {
    "difficulty_levels": {
      "easy": {
        "accuracy": 80,
        "questions_count": 3,
        "average_response_time": 15000
      }
    },
    "learning_outcomes": {
      "LO1": {
        "accuracy": 75,
        "questions_count": 5,
        "average_response_time": 18000
      }
    },
    "performance_metrics": {
      "average_response_time": 20000,
      "completion_rate": 100,
      "first_attempt_accuracy": 70,
      "overall_accuracy": 70
    }
  }
}
```

### 3. API Dữ liệu trung bình
```
GET /api/quiz-results/quiz/:quizId/radar/average
```

**Quyền truy cập:** Admin, Teacher

**Response:**
```json
{
  "quiz_id": 1,
  "radar_data": {
    "difficulty_levels": {
      "easy": {
        "accuracy": 75,
        "questions_count": 3,
        "average_response_time": 16000
      }
    },
    "learning_outcomes": {
      "LO1": {
        "accuracy": 70,
        "questions_count": 5,
        "average_response_time": 19000
      }
    },
    "performance_metrics": {
      "average_response_time": 21000,
      "completion_rate": 100,
      "first_attempt_accuracy": 65,
      "overall_accuracy": 65
    }
  }
}
```

### 4. API Dữ liệu top performer
```
GET /api/quiz-results/quiz/:quizId/radar/top-performer
```

**Quyền truy cập:** Admin, Teacher

**Response:**
```json
{
  "quiz_id": 1,
  "top_performer": {
    "user_id": 2,
    "name": "Top Student",
    "score": 90
  },
  "radar_data": {
    "difficulty_levels": {
      "easy": {
        "accuracy": 100,
        "questions_count": 3,
        "average_response_time": 12000
      }
    },
    "learning_outcomes": {
      "LO1": {
        "accuracy": 90,
        "questions_count": 5,
        "average_response_time": 15000
      }
    },
    "performance_metrics": {
      "average_response_time": 15000,
      "completion_rate": 100,
      "first_attempt_accuracy": 90,
      "overall_accuracy": 90
    }
  }
}
```

## Cách sử dụng với Frontend

### 1. Sử dụng Chart.js
```javascript
import Chart from 'chart.js/auto';

async function loadRadarData(quizId) {
  const response = await fetch(`/api/quiz-results/quiz/${quizId}/radar/all`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  const data = await response.json();
  
  const ctx = document.getElementById('radarChart').getContext('2d');
  new Chart(ctx, {
    type: 'radar',
    data: {
      labels: ['Easy', 'Medium', 'Hard', 'LO1', 'LO2', 'LO3'],
      datasets: [
        {
          label: 'Current User',
          data: [
            data.radar_data.current_user.data.difficulty_levels.easy.accuracy,
            data.radar_data.current_user.data.difficulty_levels.medium.accuracy,
            data.radar_data.current_user.data.difficulty_levels.hard.accuracy,
            data.radar_data.current_user.data.learning_outcomes.LO1.accuracy,
            data.radar_data.current_user.data.learning_outcomes.LO2.accuracy,
            data.radar_data.current_user.data.learning_outcomes.LO3.accuracy
          ],
          borderColor: 'rgb(255, 99, 132)',
          backgroundColor: 'rgba(255, 99, 132, 0.2)'
        },
        {
          label: 'Average',
          data: [
            data.radar_data.average.difficulty_levels.easy.accuracy,
            data.radar_data.average.difficulty_levels.medium.accuracy,
            data.radar_data.average.difficulty_levels.hard.accuracy,
            data.radar_data.average.learning_outcomes.LO1.accuracy,
            data.radar_data.average.learning_outcomes.LO2.accuracy,
            data.radar_data.average.learning_outcomes.LO3.accuracy
          ],
          borderColor: 'rgb(54, 162, 235)',
          backgroundColor: 'rgba(54, 162, 235, 0.2)'
        }
      ]
    },
    options: {
      scales: {
        r: {
          beginAtZero: true,
          max: 100
        }
      }
    }
  });
}
```

### 2. Sử dụng Recharts (React)
```jsx
import { RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar, Legend } from 'recharts';

function RadarChartComponent({ quizId }) {
  const [data, setData] = useState(null);

  useEffect(() => {
    async function fetchData() {
      const response = await fetch(`/api/quiz-results/quiz/${quizId}/radar/all`);
      const result = await response.json();
      setData(result);
    }
    fetchData();
  }, [quizId]);

  if (!data) return <div>Loading...</div>;

  const chartData = [
    {
      subject: 'Easy',
      'Current User': data.radar_data.current_user.data.difficulty_levels.easy.accuracy,
      'Average': data.radar_data.average.difficulty_levels.easy.accuracy,
      'Top Performer': data.radar_data.top_performer.data.difficulty_levels.easy.accuracy,
    },
    {
      subject: 'Medium',
      'Current User': data.radar_data.current_user.data.difficulty_levels.medium.accuracy,
      'Average': data.radar_data.average.difficulty_levels.medium.accuracy,
      'Top Performer': data.radar_data.top_performer.data.difficulty_levels.medium.accuracy,
    },
    // ... thêm các mục khác
  ];

  return (
    <RadarChart width={500} height={300} data={chartData}>
      <PolarGrid />
      <PolarAngleAxis dataKey="subject" />
      <PolarRadiusAxis angle={30} domain={[0, 100]} />
      <Radar name="Current User" dataKey="Current User" stroke="#8884d8" fill="#8884d8" fillOpacity={0.6} />
      <Radar name="Average" dataKey="Average" stroke="#82ca9d" fill="#82ca9d" fillOpacity={0.6} />
      <Radar name="Top Performer" dataKey="Top Performer" stroke="#ffc658" fill="#ffc658" fillOpacity={0.6} />
      <Legend />
    </RadarChart>
  );
}
```

## Test API

### Sử dụng Postman

1. **Đăng nhập để lấy token:**
```
POST /api/users/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "123456"
}
```

2. **Test API radar chart:**
```
GET /api/quiz-results/quiz/1/radar/all
Authorization: Bearer <token>
```

### Sử dụng script test
```bash
cd backend
node test_radar_api.js
```

## Lưu ý

1. **Quyền truy cập:** Mỗi API có quyền truy cập khác nhau tùy theo vai trò người dùng
2. **Dữ liệu:** API chỉ trả về dữ liệu khi có người tham gia quiz và có lịch sử trả lời câu hỏi
3. **Performance:** API sử dụng cache để tối ưu hiệu suất
4. **Error handling:** Tất cả API đều có xử lý lỗi và trả về thông báo lỗi chi tiết

## Troubleshooting

### Lỗi thường gặp:

1. **401 Unauthorized:** Token không hợp lệ hoặc hết hạn
2. **403 Forbidden:** Không có quyền truy cập API
3. **404 Not Found:** Quiz không tồn tại
4. **500 Internal Server Error:** Lỗi server, kiểm tra logs

### Debug:
- Kiểm tra token trong header Authorization
- Kiểm tra quiz_id có tồn tại không
- Kiểm tra vai trò người dùng có phù hợp không
- Kiểm tra logs server để xem lỗi chi tiết