'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
    class Question extends Model {
        static associate(models) {
            Question.belongsTo(models.QuestionType, { foreignKey: 'question_type_id' });
            Question.belongsTo(models.Level, { foreignKey: 'level_id' });
            Question.belongsTo(models.LO, { foreignKey: 'lo_id', as: 'LO' });
            Question.belongsToMany(models.Quiz, { through: models.QuizQuestion, foreignKey: 'question_id' });
            Question.hasMany(models.Answer, { foreignKey: 'question_id' });
        }
    }

    Question.init(
        {
            question_id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true,
                autoIncrementIdentity: true
            },
            question_type_id: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: 'QuestionTypes',
                    key: 'question_type_id',
                },
            },
            level_id: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: 'Levels',
                    key: 'level_id',
                },
            },
            question_text: {
                type: DataTypes.TEXT,
                allowNull: false,
            },
            lo_id: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: 'LOs',
                    key: 'lo_id',
                },
            },
            explanation: { // Thêm cột explanation
                type: DataTypes.TEXT,
                allowNull: true, // Có thể để null nếu không có giải thích
            },
        },
        {
            sequelize,
            modelName: 'Question',
            tableName: 'Questions',
        }
    );

    return Question;
};