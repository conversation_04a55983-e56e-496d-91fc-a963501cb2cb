-- Migration SQL cho Adaptive Quiz Generation
-- Thêm trường adaptive_config vào bảng Quizzes

-- 1. <PERSON>h<PERSON><PERSON> cột adaptive_config và<PERSON> bảng Quizzes
ALTER TABLE "Quizzes" 
ADD COLUMN "adaptive_config" JSONB NULL;

-- 2. <PERSON><PERSON><PERSON><PERSON> comment cho cột mới
COMMENT ON COLUMN "Quizzes"."adaptive_config" IS 'Configuration for adaptive quiz generation including user analysis, weak areas, and question distribution';

-- 3. Tạo index cho JSONB column để tăng performance khi query
CREATE INDEX idx_quizzes_adaptive_config_user_id 
ON "Quizzes" USING GIN (("adaptive_config"->>'user_id'));

CREATE INDEX idx_quizzes_adaptive_config_focus_mode 
ON "Quizzes" USING GIN (("adaptive_config"->>'focus_mode'));

-- 4. Tạo index cho việc tì<PERSON> kiếm quiz thích ứng
CREATE INDEX idx_quizzes_adaptive_config_not_null 
ON "Quizzes" ("quiz_id") 
WHERE "adaptive_config" IS NOT NULL;

-- 5. Thêm constraint để validate JSON structure (optional)
ALTER TABLE "Quizzes" 
ADD CONSTRAINT check_adaptive_config_structure 
CHECK (
    "adaptive_config" IS NULL OR (
        "adaptive_config" ? 'user_id' AND
        "adaptive_config" ? 'focus_mode' AND
        "adaptive_config" ? 'generated_at'
    )
);

-- 6. Tạo function để lấy quiz thích ứng của user
CREATE OR REPLACE FUNCTION get_adaptive_quizzes_by_user(user_id_param INTEGER)
RETURNS TABLE (
    quiz_id INTEGER,
    name VARCHAR,
    subject_id INTEGER,
    duration INTEGER,
    status VARCHAR,
    pin VARCHAR,
    focus_mode VARCHAR,
    generated_at TIMESTAMP,
    weak_areas_count INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        q.quiz_id,
        q.name,
        q.subject_id,
        q.duration,
        q.status,
        q.pin,
        q.adaptive_config->>'focus_mode' as focus_mode,
        (q.adaptive_config->>'generated_at')::TIMESTAMP as generated_at,
        COALESCE(jsonb_array_length(q.adaptive_config->'weak_areas_identified'), 0) as weak_areas_count
    FROM "Quizzes" q
    WHERE q.adaptive_config IS NOT NULL
    AND (q.adaptive_config->>'user_id')::INTEGER = user_id_param
    ORDER BY (q.adaptive_config->>'generated_at')::TIMESTAMP DESC;
END;
$$ LANGUAGE plpgsql;

-- 7. Tạo function để thống kê adaptive quiz
CREATE OR REPLACE FUNCTION get_adaptive_quiz_statistics()
RETURNS TABLE (
    total_adaptive_quizzes BIGINT,
    focus_mode_distribution JSONB,
    avg_weak_areas_per_quiz NUMERIC,
    most_common_weak_levels JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) as total_adaptive_quizzes,
        jsonb_object_agg(
            focus_mode, 
            focus_count
        ) as focus_mode_distribution,
        AVG(COALESCE(jsonb_array_length(adaptive_config->'weak_areas_identified'), 0)) as avg_weak_areas_per_quiz,
        jsonb_object_agg(
            weak_level,
            level_count
        ) as most_common_weak_levels
    FROM (
        SELECT 
            adaptive_config->>'focus_mode' as focus_mode,
            COUNT(*) as focus_count
        FROM "Quizzes" 
        WHERE adaptive_config IS NOT NULL
        GROUP BY adaptive_config->>'focus_mode'
    ) focus_stats,
    (
        SELECT 
            weak_area->>'name' as weak_level,
            COUNT(*) as level_count
        FROM "Quizzes",
        jsonb_array_elements(adaptive_config->'weak_areas_identified') as weak_area
        WHERE adaptive_config IS NOT NULL
        AND weak_area->>'type' = 'level'
        GROUP BY weak_area->>'name'
        ORDER BY COUNT(*) DESC
        LIMIT 5
    ) level_stats
    GROUP BY weak_level, level_count;
END;
$$ LANGUAGE plpgsql;

-- 8. Tạo view để dễ dàng query adaptive quiz info
CREATE OR REPLACE VIEW adaptive_quiz_summary AS
SELECT 
    q.quiz_id,
    q.name as quiz_name,
    q.subject_id,
    s.name as subject_name,
    q.duration,
    q.status,
    q.pin,
    (q.adaptive_config->>'user_id')::INTEGER as target_user_id,
    q.adaptive_config->>'focus_mode' as focus_mode,
    (q.adaptive_config->>'generated_at')::TIMESTAMP as generated_at,
    COALESCE(jsonb_array_length(q.adaptive_config->'weak_areas_identified'), 0) as weak_areas_count,
    q.adaptive_config->'question_distribution'->>'total_questions' as total_questions,
    q.adaptive_config->'target_user'->>'user_name' as target_user_name
FROM "Quizzes" q
LEFT JOIN "Subjects" s ON q.subject_id = s.subject_id
WHERE q.adaptive_config IS NOT NULL;

-- 9. Tạo trigger để log adaptive quiz creation
CREATE OR REPLACE FUNCTION log_adaptive_quiz_creation()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.adaptive_config IS NOT NULL AND OLD.adaptive_config IS NULL THEN
        INSERT INTO quiz_logs (quiz_id, action, details, created_at)
        VALUES (
            NEW.quiz_id,
            'adaptive_quiz_created',
            jsonb_build_object(
                'user_id', NEW.adaptive_config->>'user_id',
                'focus_mode', NEW.adaptive_config->>'focus_mode',
                'weak_areas_count', COALESCE(jsonb_array_length(NEW.adaptive_config->'weak_areas_identified'), 0)
            ),
            NOW()
        );
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Tạo bảng quiz_logs nếu chưa có
CREATE TABLE IF NOT EXISTS quiz_logs (
    log_id SERIAL PRIMARY KEY,
    quiz_id INTEGER REFERENCES "Quizzes"(quiz_id),
    action VARCHAR(50) NOT NULL,
    details JSONB,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Tạo trigger
DROP TRIGGER IF EXISTS trigger_log_adaptive_quiz ON "Quizzes";
CREATE TRIGGER trigger_log_adaptive_quiz
    AFTER UPDATE ON "Quizzes"
    FOR EACH ROW
    EXECUTE FUNCTION log_adaptive_quiz_creation();

-- 10. Tạo index cho performance
CREATE INDEX IF NOT EXISTS idx_quiz_logs_quiz_id ON quiz_logs(quiz_id);
CREATE INDEX IF NOT EXISTS idx_quiz_logs_action ON quiz_logs(action);
CREATE INDEX IF NOT EXISTS idx_quiz_logs_created_at ON quiz_logs(created_at);

-- 11. Sample data để test (optional)
-- INSERT INTO "Quizzes" (subject_id, name, duration, status, pin, adaptive_config, update_time)
-- VALUES (
--     1,
--     'Quiz Thích Ứng - Test',
--     30,
--     'pending',
--     'TEST01',
--     '{
--         "user_id": 123,
--         "focus_mode": "weak_areas",
--         "difficulty_adjustment": "auto",
--         "weak_areas_identified": [
--             {
--                 "type": "level",
--                 "name": "Hard",
--                 "accuracy": 35,
--                 "priority": "high"
--             },
--             {
--                 "type": "lo",
--                 "name": "LO3.2",
--                 "accuracy": 42,
--                 "priority": "high"
--             }
--         ],
--         "question_distribution": {
--             "total_questions": 20,
--             "by_difficulty": {
--                 "easy": 4,
--                 "medium": 10,
--                 "hard": 6
--             },
--             "by_priority": {
--                 "weak_areas": 12,
--                 "medium_areas": 5,
--                 "strong_areas": 3
--             }
--         },
--         "generated_at": "2024-01-15T10:30:00.000Z",
--         "target_user": {
--             "user_id": 123,
--             "user_name": "Nguyễn Văn A"
--         }
--     }'::jsonb,
--     NOW()
-- );

-- 12. Queries hữu ích để kiểm tra

-- Kiểm tra adaptive quiz đã tạo
-- SELECT * FROM adaptive_quiz_summary ORDER BY generated_at DESC;

-- Lấy adaptive quiz của user cụ thể
-- SELECT * FROM get_adaptive_quizzes_by_user(123);

-- Thống kê adaptive quiz
-- SELECT * FROM get_adaptive_quiz_statistics();

-- Tìm quiz có focus_mode = 'weak_areas'
-- SELECT quiz_id, name, adaptive_config->>'user_id' as user_id
-- FROM "Quizzes" 
-- WHERE adaptive_config->>'focus_mode' = 'weak_areas';

-- Tìm quiz có weak areas về 'Hard' level
-- SELECT quiz_id, name
-- FROM "Quizzes",
-- jsonb_array_elements(adaptive_config->'weak_areas_identified') as weak_area
-- WHERE weak_area->>'type' = 'level' 
-- AND weak_area->>'name' = 'Hard';

COMMIT;
