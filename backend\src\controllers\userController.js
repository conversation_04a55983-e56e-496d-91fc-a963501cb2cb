const { User, Role, Course, StudentCourse, QuizResult, CourseResult } = require('../models');
const jwt = require('jsonwebtoken')
const XLSX = require('xlsx');
const { Op, literal } = require('sequelize');
// L<PERSON>y danh sách tất cả người dùng
exports.getAllUsers = async (req, res) => {
    try {
        const { page = 1, limit = 10 } = req.query;
        const offset = (page - 1) * limit;

        const users = await User.findAndCountAll({
            limit: parseInt(limit),
            offset: parseInt(offset),
            include: [
                { model: Role, attributes: ['role_id', 'name'] },
                { model: Course, attributes: ['course_id', 'name'] },
                { model: QuizResult, attributes: ['result_id', 'score'] },
                { model: CourseResult, attributes: ['result_id', 'average_score'] },
            ],
        });

        res.status(200).json({
            totalItems: users.count,
            totalPages: <PERSON>.ceil(users.count / limit),
            currentPage: parseInt(page),
            users: users.rows,
        });
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi lấy danh sách người dùng', error: error.message });
    }
};

// Lấy thông tin chi tiết một người dùng
exports.getUserById = async (req, res) => {
    try {
        const user = await User.findByPk(req.params.id, {
            include: [
                { model: Role, attributes: ['role_id', 'name'] },
                { model: Course, attributes: ['course_id', 'name'] },
                { model: QuizResult, attributes: ['result_id', 'score'] },
                { model: CourseResult, attributes: ['result_id', 'average_score'] },
            ],
        });

        if (!user) {
            return res.status(404).json({ message: 'Người dùng không tồn tại' });
        }

        res.status(200).json(user);
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi lấy thông tin người dùng', error: error.message });
    }
};


// Cập nhật thông tin một người dùng
exports.updateUser = async (req, res) => {
    try {
        const { user_id } = req.params;
        const { name, email, password } = req.body;

        const user = await req.models.User.findByPk(user_id, {
            include: [{ model: req.models.Role, as: 'Role' }],
        });
        if (!user) {
            return res.status(404).json({ error: 'Người dùng không tồn tại' });
        }

        // Kiểm tra quyền
        if (req.roleName === 'student' && req.user.user_id !== parseInt(user_id)) {
            return res.status(403).json({ error: 'Bạn chỉ có thể cập nhật thông tin của chính mình' });
        }
        if (req.roleName === 'teacher' && user.Role.name !== 'student') {
            return res.status(403).json({ error: 'Giảng viên chỉ có thể cập nhật thông tin của học viên' });
        }

        if (name) user.name = name;
        if (email && email !== user.email) {
            const existingEmail = await req.models.User.findOne({ where: { email } });
            if (existingEmail) {
                return res.status(400).json({ error: 'Email đã tồn tại' });
            }
            user.email = email;
        }
        if (password) user.password = password;

        await user.save();

        res.status(200).json({
            message: 'Cập nhật người dùng thành công',
            user: {
                user_id: user.user_id,
                name: user.name,
                email: user.email,
                role: user.Role.name,
            },
        });
    } catch (error) {
        res.status(500).json({ error: 'Lỗi khi cập nhật người dùng', details: error.message });
    }
};

// Xóa một người dùng
exports.deleteUser = async (req, res) => {
    try {
        const user = await User.findByPk(req.params.id);
        if (!user) {
            return res.status(404).json({ message: 'Người dùng không tồn tại' });
        }

        await user.destroy();
        res.status(200).json({ message: 'Xóa người dùng thành công' });
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi xóa người dùng', error: error.message });
    }
};

require('dotenv').config();
exports.login = async (req, res) => {
    try {
        const { email, password } = req.body;

        if (!email || !password) {
            return res.status(400).json({ error: 'Vui lòng cung cấp email và password' });
        }

        const user = await User.findOne({
            where: { email },
            include: [{ model: Role, as: 'Role' }],
        });
        if (!user) {
            return res.status(404).json({ error: 'Email không tồn tại' });
        }

        const isMatch = await user.comparePassword(password);
        if (!isMatch) {
            return res.status(401).json({ error: 'Mật khẩu không đúng' });
        }

        if (!process.env.JWT_SECRET) {
            throw new Error('JWT_SECRET không được thiết lập trong biến môi trường');
        }

        const token = jwt.sign(
            { user_id: user.user_id, role: user.Role.name }, // Lưu vai trò vào token
            process.env.JWT_SECRET,
            { expiresIn: '1h' }
        );

        res.status(200).json({
            token,
            user: {
                user_id: user.user_id,
                name: user.name,
                email: user.email,
                role: user.Role.name,
            },
        });
    } catch (error) {
        res.status(500).json({ error: 'Lỗi khi đăng nhập', details: error.message });
    }
};

exports.createAdmin = async (req, res) => {
    try {
        const { name, email, password } = req.body;

        if (!name || !email || !password) {
            return res.status(400).json({ error: 'Vui lòng cung cấp đầy đủ thông tin' });
        }

        const existingUser = await User.findOne({ where: { email } });
        if (existingUser) {
            return res.status(400).json({ error: 'Email đã tồn tại' });
        }

        const adminRole = await Role.findOne({ where: { name: 'admin' } });
        if (!adminRole) {
            return res.status(500).json({ error: 'Vai trò admin không tồn tại' });
        }

        const admin = await User.create({
            name,
            email,
            password,
            role_id: adminRole.role_id,
        });

        res.status(201).json({
            message: 'Tạo admin thành công',
            user: {
                user_id: admin.user_id,
                name: admin.name,
                email: admin.email,
                role: 'admin',
            },
        });
    } catch (error) {
        res.status(500).json({ error: 'Lỗi khi tạo admin', details: error.message });
    }
};

exports.createTeacher = async (req, res) => {
    try {
        const { name, email, password } = req.body;

        if (!name || !email || !password) {
            return res.status(400).json({ error: 'Vui lòng cung cấp đầy đủ thông tin' });
        }

        const existingUser = await User.findOne({ where: { email } });
        if (existingUser) {
            return res.status(400).json({ error: 'Email đã tồn tại' });
        }

        const teacherRole = await Role.findOne({ where: { name: 'teacher' } });
        if (!teacherRole) {
            return res.status(500).json({ error: 'Vai trò teacher không tồn tại' });
        }

        const teacher = await User.create({
            name,
            email,
            password,
            role_id: teacherRole.role_id,
        });

        res.status(201).json({
            message: 'Tạo giảng viên thành công',
            user: {
                user_id: teacher.user_id,
                name: teacher.name,
                email: teacher.email,
                role: 'teacher',
            },
        });
    } catch (error) {
        res.status(500).json({ error: 'Lỗi khi tạo giảng viên', details: error.message });
    }
};

exports.createStudent = async (req, res) => {
    try {
        const { name, email, password } = req.body;

        if (!name || !email || !password) {
            return res.status(400).json({ error: 'Vui lòng cung cấp đầy đủ thông tin' });
        }

        const existingUser = await User.findOne({ where: { email } });
        if (existingUser) {
            return res.status(400).json({ error: 'Email đã tồn tại' });
        }

        const studentRole = await Role.findOne({ where: { name: 'student' } });
        if (!studentRole) {
            return res.status(500).json({ error: 'Vai trò student không tồn tại' });
        }

        const student = await User.create({
            name,
            email,
            password,
            role_id: studentRole.role_id,
        });

        res.status(201).json({
            message: 'Tạo học viên thành công',
            user: {
                user_id: student.user_id,
                name: student.name,
                email: student.email,
                role: 'student',
            },
        });
    } catch (error) {
        res.status(500).json({ error: 'Lỗi khi tạo học viên', details: error.message });
    }
};

exports.importStudents = async (req, res) => {
    try {
        // Kiểm tra xem có file Excel được tải lên không
        if (!req.file) {
            return res.status(400).json({ error: 'Vui lòng tải lên file Excel' });
        }

        // Tìm vai trò student trong bảng Roles
        const studentRole = await Role.findOne({
            where: { name: { [Op.iLike]: 'student' } },
        });
        if (!studentRole) {
            return res.status(500).json({ error: 'Vai trò student không tồn tại' });
        }

        // Đọc file Excel bằng xlsx
        const workbook = XLSX.read(req.file.buffer, { type: 'buffer' });
        const worksheet = workbook.Sheets[workbook.SheetNames[0]];

        // Chuyển dữ liệu từ worksheet thành mảng JSON
        const rows = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

        const createdStudents = [];
        const skippedStudents = [];

        // Bắt đầu đọc từ hàng 12 (hàng 11 là header, dữ liệu bắt đầu từ hàng 12)
        // rows[0] là hàng 1, rows[10] là hàng 11 (header), nên dữ liệu bắt đầu từ rows[11] (hàng 12)
        for (let i = 11; i < rows.length; i++) {
            const cells = rows[i];

            // Trích xuất dữ liệu từ các cột
            const maSV = cells[1] ? cells[1].toString().trim() : ''; // Cột "Mã SV" (cột 2)
            const hoLot = cells[2] ? cells[2].toString().trim() : ''; // Cột "Họ lót" (cột 3)
            const ten = cells[3] ? cells[3].toString().trim() : ''; // Cột "Tên" (cột 4)
            // Email sẽ được tạo lại từ maSV
            const email = maSV ? `${maSV}@st.tvu.edu.vn` : '';

            // Kiểm tra dữ liệu hợp lệ
            if (!hoLot || !ten || !email || !maSV) {
                skippedStudents.push({
                    maSV,
                    email,
                    reason: 'Thiếu thông tin (họ lót, tên, email hoặc mã SV)',
                });
                continue;
            }

            // Kết hợp Họ lót và Tên để tạo name
            const name = `${hoLot} ${ten}`;

            // Kiểm tra email đã tồn tại chưa
            const existingUser = await User.findOne({ where: { email } });
            if (existingUser) {
                skippedStudents.push({
                    maSV,
                    email,
                    reason: 'Email đã tồn tại',
                });
                continue;
            }

            // Tạo sinh viên mới
            const student = await User.create({
                name,
                email,
                password: maSV, // Mật khẩu là Mã SV, sẽ được mã hóa bởi hook
                role_id: studentRole.role_id,
            });

            createdStudents.push({
                user_id: student.user_id,
                name: student.name,
                email: student.email,
                role: 'student',
            });
        }

        // Trả về kết quả
        res.status(200).json({
            message: 'Import sinh viên thành công',
            created: createdStudents,
            skipped: skippedStudents,
        });
    } catch (error) {
        res.status(500).json({ error: 'Lỗi khi import sinh viên', details: error.message });
    }
};



