const express = require('express');
const router = express.Router();
const questionController = require('../controllers/questionController');
const multer = require('multer');
const upload = multer({ dest: 'uploads/' });
const handleMulterError = require('../middleware/multerMiddleware')
// L<PERSON>y danh sách tất cả câu hỏi
router.get('/', questionController.getAllQuestions);

// Lấy thông tin chi tiết một câu hỏi
// Đặt route cụ thể trước
router.get('/bylos', questionController.getQuestionsByLOs);

// Sau đó mới đến route động
router.get('/:id', questionController.getQuestionById);

// Tạo một câu hỏi mới
router.post('/', questionController.createQuestion);

// Cập nhật thông tin một câu hỏi
router.put('/:id', questionController.updateQuestion);

// Xóa một câu hỏi
router.delete('/:id', questionController.deleteQuestion);

// Lấy danh sách câu hỏi theo lo_id
//router.get('/by-lo/:lo_id', questionController.getQuestionsByLoId);
router.post('/import', upload.single('file'), handleMulterError, questionController.importQuestionsFromCSV);
router.post('/import-excel', upload.single('file'), handleMulterError, questionController.importQuestionsFromExcel);
module.exports = router;