'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
    class Quiz extends Model {
        static associate(models) {
            Quiz.belongsTo(models.Subject, { foreignKey: 'subject_id' });
            Quiz.belongsToMany(models.Question, { through: models.QuizQuestion, foreignKey: 'quiz_id' });
            Quiz.hasMany(models.QuizResult, { foreignKey: 'quiz_id' });
        }
    }

    Quiz.init(
        {
            quiz_id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true,
                autoIncrementIdentity: true
            },
            subject_id: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: 'Subjects',
                    key: 'subject_id',
                },
            },
            name: {
                type: DataTypes.STRING,
                allowNull: false,
            },
            duration: {
                type: DataTypes.INTEGER,
            },
            start_time: {
                type: DataTypes.DATE,
                allowNull: true,
            },
            end_time: {
                type: DataTypes.DATE,
                allowNull: true,
            },
            update_time: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW
            },
            status: {
                type: DataTypes.STRING,
                allowNull: false,
                defaultValue: 'pending',
                validate: {
                    isIn: [['pending', 'active', 'ended', 'finished']]
                }
            },
            pin: {
                type: DataTypes.STRING,
                allowNull: true,
                unique: true
            },
            current_question_index: {
                type: DataTypes.INTEGER,
                defaultValue: 0
            },
            show_leaderboard: {
                type: DataTypes.BOOLEAN,
                defaultValue: false
            },
            adaptive_config: {
                type: DataTypes.JSON,
                allowNull: true,
                comment: 'Configuration for adaptive quiz generation'
            }
        },
        {
            sequelize,
            modelName: 'Quiz',
            tableName: 'Quizzes',
            timestamps: false
        }
    );

    return Quiz;
};