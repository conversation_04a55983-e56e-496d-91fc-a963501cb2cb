// redis/utils.js
const { redisClient, connectRedis } = require('./redis');

// <PERSON><PERSON><PERSON> bảo kết nối Redis
connectRedis().catch(err => {
    console.error('Failed to connect to Redis:', err);
});

// Hàm lưu cache
const setCache = async (key, value, ttl = 3600) => {
    try {
        await redisClient.set(key, JSON.stringify(value), 'EX', ttl);
    } catch (err) {
        console.error('Error setting cache:', err);
    }
};

// Hàm lấy cache
const getCache = async (key) => {
    try {
        const data = await redisClient.get(key);
        return data ? JSON.parse(data) : null;
    } catch (err) {
        console.error('Error getting cache:', err);
        return null;
    }
};

// Hàm xóa cache
const deleteCache = async (key) => {
    try {
        await redisClient.del(key);
    } catch (err) {
        console.error('Error deleting cache:', err);
    }
};

// Hàm xóa cache theo pattern
const deleteCacheByPattern = async (pattern) => {
    try {
        const keys = await redisClient.keys(pattern);
        if (keys.length > 0) {
            await redisClient.del(keys);
        }
    } catch (err) {
        console.error('Error deleting cache by pattern:', err);
    }
};

module.exports = {
    setCache,
    getCache,
    deleteCache,
    deleteCacheByPattern
};