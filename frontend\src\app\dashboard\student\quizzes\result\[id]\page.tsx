"use client";

import { useState, useEffect, use } from "react";
import { useAuthStatus } from "@/hooks/use-auth";
import { quizService } from "@/services/api";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import {
  ChevronLeft,
  Clock,
  Calendar,
  Target,
  CheckCircle2,
} from "lucide-react";
import { format } from "date-fns";
import { vi } from "date-fns/locale";
import { Progress } from "@/components/ui/progress";
import { QuizResultStudent } from "@/types/quiz";
import { useRouter } from "next/navigation";
import StudentRadarChart from "@/components/charts/StudentRadarChart";

interface QuizResultPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default function QuizResultDetailPage({ params }: QuizResultPageProps) {
  const { getUser } = useAuthStatus();
  const [resultDetail, setResultDetail] = useState<QuizResultStudent | null>(
    null
  );
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  // Sử dụng React.use() để unwrap params
  const resolvedParams = use(params);
  const resultId = Number(resolvedParams.id);

  useEffect(() => {
    const fetchQuizResult = async () => {
      try {
        setIsLoading(true);

        if (isNaN(resultId)) {
          setError("ID kết quả không hợp lệ");
          setIsLoading(false);
          return;
        }

        const result = await quizService.getQuizResultById(resultId);
        console.log(result);
        // Kiểm tra quyền truy cập (chỉ được xem kết quả của chính mình)
        const user = getUser();
        if (!user || String(user.user_id) !== String(result.user_id)) {
          console.log(
            "Debug - user.user_id:",
            user?.user_id,
            "result.user_id:",
            result.user_id
          );
          setError("Bạn không có quyền xem kết quả này");
          setIsLoading(false);
          return;
        }

        setResultDetail(result);
        setError(null);
      } catch (err) {
        console.error("Lỗi khi lấy chi tiết kết quả bài kiểm tra:", err);
        setError("Đã xảy ra lỗi khi tải chi tiết kết quả bài kiểm tra");
      } finally {
        setIsLoading(false);
      }
    };

    fetchQuizResult();
  }, [resultId]);

  const formatDate = (dateString: string) => {
    if (!dateString) return "N/A";
    try {
      return format(new Date(dateString), "dd/MM/yyyy HH:mm", { locale: vi });
    } catch {
      return "Ngày không hợp lệ";
    }
  };

  const formatCompletionTime = (time: number | null) => {
    if (!time) return "N/A";
    const minutes = Math.floor(time / 60);
    const seconds = time % 60;
    return `${minutes} phút ${seconds < 10 ? "0" : ""}${seconds}`;
  };

  const handleBack = () => {
    router.push("/dashboard/student/quizzes/completed");
  };

  if (isLoading) {
    return (
      <div className="w-full mx-auto">
        <div className="mb-4 sm:mb-6 md:mb-8 flex items-center">
          <Skeleton className="h-8 w-24" />
        </div>

        <div className="mb-6 sm:mb-8 md:mb-10">
          <Skeleton className="h-8 w-1/3 mb-2" />
          <Skeleton className="h-4 w-1/4" />
        </div>

        <div className="space-y-6 sm:space-y-8 md:space-y-10">
          <Skeleton className="h-64 w-full rounded-lg" />
          <Skeleton className="h-40 w-full rounded-lg" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full mx-auto">
        <div className="text-center py-10">
          <div className="text-2xl text-red-500 mb-4">{error}</div>
          <Button onClick={handleBack}>Quay lại danh sách</Button>
        </div>
      </div>
    );
  }

  if (!resultDetail) {
    return (
      <div className="w-full mx-auto">
        <div className="text-center py-10">
          <div className="text-2xl mb-4">
            Không tìm thấy kết quả bài kiểm tra
          </div>
          <Button onClick={handleBack}>Quay lại danh sách</Button>
        </div>
      </div>
    );
  }

  const scorePercentage = (resultDetail.score / 10) * 100;

  // Phân loại kết quả
  const getScoreStatus = () => {
    if (resultDetail.score >= 8)
      return { label: "Xuất sắc", color: "text-green-600" };
    if (resultDetail.score >= 6.5)
      return { label: "Khá", color: "text-blue-600" };
    if (resultDetail.score >= 5)
      return { label: "Trung bình", color: "text-yellow-600" };
    return { label: "Chưa đạt", color: "text-red-600" };
  };

  const scoreStatus = getScoreStatus();

  return (
    <div className="w-full mx-auto">
      <div className="mb-4 sm:mb-6 md:mb-8 flex items-center">
        <Button
          variant="ghost"
          size="sm"
          className="gap-1 text-muted-foreground hover:text-foreground cursor-pointer"
          onClick={handleBack}
        >
          <ChevronLeft className="h-4 w-4" />
          <span>Quay lại</span>
        </Button>
      </div>

      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 sm:gap-4 mb-6 sm:mb-8 md:mb-10">
        <h2 className="text-xl sm:text-2xl font-bold">
          Chi tiết kết quả bài kiểm tra
        </h2>
      </div>

      <div className="space-y-6 sm:space-y-8 md:space-y-10">
        {/* Thông tin kết quả bài kiểm tra */}
        <Card className="bg-card border-2 overflow-hidden">
          <CardContent>
            <div>
              <div className="flex items-start sm:items-center justify-between gap-3 sm:gap-4 flex-wrap mb-3 sm:mb-4">
                <h3 className="text-lg sm:text-xl md:text-2xl font-bold">
                  {resultDetail.Quiz.name}
                </h3>
                <Badge className="bg-green-500 hover:bg-green-600 text-white px-2 py-1 sm:px-3 sm:py-1.5 text-xs sm:text-sm font-medium flex items-center">
                  <CheckCircle2 className="h-3.5 w-3.5 sm:h-4 sm:w-4 mr-1" />
                  Đã hoàn thành
                </Badge>
              </div>
              <p className="text-sm sm:text-base text-muted-foreground">
                {resultDetail.Student.name}
              </p>
            </div>

            <div className="border-t bg-muted/5 p-4 sm:p-5 md:px-7 md:py-6 mt-4 sm:mt-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-5 md:gap-7">
                {/* Điểm số */}
                <div className="flex items-center gap-3 sm:gap-4">
                  <div className="size-10 sm:size-12 rounded-full bg-primary/10 flex items-center justify-center text-primary">
                    <Target className="h-5 w-5 sm:h-6 sm:w-6" />
                  </div>
                  <div>
                    <p className="text-xs sm:text-sm font-medium mb-0.5 text-muted-foreground">
                      Kết quả
                    </p>
                    <div className="flex items-center">
                      <p
                        className={`text-sm sm:text-lg font-bold ${scoreStatus.color} mr-2`}
                      >
                        {resultDetail.score.toFixed(1)}/10
                      </p>
                      <Badge
                        className={`bg-opacity-20 text-xs ${scoreStatus.color}`}
                      >
                        {scoreStatus.label}
                      </Badge>
                    </div>
                  </div>
                </div>

                {/* Thời gian làm bài */}
                <div className="flex items-center gap-3 sm:gap-4">
                  <div className="size-10 sm:size-12 rounded-full bg-primary/10 flex items-center justify-center text-primary">
                    <Clock className="h-5 w-5 sm:h-6 sm:w-6" />
                  </div>
                  <div>
                    <p className="text-xs sm:text-sm font-medium mb-0.5 text-muted-foreground">
                      Thời gian làm bài
                    </p>
                    <p className="text-sm sm:text-base font-medium">
                      {formatCompletionTime(resultDetail.completion_time)}
                    </p>
                  </div>
                </div>
              </div>

              <div className="mt-6">
                <p className="text-xs sm:text-sm font-medium mb-1.5 text-muted-foreground">
                  Tiến độ hoàn thành
                </p>
                <div className="flex items-center mb-1.5">
                  <Progress
                    value={scorePercentage}
                    className="h-2 flex-1 mr-3"
                  />
                  <span className="text-xs font-medium">
                    {scorePercentage.toFixed(0)}%
                  </span>
                </div>
              </div>
            </div>

            <div className="border-t pt-4 sm:pt-3 md:pt-4 bg-muted/10 flex items-center gap-2 text-xs sm:text-sm text-muted-foreground">
              <Calendar className="h-3.5 w-3.5 sm:h-4 sm:w-4" />
              <p>Ngày làm bài: {formatDate(resultDetail.update_time)}</p>
            </div>
          </CardContent>
        </Card>

        {/* Radar Chart Analysis */}
        <StudentRadarChart
          quizId={resultDetail.quiz_id}
          quizName={resultDetail.Quiz.name}
          className="w-full"
        />
      </div>
    </div>
  );
}
