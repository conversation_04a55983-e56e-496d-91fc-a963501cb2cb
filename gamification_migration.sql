-- =====================================================
-- GAMIFICATION MIGRATION FOR SYNLEARNIA
-- Thêm các trường gamification vào bảng Users
-- =====================================================

-- Bắt đầu transaction
BEGIN;

-- Thêm các trường gamification vào bảng Users
ALTER TABLE "Users" ADD COLUMN IF NOT EXISTS "total_points" INTEGER NOT NULL DEFAULT 0;
COMMENT ON COLUMN "Users"."total_points" IS 'Tổng điểm tích lũy của người dùng';

ALTER TABLE "Users" ADD COLUMN IF NOT EXISTS "current_level" INTEGER NOT NULL DEFAULT 1;
COMMENT ON COLUMN "Users"."current_level" IS 'Cấp độ hiện tại của người dùng';

ALTER TABLE "Users" ADD COLUMN IF NOT EXISTS "experience_points" INTEGER NOT NULL DEFAULT 0;
COMMENT ON COLUMN "Users"."experience_points" IS 'Điểm kinh nghiệm trong cấp độ hiện tại';

ALTER TABLE "Users" ADD COLUMN IF NOT EXISTS "gamification_stats" JSONB NOT NULL DEFAULT '{
    "total_quizzes_completed": 0,
    "total_correct_answers": 0,
    "total_questions_answered": 0,
    "average_response_time": 0,
    "best_streak": 0,
    "current_streak": 0,
    "speed_bonus_earned": 0,
    "perfect_scores": 0
}'::jsonb;
COMMENT ON COLUMN "Users"."gamification_stats" IS 'Thống kê gamification của người dùng';

-- Tạo index cho performance
CREATE INDEX IF NOT EXISTS "idx_users_total_points" ON "Users" ("total_points");
CREATE INDEX IF NOT EXISTS "idx_users_current_level" ON "Users" ("current_level");

-- Tạo index cho JSONB stats để query nhanh hơn
CREATE INDEX IF NOT EXISTS "idx_users_gamification_stats_gin" ON "Users" USING GIN ("gamification_stats");

-- =====================================================
-- TẠO BẢNG BADGE SYSTEM (Tùy chọn - cho tương lai)
-- =====================================================

-- Bảng loại huy hiệu
CREATE TABLE IF NOT EXISTS "BadgeTypes" (
    "badge_id" SERIAL PRIMARY KEY,
    "name" VARCHAR(100) NOT NULL,
    "description" TEXT,
    "icon_url" VARCHAR(255),
    "criteria" JSONB NOT NULL DEFAULT '{}'::jsonb,
    "points_required" INTEGER DEFAULT 0,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Bảng huy hiệu của người dùng
CREATE TABLE IF NOT EXISTS "UserBadges" (
    "user_id" INTEGER NOT NULL,
    "badge_id" INTEGER NOT NULL,
    "earned_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    "progress" JSONB DEFAULT '{}'::jsonb,
    PRIMARY KEY ("user_id", "badge_id"),
    FOREIGN KEY ("user_id") REFERENCES "Users"("user_id") ON DELETE CASCADE,
    FOREIGN KEY ("badge_id") REFERENCES "BadgeTypes"("badge_id") ON DELETE CASCADE
);

-- Index cho bảng UserBadges
CREATE INDEX IF NOT EXISTS "idx_user_badges_user_id" ON "UserBadges" ("user_id");
CREATE INDEX IF NOT EXISTS "idx_user_badges_badge_id" ON "UserBadges" ("badge_id");
CREATE INDEX IF NOT EXISTS "idx_user_badges_earned_at" ON "UserBadges" ("earned_at");

-- =====================================================
-- THÊM DỮ LIỆU MẪU CHO BADGE SYSTEM
-- =====================================================

-- Thêm các huy hiệu cơ bản
INSERT INTO "BadgeTypes" ("name", "description", "icon_url", "criteria", "points_required") VALUES
('Người mới bắt đầu', 'Hoàn thành quiz đầu tiên', '/badges/beginner.png', '{"first_quiz": true}', 0),
('Tốc độ ánh sáng', 'Trả lời đúng 10 câu trong vòng 5 giây', '/badges/speed.png', '{"speed_answers": 10, "max_time": 5000}', 50),
('Chuỗi vàng', 'Trả lời đúng 5 câu liên tiếp', '/badges/streak.png', '{"streak_count": 5}', 30),
('Chuyên gia HTML', 'Đạt 100 điểm trong các quiz HTML', '/badges/html_expert.png', '{"subject": "HTML", "points": 100}', 100),
('Chuyên gia CSS', 'Đạt 100 điểm trong các quiz CSS', '/badges/css_expert.png', '{"subject": "CSS", "points": 100}', 100),
('Chuyên gia JavaScript', 'Đạt 100 điểm trong các quiz JavaScript', '/badges/js_expert.png', '{"subject": "JavaScript", "points": 100}', 100),
('Hoàn hảo', 'Đạt điểm tuyệt đối trong 3 quiz', '/badges/perfect.png', '{"perfect_scores": 3}', 150),
('Kiên trì', 'Hoàn thành 20 quiz', '/badges/persistent.png', '{"total_quizzes": 20}', 200),
('Bậc thầy', 'Đạt level 10', '/badges/master.png', '{"level": 10}', 1000),
('Huyền thoại', 'Đạt 2000 điểm tổng', '/badges/legend.png', '{"total_points": 2000}', 2000)
ON CONFLICT DO NOTHING;

-- =====================================================
-- CẬP NHẬT DỮ LIỆU CHO USERS HIỆN CÓ (Nếu cần)
-- =====================================================

-- Cập nhật điểm cho users đã có dữ liệu quiz (tùy chọn)
-- Uncomment nếu muốn tính lại điểm cho users hiện có
/*
UPDATE "Users" SET 
    "total_points" = COALESCE((
        SELECT SUM(score * 10) 
        FROM "QuizResults" 
        WHERE "QuizResults"."user_id" = "Users"."user_id"
    ), 0),
    "current_level" = GREATEST(1, FLOOR(COALESCE((
        SELECT SUM(score * 10) 
        FROM "QuizResults" 
        WHERE "QuizResults"."user_id" = "Users"."user_id"
    ), 0) / 100) + 1),
    "experience_points" = COALESCE((
        SELECT SUM(score * 10) 
        FROM "QuizResults" 
        WHERE "QuizResults"."user_id" = "Users"."user_id"
    ), 0) % 100
WHERE EXISTS (
    SELECT 1 FROM "QuizResults" 
    WHERE "QuizResults"."user_id" = "Users"."user_id"
);
*/

-- Commit transaction
COMMIT;

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Kiểm tra các cột đã được thêm
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'Users' 
AND column_name IN ('total_points', 'current_level', 'experience_points', 'gamification_stats')
ORDER BY ordinal_position;

-- Kiểm tra indexes đã được tạo
SELECT indexname, indexdef 
FROM pg_indexes 
WHERE tablename = 'Users' 
AND indexname LIKE '%gamification%' OR indexname LIKE '%points%' OR indexname LIKE '%level%';

-- Kiểm tra badge tables
SELECT table_name 
FROM information_schema.tables 
WHERE table_name IN ('BadgeTypes', 'UserBadges');

-- Đếm số lượng badges đã thêm
SELECT COUNT(*) as total_badges FROM "BadgeTypes";

PRINT 'Gamification migration completed successfully!';
