# Hướng dẫn test API Radar Chart với Postman

## 1. <PERSON><PERSON><PERSON><PERSON> lập Postman Environment

### Tạo Environment mới:
1. Click vào icon b<PERSON>h răng (Settings) ở góc trên bên phải
2. Click "Add" để tạo environment mới
3. Đặt tên: `QL_CTDT_API_Test`

### Thêm các biến môi trường:
| Variable | Initial Value | Current Value |
|----------|---------------|---------------|
| `base_url` | `http://localhost:3000` | `http://localhost:3000` |
| `token` | (để trống) | (sẽ được set sau khi login) |
| `quiz_id` | (để trống) | (sẽ được set sau khi lấy danh sách quiz) |

## 2. Collection Setup

### Tạo Collection mới:
1. Click "New" → "Collection"
2. Đặt tên: `QL_CTDT Radar Chart API`
3. Trong tab "Authorization", chọn Type: "Bearer Token"
4. Trong Token field, nhập: `{{token}}`

## 3. Các Request cần tạo

### Request 1: Login
- **Method:** POST
- **URL:** `{{base_url}}/api/users/login`
- **Headers:** 
  - Content-Type: application/json
- **Body (raw JSON):**
```json
{
  "email": "<EMAIL>",
  "password": "123456"
}
```

**Pre-request Script:**
```javascript
// Không cần script gì
```

**Tests Script:**
```javascript
if (pm.response.code === 200) {
    const response = pm.response.json();
    pm.environment.set("token", response.token);
    console.log("Token saved:", response.token);
}
```

### Request 2: Get Quizzes
- **Method:** GET
- **URL:** `{{base_url}}/api/quizzes`
- **Headers:** 
  - Authorization: Bearer {{token}}

**Tests Script:**
```javascript
if (pm.response.code === 200) {
    const response = pm.response.json();
    if (response.quizzes && response.quizzes.length > 0) {
        pm.environment.set("quiz_id", response.quizzes[0].quiz_id);
        console.log("Quiz ID saved:", response.quizzes[0].quiz_id);
    }
}
```

### Request 3: Get All Radar Data
- **Method:** GET
- **URL:** `{{base_url}}/api/quiz-results/quiz/{{quiz_id}}/radar/all`
- **Headers:** 
  - Authorization: Bearer {{token}}

**Tests Script:**
```javascript
pm.test("Status code is 200", function () {
    pm.response.to.have.status(200);
});

pm.test("Response has radar_data", function () {
    const response = pm.response.json();
    pm.expect(response).to.have.property('radar_data');
});

pm.test("Response has quiz_id", function () {
    const response = pm.response.json();
    pm.expect(response.quiz_id).to.eql(parseInt(pm.environment.get("quiz_id")));
});
```

### Request 4: Get Current User Radar Data
- **Method:** GET
- **URL:** `{{base_url}}/api/quiz-results/quiz/{{quiz_id}}/radar/current-user`
- **Headers:** 
  - Authorization: Bearer {{token}}

**Tests Script:**
```javascript
pm.test("Status code is 200", function () {
    pm.response.to.have.status(200);
});

pm.test("Response has user_id", function () {
    const response = pm.response.json();
    pm.expect(response).to.have.property('user_id');
});
```

### Request 5: Get Average Radar Data
- **Method:** GET
- **URL:** `{{base_url}}/api/quiz-results/quiz/{{quiz_id}}/radar/average`
- **Headers:** 
  - Authorization: Bearer {{token}}

**Tests Script:**
```javascript
pm.test("Status code is 200", function () {
    pm.response.to.have.status(200);
});

pm.test("Response has radar_data", function () {
    const response = pm.response.json();
    pm.expect(response).to.have.property('radar_data');
});
```

### Request 6: Get Top Performer Radar Data
- **Method:** GET
- **URL:** `{{base_url}}/api/quiz-results/quiz/{{quiz_id}}/radar/top-performer`
- **Headers:** 
  - Authorization: Bearer {{token}}

**Tests Script:**
```javascript
pm.test("Status code is 200", function () {
    pm.response.to.have.status(200);
});

pm.test("Response has top_performer", function () {
    const response = pm.response.json();
    pm.expect(response).to.have.property('top_performer');
});
```

## 4. Test Cases

### Test Case 1: Admin User
1. Chạy request "Login" với tài khoản admin
2. Chạy request "Get Quizzes" để lấy quiz_id
3. Chạy tất cả các request radar chart
4. Kiểm tra response có đầy đủ dữ liệu

### Test Case 2: Teacher User
1. Chạy request "Login" với tài khoản teacher
2. Chạy request "Get Quizzes" để lấy quiz_id
3. Chạy các request: Get All Radar Data, Get Average Radar Data, Get Top Performer Radar Data
4. Kiểm tra không thể truy cập Get Current User Radar Data (403 error)

### Test Case 3: Student User
1. Chạy request "Login" với tài khoản student
2. Chạy request "Get Quizzes" để lấy quiz_id
3. Chạy các request: Get All Radar Data, Get Current User Radar Data
4. Kiểm tra không thể truy cập Get Average Radar Data và Get Top Performer Radar Data (403 error)

### Test Case 4: Invalid Token
1. Set token = "invalid_token"
2. Chạy bất kỳ request nào
3. Kiểm tra nhận được 401 error

### Test Case 5: Invalid Quiz ID
1. Set quiz_id = 999999
2. Chạy bất kỳ request radar chart nào
3. Kiểm tra nhận được 404 error

## 5. Expected Responses

### Success Response (200):
```json
{
  "quiz_id": 1,
  "quiz_name": "Quiz Test",
  "total_questions": 10,
  "radar_data": {
    "current_user": {
      "user_id": 1,
      "data": {
        "difficulty_levels": {
          "easy": {
            "accuracy": 80,
            "questions_count": 3,
            "average_response_time": 15000
          }
        },
        "learning_outcomes": {
          "LO1": {
            "accuracy": 75,
            "questions_count": 5,
            "average_response_time": 18000
          }
        },
        "performance_metrics": {
          "average_response_time": 20000,
          "completion_rate": 100,
          "first_attempt_accuracy": 70,
          "overall_accuracy": 70
        }
      }
    }
  }
}
```

### Error Response (403):
```json
{
  "error": "Không có quyền truy cập"
}
```

### Error Response (404):
```json
{
  "error": "Không tìm thấy quiz"
}
```

### Error Response (401):
```json
{
  "error": "Token không hợp lệ"
}
```

## 6. Automation Scripts

### Pre-request Script cho tất cả requests (trừ Login):
```javascript
// Kiểm tra token có tồn tại không
if (!pm.environment.get("token")) {
    console.error("Token not found. Please run Login request first.");
}
```

### Collection-level Pre-request Script:
```javascript
// Set default headers
pm.request.headers.add({
    key: 'Content-Type',
    value: 'application/json'
});
```

## 7. Environment Variables cho các tài khoản test

### Admin Account:
```json
{
  "admin_email": "<EMAIL>",
  "admin_password": "123456"
}
```

### Teacher Account:
```json
{
  "teacher_email": "<EMAIL>", 
  "teacher_password": "123456"
}
```

### Student Account:
```json
{
  "student_email": "<EMAIL>",
  "student_password": "123456"
}
```

## 8. Tips và Best Practices

1. **Luôn chạy Login trước** để lấy token
2. **Kiểm tra response status** trong Tests tab
3. **Validate response structure** để đảm bảo API trả về đúng format
4. **Test với các role khác nhau** để kiểm tra authorization
5. **Test với invalid data** để kiểm tra error handling
6. **Sử dụng environment variables** để dễ dàng switch giữa các môi trường
7. **Export collection** để chia sẻ với team

## 9. Troubleshooting

### Lỗi thường gặp:
1. **401 Unauthorized:** Token hết hạn hoặc không hợp lệ
2. **403 Forbidden:** Không có quyền truy cập API
3. **404 Not Found:** Quiz ID không tồn tại
4. **500 Internal Server Error:** Lỗi server

### Debug:
1. Kiểm tra token trong environment
2. Kiểm tra quiz_id có đúng không
3. Kiểm tra role của user có phù hợp không
4. Kiểm tra server logs
5. Sử dụng console.log trong Tests script để debug 