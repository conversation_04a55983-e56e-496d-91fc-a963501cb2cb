# Thiết Kế Adaptive Quiz Generation

## 🎯 Mục tiêu

Tạo quiz thích ứng dựa trên điểm yếu cá nhân của từng học sinh, sử dụng dữ liệu từ Improvement Analysis API đã có.

## 🧠 Thuật toán Adaptive Quiz

### 1. **Phân tích điểm yếu của học sinh**
```
Input: user_id, subject_id (hoặc quiz_id gần nhất)
↓
Gọi Improvement Analysis API
↓
Lấy weak_levels + chapters_need_improvement
↓
Tính toán priority score cho từng LO và Level
```

### 2. **Thuật toán chọn câu hỏi**
```
Adaptive Algorithm:
├── 60% câu hỏi từ weak areas (accuracy < 70%)
├── 25% câu hỏi từ medium areas (70% ≤ accuracy < 85%)
└── 15% câu hỏi từ strong areas (accuracy ≥ 85%)

Difficulty Distribution:
├── Nếu weak ở Hard: 20% Easy, 50% Medium, 30% Hard
├── Nếu weak ở Medium: 30% Easy, 50% Medium, 20% Hard  
└── Nếu weak ở Easy: 50% Easy, 40% Medium, 10% Hard
```

### 3. **Scoring System cho LO Priority**
```javascript
LO Priority Score = (100 - accuracy) * weight_factor

weight_factor:
- High priority: 3.0
- Medium priority: 2.0  
- Low priority: 1.0
- No data: 1.5 (default)
```

## 📊 API Design

### **Endpoint:** `POST /api/quizzes/adaptive/generate`

### **Request Body:**
```json
{
  "user_id": 123,
  "subject_id": 5,
  "quiz_config": {
    "name": "Quiz Thích Ứng - Chương 3",
    "total_questions": 20,
    "duration": 30,
    "focus_mode": "weak_areas", // "weak_areas" | "balanced" | "challenge"
    "difficulty_adjustment": "auto" // "auto" | "manual"
  },
  "manual_config": { // Optional, nếu difficulty_adjustment = "manual"
    "difficulty_ratio": {
      "easy": 30,
      "medium": 50, 
      "hard": 20
    },
    "lo_weights": {
      "LO3.1": 3.0,
      "LO3.2": 2.5
    }
  }
}
```

### **Response:**
```json
{
  "success": true,
  "quiz": {
    "quiz_id": 456,
    "name": "Quiz Thích Ứng - Chương 3",
    "total_questions": 20,
    "duration": 30,
    "pin": "ABC123",
    "adaptive_config": {
      "user_id": 123,
      "focus_mode": "weak_areas",
      "weak_areas_identified": [
        {
          "type": "level",
          "name": "Hard",
          "accuracy": 35,
          "priority": "high"
        },
        {
          "type": "lo", 
          "name": "LO3.2",
          "accuracy": 42,
          "priority": "high"
        }
      ],
      "question_distribution": {
        "by_priority": {
          "weak_areas": 12,
          "medium_areas": 5,
          "strong_areas": 3
        },
        "by_difficulty": {
          "easy": 4,
          "medium": 10,
          "hard": 6
        },
        "by_lo": {
          "LO3.1": 8,
          "LO3.2": 7,
          "LO3.3": 5
        }
      }
    }
  },
  "recommendations": {
    "study_focus": [
      "Tập trung vào câu hỏi mức độ Hard",
      "Ôn tập LO3.2: Database Normalization", 
      "Luyện tập thêm về Query Optimization"
    ],
    "expected_improvement": "+15-25% accuracy sau quiz này"
  }
}
```

## 🔧 Implementation Plan

### **Phase 1: Backend API (1 tuần)**

#### 1. Tạo Adaptive Quiz Controller
```javascript
// backend/src/controllers/adaptiveQuizController.js
exports.generateAdaptiveQuiz = async (req, res) => {
  // 1. Lấy improvement analysis của user
  // 2. Tính toán adaptive algorithm  
  // 3. Chọn câu hỏi theo thuật toán
  // 4. Tạo quiz với metadata adaptive
  // 5. Return quiz + recommendations
}
```

#### 2. Tạo Adaptive Algorithm Service
```javascript
// backend/src/services/adaptiveQuizService.js
class AdaptiveQuizService {
  async analyzeUserWeakness(userId, subjectId)
  async calculateLOPriorities(improvementData)
  async generateQuestionDistribution(priorities, totalQuestions)
  async selectAdaptiveQuestions(distribution, subjectId)
}
```

#### 3. Extend Quiz Model
```javascript
// Thêm vào Quiz model
adaptive_config: {
  type: DataTypes.JSON,
  allowNull: true,
  comment: 'Configuration for adaptive quiz'
}
```

### **Phase 2: Frontend Integration (1 tuần)**

#### 1. Adaptive Quiz Creation Page
```tsx
// frontend/src/app/dashboard/teaching/quizzes/adaptive/create/page.tsx
export default function CreateAdaptiveQuizPage() {
  return (
    <AdaptiveQuizCreationWizard />
  );
}
```

#### 2. Student Selection Component
```tsx
// Chọn học sinh để tạo quiz thích ứng
<StudentSelector 
  onSelect={handleStudentSelect}
  showWeaknessPreview={true}
/>
```

#### 3. Adaptive Config Component  
```tsx
// Cấu hình adaptive parameters
<AdaptiveConfigForm
  studentWeakness={weaknessData}
  onConfigChange={handleConfigChange}
/>
```

### **Phase 3: Advanced Features (1 tuần)**

#### 1. Batch Adaptive Quiz
```javascript
// Tạo quiz thích ứng cho nhiều học sinh cùng lúc
POST /api/quizzes/adaptive/batch-generate
{
  "user_ids": [123, 124, 125],
  "subject_id": 5,
  "quiz_config": {...}
}
```

#### 2. Adaptive Quiz Analytics
```javascript
// Phân tích hiệu quả của adaptive quiz
GET /api/quizzes/adaptive/:quizId/effectiveness
```

#### 3. Auto-Adaptive Mode
```javascript
// Tự động tạo quiz thích ứng định kỳ
POST /api/quizzes/adaptive/schedule
{
  "user_id": 123,
  "subject_id": 5, 
  "schedule": "weekly",
  "auto_config": true
}
```

## 🎨 UI/UX Design

### **1. Teacher Dashboard - Adaptive Quiz Section**
```
┌─────────────────────────────────────────────────┐
│ 🧠 Tạo Quiz Thích Ứng                          │
├─────────────────────────────────────────────────┤
│ 👥 Chọn học sinh:                               │
│ [Dropdown: Nguyễn Văn A] [Xem điểm yếu]        │
│                                                 │
│ 📊 Điểm yếu đã phát hiện:                       │
│ • Hard questions: 35% accuracy ⚠️              │
│ • LO3.2 (Normalization): 42% accuracy ⚠️       │
│ • Chapter 3: Database Design: 45% accuracy     │
│                                                 │
│ ⚙️ Cấu hình quiz:                               │
│ Số câu hỏi: [20] Thời gian: [30] phút          │
│ Focus mode: [Weak Areas ▼]                     │
│                                                 │
│ [Tạo Quiz Thích Ứng] [Xem Preview]             │
└─────────────────────────────────────────────────┘
```

### **2. Student View - Adaptive Quiz Notification**
```
┌─────────────────────────────────────────────────┐
│ 🎯 Quiz Thích Ứng Dành Riêng Cho Bạn           │
├─────────────────────────────────────────────────┤
│ Giáo viên đã tạo quiz đặc biệt để giúp bạn      │
│ cải thiện những điểm yếu:                       │
│                                                 │
│ 🔴 Cần cải thiện:                               │
│ • Câu hỏi mức độ khó                           │
│ • Chương 3: Thiết kế CSDL                      │
│                                                 │
│ 📈 Mục tiêu: Cải thiện 15-25% accuracy         │
│ ⏱️ Thời gian: 30 phút | 📝 20 câu hỏi           │
│                                                 │
│ [Bắt Đầu Quiz] [Xem Gợi Ý Ôn Tập]             │
└─────────────────────────────────────────────────┘
```

### **3. Quiz Results - Adaptive Analysis**
```
┌─────────────────────────────────────────────────┐
│ 📊 Kết Quả Quiz Thích Ứng                       │
├─────────────────────────────────────────────────┤
│ Điểm số: 75/100 (+15% so với quiz trước) ✅     │
│                                                 │
│ 🎯 Cải thiện đạt được:                          │
│ • Hard questions: 35% → 50% (+15%) ✅           │
│ • LO3.2: 42% → 58% (+16%) ✅                    │
│                                                 │
│ 🔄 Vẫn cần cải thiện:                           │
│ • LO3.3: 45% accuracy ⚠️                       │
│                                                 │
│ 💡 Gợi ý tiếp theo:                             │
│ • Ôn tập thêm về Query Optimization            │
│ • Làm quiz thích ứng tiếp theo sau 3 ngày      │
│                                                 │
│ [Tạo Quiz Tiếp Theo] [Xem Tài Liệu Ôn Tập]     │
└─────────────────────────────────────────────────┘
```

## 🚀 Advanced Features (Future)

### **1. AI-Powered Difficulty Adjustment**
- Realtime adjustment dựa trên performance trong quiz
- Machine learning để predict optimal difficulty

### **2. Collaborative Adaptive Quiz**
- Tạo quiz thích ứng cho nhóm học sinh có điểm yếu tương tự
- Peer learning recommendations

### **3. Adaptive Learning Path**
- Tự động tạo sequence của adaptive quizzes
- Integration với curriculum planning

### **4. Gamification**
- Achievement system cho adaptive quiz completion
- Progress tracking với visual rewards

## 📈 Success Metrics

### **Immediate (1 tháng):**
- 80% teachers sử dụng adaptive quiz feature
- 25% improvement trong weak areas accuracy
- 90% student satisfaction với personalized quizzes

### **Long-term (3 tháng):**
- 40% overall improvement trong subject performance
- 60% reduction trong số học sinh có điểm yếu persistent
- 95% teacher adoption rate
