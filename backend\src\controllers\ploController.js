const { PLO, Program, PO, Subject, LO, ProgramOutcomeTracking, sequelize } = require('../models');
const { Op } = require('sequelize');

exports.getAllPLOs = async (req, res) => {
    try {
        const { page = 1, limit = 10 } = req.query;
        const offset = (page - 1) * limit;

        const plos = await PLO.findAndCountAll({
            limit: parseInt(limit),
            offset: parseInt(offset),
            include: [
                { model: Program, attributes: ['program_id', 'name'] },
                { model: PO, through: { attributes: [] }, attributes: ['po_id', 'name'] },
                { model: Subject, attributes: ['subject_id', 'name'] },
                { model: LO, attributes: ['lo_id', 'name'] },
            ],
        });

        res.status(200).json({
            totalItems: plos.count,
            totalPages: Math.ceil(plos.count / limit),
            currentPage: parseInt(page),
            plos: plos.rows,
        });
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi lấy danh sách PLO', error: error.message });
    }
};

exports.getPLOById = async (req, res) => {
    try {
        const plo = await PLO.findByPk(req.params.id, {
            include: [
                { model: Program, attributes: ['program_id', 'name'] },
                { model: PO, through: { attributes: [] }, attributes: ['po_id', 'name'] },
                { model: Subject, attributes: ['subject_id', 'name'] },
                { model: LO, attributes: ['lo_id', 'name'] },
            ],
        });

        if (!plo) return res.status(404).json({ message: 'PLO không tồn tại' });
        res.status(200).json(plo);
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi lấy thông tin PLO', error: error.message });
    }
};

exports.createPLO = async (req, res) => {
    try {
        const { description, program_id } = req.body;

        if (!program_id) {
            return res.status(400).json({ message: 'Thiếu trường bắt buộc: program_id' });
        }

        const program = await Program.findByPk(program_id);
        if (!program) {
            return res.status(400).json({ message: 'Chương trình không tồn tại' });
        }

        const newPLO = await PLO.create({ description, program_id });
        res.status(201).json(newPLO);
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi tạo PLO', error: error.message });
    }
};

exports.updatePLO = async (req, res) => {
    try {
        const { description, program_id } = req.body;

        const plo = await PLO.findByPk(req.params.id);
        if (!plo) return res.status(404).json({ message: 'PLO không tồn tại' });

        if (program_id) {
            const program = await Program.findByPk(program_id);
            if (!program) return res.status(400).json({ message: 'Chương trình không tồn tại' });
        }

        await plo.update({
            description: description || plo.description,
            program_id: program_id || plo.program_id,
        });

        res.status(200).json(plo);
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi cập nhật PLO', error: error.message });
    }
};

exports.deletePLO = async (req, res) => {
    try {
        const plo = await PLO.findByPk(req.params.id);
        if (!plo) return res.status(404).json({ message: 'PLO không tồn tại' });

        await plo.destroy();
        res.status(200).json({ message: 'Xóa PLO thành công' });
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi xóa PLO', error: error.message });
    }
};

// =====================================================
// NEW ADMIN FUNCTIONS FOR PLO MANAGEMENT
// =====================================================

// Get PLOs by Program
exports.getPLOsByProgram = async (req, res) => {
    try {
        const { program_id } = req.params;
        const { page = 1, limit = 10 } = req.query;
        const offset = (page - 1) * limit;

        const plos = await PLO.findAndCountAll({
            where: { program_id },
            limit: parseInt(limit),
            offset: parseInt(offset),
            include: [
                { model: Program, attributes: ['program_id', 'name'] },
                { model: PO, through: { attributes: [] }, attributes: ['po_id', 'name'] },
                { model: Subject, attributes: ['subject_id', 'name'] },
            ],
        });

        res.status(200).json({
            totalItems: plos.count,
            totalPages: Math.ceil(plos.count / limit),
            currentPage: parseInt(page),
            plos: plos.rows,
        });
    } catch (error) {
        res.status(500).json({ message: 'Error getting PLOs by program', error: error.message });
    }
};

// Get PLO Statistics
exports.getPLOStatistics = async (req, res) => {
    try {
        const { program_id } = req.query;

        let whereClause = {};
        if (program_id) {
            whereClause.program_id = program_id;
        }

        // Get total PLOs
        const totalPLOs = await PLO.count({ where: whereClause });

        // Get PLOs with tracking data
        const plosWithTracking = await PLO.findAll({
            where: whereClause,
            include: [{
                model: ProgramOutcomeTracking,
                where: { outcome_type: 'PLO', is_active: true },
                required: false,
                attributes: ['current_score', 'achievement_status']
            }],
            attributes: ['plo_id', 'description']
        });

        // Calculate statistics
        let totalStudentsTracked = 0;
        let achievedCount = 0;
        let averageScore = 0;
        let totalScore = 0;
        let scoreCount = 0;

        plosWithTracking.forEach(plo => {
            const trackingData = plo.ProgramOutcomeTrackings || [];
            totalStudentsTracked += trackingData.length;

            trackingData.forEach(tracking => {
                if (tracking.achievement_status === 'achieved' || tracking.achievement_status === 'exceeded') {
                    achievedCount++;
                }
                totalScore += tracking.current_score;
                scoreCount++;
            });
        });

        averageScore = scoreCount > 0 ? (totalScore / scoreCount).toFixed(2) : 0;
        const achievementRate = totalStudentsTracked > 0 ? ((achievedCount / totalStudentsTracked) * 100).toFixed(2) : 0;

        res.json({
            overview: {
                total_plos: totalPLOs,
                plos_with_tracking: plosWithTracking.filter(plo => plo.ProgramOutcomeTrackings?.length > 0).length,
                total_students_tracked: totalStudentsTracked,
                average_score: parseFloat(averageScore),
                achievement_rate: parseFloat(achievementRate)
            },
            plo_breakdown: plosWithTracking.map(plo => ({
                plo_id: plo.plo_id,
                description: plo.description,
                students_tracked: plo.ProgramOutcomeTrackings?.length || 0,
                average_score: plo.ProgramOutcomeTrackings?.length > 0 ?
                    (plo.ProgramOutcomeTrackings.reduce((sum, t) => sum + t.current_score, 0) / plo.ProgramOutcomeTrackings.length).toFixed(2) : 0
            })),
            generated_at: new Date()
        });

    } catch (error) {
        console.error('Error getting PLO statistics:', error);
        res.status(500).json({ message: 'Error getting PLO statistics', error: error.message });
    }
};

// Get PLO Achievement Analysis
exports.getPLOAchievementAnalysis = async (req, res) => {
    try {
        const { program_id } = req.params;

        // Get PLOs in program
        const plos = await PLO.findAll({
            where: { program_id },
            include: [{
                model: ProgramOutcomeTracking,
                where: {
                    outcome_type: 'PLO',
                    is_active: true
                },
                required: false,
                attributes: ['user_id', 'current_score', 'target_score', 'achievement_status']
            }]
        });

        const analysis = plos.map(plo => {
            const trackingData = plo.ProgramOutcomeTrackings || [];
            const totalStudents = trackingData.length;

            if (totalStudents === 0) {
                return {
                    plo_info: {
                        plo_id: plo.plo_id,
                        description: plo.description
                    },
                    achievement_metrics: {
                        total_students: 0,
                        achieved_count: 0,
                        achievement_rate: 0,
                        average_score: 0,
                        mastery_distribution: { expert: 0, proficient: 0, developing: 0, novice: 0 }
                    }
                };
            }

            const achievedCount = trackingData.filter(t =>
                t.achievement_status === 'achieved' || t.achievement_status === 'exceeded'
            ).length;

            const averageScore = trackingData.reduce((sum, t) => sum + t.current_score, 0) / totalStudents;

            // Mastery distribution for PLOs
            const masteryDistribution = { expert: 0, proficient: 0, developing: 0, novice: 0 };
            trackingData.forEach(t => {
                if (t.current_score >= 90) masteryDistribution.expert++;
                else if (t.current_score >= 80) masteryDistribution.proficient++;
                else if (t.current_score >= 70) masteryDistribution.developing++;
                else masteryDistribution.novice++;
            });

            return {
                plo_info: {
                    plo_id: plo.plo_id,
                    description: plo.description
                },
                achievement_metrics: {
                    total_students: totalStudents,
                    achieved_count: achievedCount,
                    achievement_rate: ((achievedCount / totalStudents) * 100).toFixed(2),
                    average_score: averageScore.toFixed(2),
                    mastery_distribution: masteryDistribution
                }
            };
        });

        res.json({
            program_id: parseInt(program_id),
            total_plos: analysis.length,
            plo_analysis: analysis,
            generated_at: new Date()
        });

    } catch (error) {
        console.error('Error getting PLO achievement analysis:', error);
        res.status(500).json({ message: 'Error getting PLO achievement analysis', error: error.message });
    }
};

// Bulk create PLOs
exports.bulkCreatePLOs = async (req, res) => {
    const transaction = await sequelize.transaction();
    try {
        const { plos } = req.body; // Array of PLO objects

        if (!Array.isArray(plos) || plos.length === 0) {
            await transaction.rollback();
            return res.status(400).json({ message: 'Invalid PLO list' });
        }

        // Validate each PLO
        for (const plo of plos) {
            if (!plo.description || !plo.program_id) {
                await transaction.rollback();
                return res.status(400).json({ message: 'Description and program_id are required for all PLOs' });
            }
        }

        // Create PLOs
        const createdPLOs = await PLO.bulkCreate(plos, { transaction });
        await transaction.commit();

        res.status(201).json({
            message: `Successfully created ${createdPLOs.length} PLOs`,
            created_plos: createdPLOs
        });

    } catch (error) {
        await transaction.rollback();
        console.error('Error bulk creating PLOs:', error);
        res.status(500).json({ message: 'Error bulk creating PLOs', error: error.message });
    }
};

// Bulk update PLOs
exports.bulkUpdatePLOs = async (req, res) => {
    const transaction = await sequelize.transaction();
    try {
        const { updates } = req.body; // Array of {plo_id, description, program_id}

        if (!Array.isArray(updates) || updates.length === 0) {
            await transaction.rollback();
            return res.status(400).json({ message: 'Invalid updates list' });
        }

        const results = [];
        for (const update of updates) {
            const { plo_id, description, program_id } = update;

            if (!plo_id) {
                continue; // Skip invalid entries
            }

            const plo = await PLO.findByPk(plo_id, { transaction });
            if (plo) {
                await plo.update({
                    description: description || plo.description,
                    program_id: program_id || plo.program_id
                }, { transaction });
                results.push(plo);
            }
        }

        await transaction.commit();

        res.json({
            message: `Successfully updated ${results.length} PLOs`,
            updated_plos: results
        });

    } catch (error) {
        await transaction.rollback();
        console.error('Error bulk updating PLOs:', error);
        res.status(500).json({ message: 'Error bulk updating PLOs', error: error.message });
    }
};

// Bulk delete PLOs
exports.bulkDeletePLOs = async (req, res) => {
    const transaction = await sequelize.transaction();
    try {
        const { plo_ids } = req.body; // Array of PLO IDs

        if (!Array.isArray(plo_ids) || plo_ids.length === 0) {
            await transaction.rollback();
            return res.status(400).json({ message: 'Invalid PLO IDs list' });
        }

        // Check if any PLO has tracking data
        const plosWithTracking = await PLO.findAll({
            where: { plo_id: { [Op.in]: plo_ids } },
            include: [{
                model: ProgramOutcomeTracking,
                required: true,
                attributes: ['tracking_id']
            }],
            transaction
        });

        if (plosWithTracking.length > 0) {
            await transaction.rollback();
            return res.status(400).json({
                message: 'Cannot delete PLOs with tracking data',
                plos_with_tracking: plosWithTracking.map(plo => ({
                    plo_id: plo.plo_id,
                    description: plo.description,
                    tracking_count: plo.ProgramOutcomeTrackings.length
                }))
            });
        }

        // Delete PLOs
        const deletedCount = await PLO.destroy({
            where: { plo_id: { [Op.in]: plo_ids } },
            transaction
        });

        await transaction.commit();

        res.json({
            message: `Successfully deleted ${deletedCount} PLOs`,
            deleted_count: deletedCount
        });

    } catch (error) {
        await transaction.rollback();
        console.error('Error bulk deleting PLOs:', error);
        res.status(500).json({ message: 'Error bulk deleting PLOs', error: error.message });
    }
};