const { QuizResult, User, Quiz, Question, Level, LO, UserQuestionHistory, ChapterLO, Chapter, ChapterSection, Subject } = require('../models');
const { Op } = require('sequelize');
const analyticsDataFlowService = require('../services/analyticsDataFlowService');

exports.getAllQuizResults = async (req, res) => {
    try {
        const { page = 1, limit = 10 } = req.query;
        const offset = (page - 1) * limit;

        const quizResults = await QuizResult.findAndCountAll({
            limit: parseInt(limit),
            offset: parseInt(offset),
            include: [
                { model: User, as: 'Student', attributes: ['user_id', 'name'] },
                { model: Quiz, as: 'Quiz', attributes: ['quiz_id', 'name'] },
            ],
        });

        res.status(200).json({
            totalItems: quizResults.count,
            totalPages: Math.ceil(quizResults.count / limit),
            currentPage: parseInt(page),
            quizResults: quizResults.rows,
        });
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi lấy danh sách QuizResult', error: error.message });
    }
};

exports.getQuizResultById = async (req, res) => {
    try {
        const quizResult = await QuizResult.findByPk(req.params.id, {
            include: [
                { model: User, as: 'Student', attributes: ['user_id', 'name'] },
                { model: Quiz, as: 'Quiz', attributes: ['quiz_id', 'name'] },
            ],
        });

        if (!quizResult) return res.status(404).json({ message: 'QuizResult không tồn tại' });
        res.status(200).json(quizResult);
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi lấy thông tin QuizResult', error: error.message });
    }
};

exports.createQuizResult = async (req, res) => {
    try {
        const { user_id, quiz_id, score, status, update_time, completion_time } = req.body;

        if (!user_id || !quiz_id || !score || !status) {
            return res.status(400).json({ message: 'Thiếu các trường bắt buộc' });
        }

        const user = await User.findByPk(user_id);
        const quiz = await Quiz.findByPk(quiz_id);

        if (!user) return res.status(400).json({ message: 'Người dùng không tồn tại' });
        if (!quiz) return res.status(400).json({ message: 'Quiz không tồn tại' });

        const newQuizResult = await QuizResult.create({
            user_id,
            quiz_id,
            score,
            status,
            update_time,
            completion_time,
        });

        // =====================================================
        // TRIGGER ANALYTICS DATA FLOW
        // =====================================================

        // Only trigger analytics for completed quizzes
        if (status === 'completed' || status === 'finished') {
            try {
                console.log(`🔄 Triggering analytics for quiz completion: User ${user_id}, Quiz ${quiz_id}, Score ${score}`);

                // Trigger analytics processing (non-blocking)
                analyticsDataFlowService.processQuizCompletion({
                    user_id,
                    quiz_id,
                    score,
                    quiz_result_id: newQuizResult.result_id
                }).then(result => {
                    if (result.success) {
                        console.log(`✅ Analytics processing completed for user ${user_id}`);
                    } else {
                        console.error(`❌ Analytics processing failed for user ${user_id}:`, result.error);
                    }
                }).catch(error => {
                    console.error(`❌ Analytics processing error for user ${user_id}:`, error);
                });

            } catch (error) {
                // Don't fail the quiz result creation if analytics fails
                console.error('❌ Error triggering analytics:', error);
            }
        }

        res.status(201).json(newQuizResult);
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi tạo QuizResult', error: error.message });
    }
};

exports.updateQuizResult = async (req, res) => {
    try {
        const { user_id, quiz_id, score, status, update_time, completion_time } = req.body;

        const quizResult = await QuizResult.findByPk(req.params.id);
        if (!quizResult) return res.status(404).json({ message: 'QuizResult không tồn tại' });

        if (user_id) {
            const user = await User.findByPk(user_id);
            if (!user) return res.status(400).json({ message: 'Người dùng không tồn tại' });
        }
        if (quiz_id) {
            const quiz = await Quiz.findByPk(quiz_id);
            if (!quiz) return res.status(400).json({ message: 'Quiz không tồn tại' });
        }

        await quizResult.update({
            user_id: user_id || quizResult.user_id,
            quiz_id: quiz_id || quizResult.quiz_id,
            score: score || quizResult.score,
            status: status || quizResult.status,
            update_time: update_time || quizResult.update_time,
            completion_time: completion_time || quizResult.completion_time,
        });

        res.status(200).json(quizResult);
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi cập nhật QuizResult', error: error.message });
    }
};

exports.deleteQuizResult = async (req, res) => {
    try {
        const quizResult = await QuizResult.findByPk(req.params.id);
        if (!quizResult) return res.status(404).json({ message: 'QuizResult không tồn tại' });

        await quizResult.destroy();
        res.status(200).json({ message: 'Xóa QuizResult thành công' });
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi xóa QuizResult', error: error.message });
    }
};

// Lấy kết quả quiz theo user_id (chỉ cho student, chỉ lấy kết quả của chính mình)
exports.getQuizResultsByUserId = async (req, res) => {
    try {
        const { user_id } = req.params;
        // Chỉ cho phép student lấy kết quả của chính mình
        if (req.roleName !== 'student' || req.user.user_id !== parseInt(user_id)) {
            return res.status(403).json({ message: 'Bạn chỉ có thể xem kết quả của chính mình' });
        }
        const results = await QuizResult.findAll({
            where: { user_id },
            include: [
                { model: User, as: 'Student', attributes: ['user_id', 'name'] },
                { model: Quiz, as: 'Quiz', attributes: ['quiz_id', 'name'] },
            ],
            order: [['update_time', 'DESC'], ['result_id', 'DESC']]
        });
        // Với mỗi kết quả, lấy lo_chapters tương ứng quiz_id
        const resultsWithChapters = [];
        const { QuizQuestion } = require('../models');
        for (const r of results) {
            const quiz_id = r.quiz_id;
            // Lấy tất cả question_id của quiz này qua bảng trung gian
            const quizQuestions = await QuizQuestion.findAll({ where: { quiz_id } });
            const questionIds = quizQuestions.map(q => q.question_id);
            // Lấy tất cả câu hỏi theo danh sách id
            const questions = await Question.findAll({
                where: { question_id: questionIds },
                include: [
                    { model: LO, as: 'LO', attributes: ['lo_id', 'name'] }
                ]
            });
            // Lấy tất cả LO duy nhất
            const loMap = {};
            questions.forEach(q => {
                if (q.lo_id && q.LO) {
                    loMap[q.lo_id] = q.LO.name;
                }
            });
            const loIds = Object.keys(loMap);
            // Với mỗi LO, lấy chương và section
            const loChapters = [];
            for (const loId of loIds) {
                const chapterLOs = await ChapterLO.findAll({ where: { lo_id: loId } });
                const chapterIds = chapterLOs.map(clo => clo.chapter_id);
                const chapters = await Chapter.findAll({
                    where: { chapter_id: chapterIds },
                    include: [
                        {
                            model: ChapterSection,
                            as: 'Sections',
                            attributes: ['section_id', 'title', 'content', 'order']
                        }
                    ]
                });
                loChapters.push({
                    lo_id: loId,
                    lo_name: loMap[loId],
                    chapters: chapters.map(chap => ({
                        chapter_id: chap.chapter_id,
                        chapter_name: chap.name,
                        sections: chap.Sections
                    }))
                });
            }
            resultsWithChapters.push({
                ...r.toJSON(),
                lo_chapters: loChapters
            });
        }
        res.status(200).json(resultsWithChapters);
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi lấy kết quả QuizResult theo user', error: error.message });
    }
};

// Lấy kết quả quiz theo quiz_id (chỉ cho admin và teacher)
exports.getQuizResultsByQuizId = async (req, res) => {
    try {
        const { quiz_id } = req.params;
        if (!["admin", "teacher"].includes(req.roleName)) {
            return res.status(403).json({ message: 'Chỉ admin hoặc giáo viên mới có quyền xem kết quả theo quiz' });
        }
        const results = await QuizResult.findAll({
            where: { quiz_id },
            include: [
                { model: User, as: 'Student', attributes: ['user_id', 'name', 'email'] },
                { model: Quiz, as: 'Quiz', attributes: ['quiz_id', 'name'] },
            ],
        });

        // Lấy tất cả question_id của quiz này qua bảng trung gian
        const quizQuestions = await require('../models').QuizQuestion.findAll({ where: { quiz_id } });
        const questionIds = quizQuestions.map(q => q.question_id);
        // Lấy tất cả câu hỏi theo danh sách id
        const questions = await Question.findAll({
            where: { question_id: questionIds },
            include: [
                { model: LO, as: 'LO', attributes: ['lo_id', 'name'] }
            ]
        });
        // Lấy tất cả LO duy nhất
        const loMap = {};
        questions.forEach(q => {
            if (q.lo_id && q.LO) {
                loMap[q.lo_id] = q.LO.name;
            }
        });
        const loIds = Object.keys(loMap);
        // Với mỗi LO, lấy chương và section
        const loChapters = [];
        for (const loId of loIds) {
            const chapterLOs = await ChapterLO.findAll({ where: { lo_id: loId } });
            const chapterIds = chapterLOs.map(clo => clo.chapter_id);
            const chapters = await Chapter.findAll({
                where: { chapter_id: chapterIds },
                include: [
                    {
                        model: ChapterSection,
                        as: 'Sections',
                        attributes: ['section_id', 'title', 'content', 'order']
                    }
                ]
            });
            loChapters.push({
                lo_id: loId,
                lo_name: loMap[loId],
                chapters: chapters.map(chap => ({
                    chapter_id: chap.chapter_id,
                    chapter_name: chap.name,
                    sections: chap.Sections
                }))
            });
        }
        // Gắn thêm vào mỗi kết quả
        const resultsWithChapters = results.map(r => ({
            ...r.toJSON(),
            lo_chapters: loChapters
        }));
        res.status(200).json(resultsWithChapters);
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi lấy kết quả QuizResult theo quiz', error: error.message });
    }
};

// API lấy dữ liệu radar chart cho người dùng hiện tại
exports.getCurrentUserRadarData = async (req, res) => {
    try {
        const { quizId } = req.params;
        const userId = req.user.user_id;

        // Kiểm tra quyền truy cập
        if (req.roleName !== 'student') {
            return res.status(403).json({ error: 'Chỉ học viên mới có thể xem dữ liệu radar của mình' });
        }

        console.log(`🔍 Getting radar data for user ${userId}, quiz ${quizId}`);

        // Lấy thông tin quiz và câu hỏi
        const quiz = await Quiz.findByPk(quizId, {
            include: [
                {
                    model: Question,
                    as: 'Questions',
                    through: { attributes: [] },
                    attributes: ['question_id', 'question_text', 'level_id', 'lo_id'],
                    include: [
                        {
                            model: Level,
                            as: 'Level',
                            attributes: ['level_id', 'name']
                        },
                        {
                            model: LO,
                            as: 'LO',
                            attributes: ['lo_id', 'name', 'description']
                        }
                    ]
                }
            ]
        });

        if (!quiz) {
            return res.status(404).json({ error: 'Không tìm thấy quiz' });
        }

        console.log(`✅ Quiz found: ${quiz.name} with ${quiz.Questions.length} questions`);

        // Lấy lịch sử trả lời câu hỏi của user
        const questionHistory = await UserQuestionHistory.findAll({
            where: {
                quiz_id: quizId,
                user_id: userId
            },
            include: [
                {
                    model: Question,
                    as: 'Question',
                    attributes: ['question_id', 'level_id', 'lo_id'],
                    include: [
                        {
                            model: Level,
                            as: 'Level',
                            attributes: ['level_id', 'name']
                        },
                        {
                            model: LO,
                            as: 'LO',
                            attributes: ['lo_id', 'name', 'description']
                        }
                    ]
                }
            ],
            order: [['attempt_date', 'ASC']]
        });

        console.log(`✅ Found ${questionHistory.length} question history records for user ${userId}`);

        // Nếu không có lịch sử, trả về dữ liệu mặc định
        if (questionHistory.length === 0) {
            console.log(`⚠️ No question history found for user ${userId}`);

            // Tạo dữ liệu mặc định dựa trên questions của quiz
            const defaultRadarData = {
                difficulty_levels: {},
                learning_outcomes: {},
                performance_metrics: {
                    average_response_time: 0,
                    completion_rate: 0,
                    first_attempt_accuracy: 0,
                    overall_accuracy: 0
                }
            };

            // Khởi tạo với 0 cho tất cả levels và LOs
            quiz.Questions.forEach(question => {
                const levelName = question.Level.name.toLowerCase();
                const loName = question.LO.name;

                if (!defaultRadarData.difficulty_levels[levelName]) {
                    defaultRadarData.difficulty_levels[levelName] = {
                        accuracy: 0,
                        questions_count: 0,
                        average_response_time: 0
                    };
                }

                if (!defaultRadarData.learning_outcomes[loName]) {
                    defaultRadarData.learning_outcomes[loName] = {
                        description: question.LO.description || '',
                        accuracy: 0,
                        questions_count: 0,
                        average_response_time: 0
                    };
                }
            });

            return res.status(200).json({
                user_id: userId,
                quiz_id: quizId,
                radar_data: defaultRadarData,
                message: 'Chưa có dữ liệu trả lời câu hỏi'
            });
        }

        // Tính toán dữ liệu radar
        const radarData = calculateRadarData(quiz.Questions, questionHistory);

        // === Bổ sung: Tìm LO yếu nhất và độ khó yếu nhất ===
        // 1. LO yếu nhất
        let weakestLO = null;
        Object.entries(radarData.learning_outcomes).forEach(([lo, data]) => {
            if (!weakestLO || data.accuracy < weakestLO.accuracy) {
                weakestLO = { lo_name: lo, accuracy: data.accuracy };
            }
        });
        // 2. Độ khó yếu nhất
        let weakestDifficulty = null;
        Object.entries(radarData.difficulty_levels).forEach(([level, data]) => {
            if (!weakestDifficulty || data.accuracy < weakestDifficulty.accuracy) {
                weakestDifficulty = { level, accuracy: data.accuracy };
            }
        });

        // === Bổ sung: Chỉ thêm chương vào LO yếu nhất ===
        if (weakestLO) {
            const { ChapterLO, Chapter, ChapterSection } = require('../models');
            const loObj = quiz.Questions.find(q => q.LO && q.LO.name === weakestLO.lo_name)?.LO;
            if (loObj) {
                weakestLO.lo_id = loObj.lo_id;
                // Lấy chương liên quan
                const chapterLOs = await ChapterLO.findAll({ where: { lo_id: loObj.lo_id } });
                const chapterIds = chapterLOs.map(clo => clo.chapter_id);
                const chapters = await Chapter.findAll({
                    where: { chapter_id: chapterIds },
                    include: [
                        {
                            model: ChapterSection,
                            as: 'Sections',
                            attributes: ['section_id', 'title', 'content', 'order']
                        }
                    ]
                });
                weakestLO.chapters = chapters.map(chap => ({
                    chapter_id: chap.chapter_id,
                    chapter_name: chap.name,
                    description: chap.description,
                    sections: chap.Sections
                }));
            }
        }
        // === Kết thúc bổ sung ===

        res.status(200).json({
            user_id: userId,
            quiz_id: quizId,
            radar_data: radarData,
            weakest_lo: weakestLO,
            weakest_difficulty: weakestDifficulty
        });
    } catch (error) {
        console.error('Lỗi trong getCurrentUserRadarData:', error);
        res.status(500).json({ error: 'Lỗi khi lấy dữ liệu radar', details: error.message });
    }
};

// API lấy dữ liệu radar chart trung bình của tất cả người tham gia
exports.getAverageRadarData = async (req, res) => {
    try {
        const { quizId } = req.params;


        // Lấy thông tin quiz và câu hỏi
        const quiz = await Quiz.findByPk(quizId, {
            include: [
                {
                    model: Question,
                    as: 'Questions',
                    through: { attributes: [] },
                    attributes: ['question_id', 'question_text', 'level_id', 'lo_id'],
                    include: [
                        {
                            model: Level,
                            as: 'Level',
                            attributes: ['level_id', 'name']
                        },
                        {
                            model: LO,
                            as: 'LO',
                            attributes: ['lo_id', 'name', 'description']
                        }
                    ]
                }
            ]
        });

        if (!quiz) {
            return res.status(404).json({ error: 'Không tìm thấy quiz' });
        }

        // Lấy tất cả lịch sử trả lời câu hỏi của quiz này
        const allQuestionHistory = await UserQuestionHistory.findAll({
            where: { quiz_id: quizId },
            include: [
                {
                    model: Question,
                    as: 'Question',
                    attributes: ['question_id', 'level_id', 'lo_id'],
                    include: [
                        {
                            model: Level,
                            as: 'Level',
                            attributes: ['level_id', 'name']
                        },
                        {
                            model: LO,
                            as: 'LO',
                            attributes: ['lo_id', 'name', 'description']
                        }
                    ]
                },
                {
                    model: User,
                    as: 'User',
                    attributes: ['user_id', 'name']
                }
            ],
            order: [['attempt_date', 'ASC']]
        });

        // Tính toán dữ liệu radar trung bình
        const radarData = calculateAverageRadarData(quiz.Questions, allQuestionHistory);

        res.status(200).json({
            quiz_id: quizId,
            radar_data: radarData
        });
    } catch (error) {
        console.error('Lỗi trong getAverageRadarData:', error);
        res.status(500).json({ error: 'Lỗi khi lấy dữ liệu radar trung bình', details: error.message });
    }
};

// API lấy dữ liệu radar chart của người xếp hạng 1
exports.getTopPerformerRadarData = async (req, res) => {
    try {
        const { quizId } = req.params;

        // Lấy leaderboard từ Firebase
        const QuizRealtimeService = require('../services/quizRealtimeService');
        const quizRealtimeService = new QuizRealtimeService();
        let leaderboard = await quizRealtimeService.getRealtimeLeaderboard(quizId);

        // Nếu không có leaderboard, fallback sang DB
        if (!leaderboard || leaderboard.length === 0) {
            // Lấy top scorer từ QuizResult
            const topResult = await QuizResult.findOne({
                where: { quiz_id: quizId },
                include: [{ model: User, as: 'Student', attributes: ['user_id', 'name'] }],
                order: [['score', 'DESC']]
            });
            if (!topResult) {
                return res.status(404).json({ error: 'Chưa có dữ liệu xếp hạng' });
            }
            leaderboard = [{
                user_id: topResult.user_id,
                name: topResult.Student?.name,
                score: topResult.score
            }];
        }

        const topPerformer = leaderboard[0];
        const topUserId = topPerformer.user_id;

        // Lấy lịch sử trả lời câu hỏi của người xếp hạng 1
        const topPerformerHistory = await UserQuestionHistory.findAll({
            where: {
                quiz_id: quizId,
                user_id: topUserId
            },
            include: [
                {
                    model: Question,
                    as: 'Question',
                    attributes: ['question_id', 'level_id', 'lo_id'],
                    include: [
                        { model: Level, as: 'Level', attributes: ['level_id', 'name'] },
                        { model: LO, as: 'LO', attributes: ['lo_id', 'name', 'description'] }
                    ]
                },
                {
                    model: User,
                    as: 'User',
                    attributes: ['user_id', 'name']
                }
            ],
            order: [['attempt_date', 'ASC']]
        });

        // Tính toán dữ liệu radar cho top performer
        const quiz = await Quiz.findByPk(quizId, {
            include: [{
                model: Question,
                as: 'Questions',
                through: { attributes: [] },
                attributes: ['question_id', 'question_text', 'level_id', 'lo_id'],
                include: [
                    { model: Level, as: 'Level', attributes: ['level_id', 'name'] },
                    { model: LO, as: 'LO', attributes: ['lo_id', 'name', 'description'] }
                ]
            }]
        });

        const radarData = calculateRadarData(quiz.Questions, topPerformerHistory);

        res.status(200).json({
            quiz_id: quizId,
            top_performer: {
                user_id: topUserId,
                name: topPerformer.name,
                score: topPerformer.score
            },
            radar_data: radarData
        });
    } catch (error) {
        console.error('Lỗi trong getTopPerformerRadarData:', error);
        res.status(500).json({ error: 'Lỗi khi lấy dữ liệu radar top performer', details: error.message });
    }
};

// Hàm tính toán dữ liệu radar cho một user
function calculateRadarData(questions, questionHistory) {
    console.log(`🔧 Calculating radar data for ${questionHistory.length} history records`);

    // Phân tích theo mức độ khó
    const difficultyAnalysis = {};
    const loAnalysis = {};
    let totalResponseTime = 0;
    let totalQuestions = 0;
    let correctAnswers = 0;
    let firstAttemptCorrect = 0;

    // Khởi tạo cấu trúc dữ liệu từ questions thực tế
    questions.forEach(question => {
        const levelName = question.Level.name.toLowerCase();
        const loName = question.LO.name;

        if (!difficultyAnalysis[levelName]) {
            difficultyAnalysis[levelName] = {
                total: 0,
                correct: 0,
                response_time: 0
            };
        }

        if (!loAnalysis[loName]) {
            loAnalysis[loName] = {
                total: 0,
                correct: 0,
                response_time: 0,
                description: question.LO.description || ''
            };
        }
    });

    console.log(`📋 Initialized analysis for ${Object.keys(difficultyAnalysis).length} difficulty levels and ${Object.keys(loAnalysis).length} LOs`);

    // Xử lý lịch sử câu trả lời
    questionHistory.forEach((history, index) => {
        const question = history.Question;
        if (!question) {
            console.log(`⚠️ History record ${index} has no Question data:`, history);
            return; // Skip nếu không có question data
        }

        if (!question.Level || !question.LO) {
            console.log(`⚠️ History record ${index} has incomplete Question data:`, {
                question_id: question.question_id,
                has_level: !!question.Level,
                has_lo: !!question.LO
            });
            return; // Skip nếu không có Level hoặc LO
        }

        const levelName = question.Level.name.toLowerCase();
        const loName = question.LO.name;

        // Cập nhật thống kê tổng thể
        totalQuestions++;
        totalResponseTime += history.time_spent || 0;

        if (history.is_correct) {
            correctAnswers++;
            if (difficultyAnalysis[levelName]) {
                difficultyAnalysis[levelName].correct++;
            }
            if (loAnalysis[loName]) {
                loAnalysis[loName].correct++;
            }
        }

        if (difficultyAnalysis[levelName]) {
            difficultyAnalysis[levelName].total++;
            difficultyAnalysis[levelName].response_time += history.time_spent || 0;
        }
        if (loAnalysis[loName]) {
            loAnalysis[loName].total++;
            loAnalysis[loName].response_time += history.time_spent || 0;
        }

        // Kiểm tra lần thử đầu tiên (giả sử mỗi câu hỏi chỉ có 1 lần trả lời)
        if (history.is_correct) {
            firstAttemptCorrect++;
        }
    });

    console.log(`📊 Processed ${totalQuestions} valid question records (${correctAnswers} correct)`);

    // Tính toán accuracy cho từng mức độ khó
    const difficultyLevels = {};
    Object.keys(difficultyAnalysis).forEach(level => {
        const data = difficultyAnalysis[level];
        difficultyLevels[level] = {
            accuracy: data.total > 0 ? Math.round((data.correct / data.total) * 100) : 0,
            questions_count: data.total,
            average_response_time: data.total > 0 ? Math.round(data.response_time / data.total) : 0
        };
    });

    // Tính toán accuracy cho từng LO
    const learningOutcomes = {};
    Object.keys(loAnalysis).forEach(lo => {
        const data = loAnalysis[lo];
        learningOutcomes[lo] = {
            accuracy: data.total > 0 ? Math.round((data.correct / data.total) * 100) : 0,
            questions_count: data.total,
            average_response_time: data.total > 0 ? Math.round(data.response_time / data.total) : 0,
            description: data.description || ''
        };
    });

    // Tính toán các metrics tổng thể
    const performanceMetrics = {
        average_response_time: totalQuestions > 0 ? Math.round(totalResponseTime / totalQuestions) : 0,
        completion_rate: 100, // Giả sử user đã hoàn thành quiz
        first_attempt_accuracy: totalQuestions > 0 ? Math.round((firstAttemptCorrect / totalQuestions) * 100) : 0,
        overall_accuracy: totalQuestions > 0 ? Math.round((correctAnswers / totalQuestions) * 100) : 0
    };

    console.log(`✅ Radar data calculation completed:`, {
        difficulty_levels: Object.keys(difficultyLevels).length,
        learning_outcomes: Object.keys(learningOutcomes).length,
        overall_accuracy: performanceMetrics.overall_accuracy
    });

    return {
        difficulty_levels: difficultyLevels,
        learning_outcomes: learningOutcomes,
        performance_metrics: performanceMetrics
    };
}

// Hàm tính toán dữ liệu radar trung bình
function calculateAverageRadarData(questions, allQuestionHistory) {
    // Nhóm dữ liệu theo user
    const userGroups = {};
    allQuestionHistory.forEach(history => {
        const userId = history.user_id;
        if (!userGroups[userId]) {
            userGroups[userId] = [];
        }
        userGroups[userId].push(history);
    });

    console.log(`Calculating average for ${Object.keys(userGroups).length} users`);

    // Tính toán radar data cho từng user
    const allUserRadarData = Object.values(userGroups).map(userHistory => {
        return calculateRadarData(questions, userHistory);
    });

    // Tính trung bình
    const averageRadarData = {
        difficulty_levels: {},
        learning_outcomes: {},
        performance_metrics: {}
    };

    // Tính trung bình cho difficulty levels
    const difficultyKeys = Object.keys(allUserRadarData[0]?.difficulty_levels || {});
    difficultyKeys.forEach(level => {
        const accuracies = allUserRadarData.map(data => data.difficulty_levels[level]?.accuracy || 0);
        const avgAccuracy = accuracies.length > 0 ? Math.round(accuracies.reduce((a, b) => a + b, 0) / accuracies.length) : 0;

        const responseTimes = allUserRadarData.map(data => data.difficulty_levels[level]?.average_response_time || 0);
        const avgResponseTime = responseTimes.length > 0 ? Math.round(responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length) : 0;

        averageRadarData.difficulty_levels[level] = {
            accuracy: avgAccuracy,
            questions_count: allUserRadarData[0]?.difficulty_levels[level]?.questions_count || 0,
            average_response_time: avgResponseTime
        };
    });

    // Tính trung bình cho learning outcomes
    const loKeys = Object.keys(allUserRadarData[0]?.learning_outcomes || {});
    loKeys.forEach(lo => {
        const accuracies = allUserRadarData.map(data => data.learning_outcomes[lo]?.accuracy || 0);
        const avgAccuracy = accuracies.length > 0 ? Math.round(accuracies.reduce((a, b) => a + b, 0) / accuracies.length) : 0;

        const responseTimes = allUserRadarData.map(data => data.learning_outcomes[lo]?.average_response_time || 0);
        const avgResponseTime = responseTimes.length > 0 ? Math.round(responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length) : 0;

        const descriptions = allUserRadarData.map(data => data.learning_outcomes[lo]?.description || '');
        const description = descriptions.find(d => d) || ''; // Lấy description đầu tiên không rỗng

        averageRadarData.learning_outcomes[lo] = {
            accuracy: avgAccuracy,
            questions_count: allUserRadarData[0]?.learning_outcomes[lo]?.questions_count || 0,
            average_response_time: avgResponseTime,
            description: description
        };
    });

    // Tính trung bình cho performance metrics
    const avgResponseTimes = allUserRadarData.map(data => data.performance_metrics.average_response_time);
    const avgFirstAttemptAccuracies = allUserRadarData.map(data => data.performance_metrics.first_attempt_accuracy);
    const avgOverallAccuracies = allUserRadarData.map(data => data.performance_metrics.overall_accuracy);

    averageRadarData.performance_metrics = {
        average_response_time: avgResponseTimes.length > 0 ? Math.round(avgResponseTimes.reduce((a, b) => a + b, 0) / avgResponseTimes.length) : 0,
        completion_rate: 100,
        first_attempt_accuracy: avgFirstAttemptAccuracies.length > 0 ? Math.round(avgFirstAttemptAccuracies.reduce((a, b) => a + b, 0) / avgFirstAttemptAccuracies.length) : 0,
        overall_accuracy: avgOverallAccuracies.length > 0 ? Math.round(avgOverallAccuracies.reduce((a, b) => a + b, 0) / avgOverallAccuracies.length) : 0
    };

    return averageRadarData;
}

// API tổng hợp lấy tất cả dữ liệu radar chart
exports.getAllRadarData = async (req, res) => {
    try {
        const { quizId } = req.params;
        const userId = req.user?.user_id;
        const userRole = req.roleName;

        // Kiểm tra quyền truy cập
        if (!['admin', 'teacher', 'student'].includes(userRole)) {
            return res.status(403).json({ error: 'Không có quyền truy cập' });
        }

        // Lấy thông tin quiz và câu hỏi với join đầy đủ
        const quiz = await Quiz.findByPk(quizId, {
            include: [
                {
                    model: Question,
                    as: 'Questions',
                    through: { attributes: [] },
                    attributes: ['question_id', 'question_text', 'level_id', 'lo_id'],
                    include: [
                        {
                            model: Level,
                            as: 'Level',
                            attributes: ['level_id', 'name']
                        },
                        {
                            model: LO,
                            as: 'LO',
                            attributes: ['lo_id', 'name', 'description']
                        }
                    ]
                }
            ]
        });

        if (!quiz) {
            return res.status(404).json({ error: 'Không tìm thấy quiz' });
        }

        // Lấy tất cả lịch sử trả lời câu hỏi của quiz này với join đầy đủ
        const allQuestionHistory = await UserQuestionHistory.findAll({
            where: { quiz_id: quizId },
            include: [
                {
                    model: Question,
                    as: 'Question',
                    attributes: ['question_id', 'question_text', 'level_id', 'lo_id'],
                    include: [
                        {
                            model: Level,
                            as: 'Level',
                            attributes: ['level_id', 'name']
                        },
                        {
                            model: LO,
                            as: 'LO',
                            attributes: ['lo_id', 'name', 'description']
                        }
                    ]
                },
                {
                    model: User,
                    as: 'User',
                    attributes: ['user_id', 'name']
                }
            ],
            order: [['attempt_date', 'ASC']]
        });

        // Debug: Log số lượng records
        console.log(`Quiz ${quizId}: Found ${allQuestionHistory.length} question history records`);
        console.log(`Quiz ${quizId}: Found ${quiz.Questions.length} questions`);

        const result = {
            quiz_id: quizId,
            quiz_name: quiz.name,
            total_questions: quiz.Questions.length,
            radar_data: {}
        };

        // 1. Dữ liệu trung bình của tất cả người tham gia (cho admin/teacher)
        if (['admin', 'teacher'].includes(userRole)) {
            result.radar_data.average = calculateAverageRadarData(quiz.Questions, allQuestionHistory);
        }

        // 2. Dữ liệu của người xếp hạng 1 (cho admin/teacher)
        if (['admin', 'teacher'].includes(userRole)) {
            try {
                const QuizRealtimeService = require('../services/quizRealtimeService');
                const quizRealtimeService = new QuizRealtimeService();
                const leaderboard = await quizRealtimeService.getRealtimeLeaderboard(quizId);

                if (leaderboard && leaderboard.length > 0) {
                    const topPerformer = leaderboard[0];
                    const topUserId = topPerformer.user_id;

                    const topPerformerHistory = allQuestionHistory.filter(history =>
                        history.user_id === topUserId
                    );

                    result.radar_data.top_performer = {
                        user_info: {
                            user_id: topUserId,
                            name: topPerformer.name,
                            score: topPerformer.score
                        },
                        data: calculateRadarData(quiz.Questions, topPerformerHistory)
                    };
                }
            } catch (error) {
                console.error('Lỗi khi lấy dữ liệu top performer:', error);
                result.radar_data.top_performer = null;
            }
        }

        // 3. Dữ liệu của người dùng hiện tại (cho student hoặc admin/teacher xem dữ liệu của mình)
        if (userId) {
            const currentUserHistory = allQuestionHistory.filter(history =>
                history.user_id === userId
            );

            console.log(`User ${userId}: Found ${currentUserHistory.length} history records`);

            if (currentUserHistory.length > 0) {
                result.radar_data.current_user = {
                    user_id: userId,
                    data: calculateRadarData(quiz.Questions, currentUserHistory)
                };
            }
        }

        // 4. Thêm thông tin tổng quan
        const uniqueUsers = [...new Set(allQuestionHistory.map(h => h.user_id))];

        // Tạo map learning outcomes với description
        const learningOutcomesMap = {};
        quiz.Questions.forEach(q => {
            if (!learningOutcomesMap[q.LO.name]) {
                learningOutcomesMap[q.LO.name] = {
                    name: q.LO.name,
                    description: q.LO.description || ''
                };
            }
        });

        result.summary = {
            total_participants: uniqueUsers.length,
            total_answers: allQuestionHistory.length,
            average_score: 0, // Có thể tính thêm nếu cần
            difficulty_levels: Object.keys(quiz.Questions.reduce((acc, q) => {
                acc[q.Level.name.toLowerCase()] = true;
                return acc;
            }, {})),
            learning_outcomes: Object.values(learningOutcomesMap)
        };

        // Debug: Log kết quả
        console.log('Radar data result:', JSON.stringify(result, null, 2));

        res.status(200).json(result);
    } catch (error) {
        console.error('Lỗi trong getAllRadarData:', error);
        res.status(500).json({ error: 'Lỗi khi lấy dữ liệu radar tổng hợp', details: error.message });
    }
};

// API: Lấy quiz result kèm chương và section theo từng LO
exports.getQuizResultWithChapters = async (req, res) => {
    try {
        const resultId = req.params.id;
        // Lấy quiz result và quiz
        const quizResult = await QuizResult.findByPk(resultId, {
            include: [
                { model: User, as: 'Student', attributes: ['user_id', 'name'] },
                { model: Quiz, as: 'Quiz', attributes: ['quiz_id', 'name'] }
            ]
        });
        if (!quizResult) return res.status(404).json({ message: 'QuizResult không tồn tại' });

        // Lấy tất cả câu hỏi của quiz này
        const quizQuestions = await Question.findAll({
            include: [
                { model: LO, as: 'LO', attributes: ['lo_id', 'name'] }
            ],
            where: { quiz_id: quizResult.quiz_id }
        });

        // Lấy tất cả LO duy nhất
        const loMap = {};
        quizQuestions.forEach(q => {
            if (q.lo_id && q.LO) {
                loMap[q.lo_id] = q.LO.name;
            }
        });
        const loIds = Object.keys(loMap);

        // Với mỗi LO, lấy chương và section
        const loChapters = [];
        for (const loId of loIds) {
            // Lấy các chương liên kết với LO này
            const chapterLOs = await ChapterLO.findAll({ where: { lo_id: loId } });
            const chapterIds = chapterLOs.map(clo => clo.chapter_id);

            // Lấy thông tin chương và section
            const chapters = await Chapter.findAll({
                where: { chapter_id: chapterIds },
                include: [
                    {
                        model: ChapterSection,
                        as: 'Sections',
                        attributes: ['section_id', 'title', 'content', 'order']
                    }
                ]
            });

            loChapters.push({
                lo_id: loId,
                lo_name: loMap[loId],
                chapters: chapters.map(chap => ({
                    chapter_id: chap.chapter_id,
                    chapter_name: chap.name,
                    sections: chap.Sections
                }))
            });
        }

        res.status(200).json({
            quiz_result: quizResult,
            lo_chapters: loChapters
        });
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi lấy quiz result kèm chương/section', error: error.message });
    }
};

// API: Lấy quiz result theo quiz_id và user_id
exports.getQuizResultByQuizAndUser = async (req, res) => {
    try {
        const { quiz_id, user_id } = req.query;
        if (!quiz_id || !user_id) {
            return res.status(400).json({ message: 'Thiếu quiz_id hoặc user_id' });
        }
        const quizResult = await QuizResult.findOne({
            where: { quiz_id, user_id },
            include: [
                { model: User, as: 'Student', attributes: ['user_id', 'name'] },
                { model: Quiz, as: 'Quiz', attributes: ['quiz_id', 'name'] }
            ]
        });
        if (!quizResult) return res.status(404).json({ message: 'QuizResult không tồn tại' });
        res.status(200).json(quizResult);
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi lấy quiz result theo quiz_id và user_id', error: error.message });
    }
};

// API: Lấy quiz result chi tiết kèm chương/section theo quiz_id và user_id
exports.getQuizResultWithChaptersByQuizAndUser = async (req, res) => {
    try {
        const { quiz_id, user_id } = req.query;
        if (!quiz_id || !user_id) {
            return res.status(400).json({ message: 'Thiếu quiz_id hoặc user_id' });
        }
        // Lấy quiz result
        const quizResult = await QuizResult.findOne({
            where: { quiz_id, user_id },
            include: [
                { model: User, as: 'Student', attributes: ['user_id', 'name'] },
                { model: Quiz, as: 'Quiz', attributes: ['quiz_id', 'name'] }
            ]
        });
        if (!quizResult) return res.status(404).json({ message: 'QuizResult không tồn tại' });

        // Lấy tất cả câu hỏi của quiz này
        const quizQuestions = await Question.findAll({
            include: [
                { model: LO, as: 'LO', attributes: ['lo_id', 'name'] }
            ],
            where: { quiz_id }
        });
        // Lấy tất cả LO duy nhất
        const loMap = {};
        quizQuestions.forEach(q => {
            if (q.lo_id && q.LO) {
                loMap[q.lo_id] = q.LO.name;
            }
        });
        const loIds = Object.keys(loMap);
        // Với mỗi LO, lấy chương và section
        const loChapters = [];
        for (const loId of loIds) {
            const chapterLOs = await ChapterLO.findAll({ where: { lo_id: loId } });
            const chapterIds = chapterLOs.map(clo => clo.chapter_id);
            const chapters = await Chapter.findAll({
                where: { chapter_id: chapterIds },
                include: [
                    {
                        model: ChapterSection,
                        as: 'Sections',
                        attributes: ['section_id', 'title', 'content', 'order']
                    }
                ]
            });
            loChapters.push({
                lo_id: loId,
                lo_name: loMap[loId],
                chapters: chapters.map(chap => ({
                    chapter_id: chap.chapter_id,
                    chapter_name: chap.name,
                    sections: chap.Sections
                }))
            });
        }
        res.status(200).json({
            quiz_result: quizResult,
            lo_chapters: loChapters
        });
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi lấy quiz result kèm chương/section theo quiz_id và user_id', error: error.message });
    }
};

// API: Đề xuất điểm yếu theo LO và hiển thị chương liên quan
exports.getWeakestLOWithChapters = async (req, res) => {
    try {
        const { quiz_id, user_id } = req.query;
        if (!quiz_id || !user_id) {
            return res.status(400).json({ message: 'Thiếu quiz_id hoặc user_id' });
        }
        const { UserQuestionHistory, Question, LO, ChapterLO, Chapter, ChapterSection } = require('../models');
        // Lấy lịch sử trả lời của user trong quiz
        const histories = await UserQuestionHistory.findAll({
            where: { quiz_id, user_id },
            include: [
                { model: Question, as: 'Question', attributes: ['lo_id'], include: [{ model: LO, as: 'LO', attributes: ['lo_id', 'name'] }] }
            ]
        });
        // Gom nhóm theo lo_id
        const loStats = {};
        histories.forEach(h => {
            const lo_id = h.Question?.lo_id;
            const lo_name = h.Question?.LO?.name || '';
            if (!lo_id) return;
            if (!loStats[lo_id]) {
                loStats[lo_id] = { total: 0, correct: 0, name: lo_name };
            }
            loStats[lo_id].total++;
            if (h.is_correct) loStats[lo_id].correct++;
        });
        // Tìm LO yếu nhất (tỉ lệ đúng thấp nhất, nhưng phải có ít nhất 1 câu)
        let weakest = null;
        Object.entries(loStats).forEach(([lo_id, stat]) => {
            const accuracy = stat.total > 0 ? (stat.correct / stat.total) * 100 : 0;
            if (!weakest || accuracy < weakest.accuracy) {
                weakest = { lo_id, lo_name: stat.name, accuracy };
            }
        });
        if (!weakest) {
            return res.status(200).json({ message: 'Không có dữ liệu LO cho user này.' });
        }
        // Lấy chương và section liên quan đến LO yếu nhất
        const chapterLOs = await ChapterLO.findAll({ where: { lo_id: weakest.lo_id } });
        const chapterIds = chapterLOs.map(clo => clo.chapter_id);
        const chapters = await Chapter.findAll({
            where: { chapter_id: chapterIds },
            include: [
                {
                    model: ChapterSection,
                    as: 'Sections',
                    attributes: ['section_id', 'title', 'content', 'order']
                }
            ]
        });
        weakest.chapters = chapters.map(chap => ({
            chapter_id: chap.chapter_id,
            chapter_name: chap.name,
            sections: chap.Sections
        }));
        res.status(200).json({
            user_id,
            quiz_id,
            weakest_lo: weakest
        });
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi đề xuất điểm yếu theo LO', error: error.message });
    }
};

// API endpoint để phân tích cấp độ và chương cần cải thiện
exports.getImprovementAnalysis = async (req, res) => {
    try {
        const { quiz_id, user_id, subject_id } = req.query;

        if (!quiz_id && !subject_id) {
            return res.status(400).json({
                message: 'Cần cung cấp quiz_id hoặc subject_id để phân tích'
            });
        }

        let analysisResult;

        if (quiz_id) {
            // Phân tích cho một quiz cụ thể
            analysisResult = await analyzeQuizImprovement(quiz_id, user_id);
        } else {
            // Phân tích cho toàn bộ subject
            analysisResult = await analyzeSubjectImprovement(subject_id, user_id);
        }

        res.status(200).json({
            success: true,
            data: analysisResult,
            generated_at: new Date()
        });

    } catch (error) {
        console.error('Lỗi khi phân tích cải thiện:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi khi phân tích dữ liệu cải thiện',
            error: error.message
        });
    }
};

// Hàm phân tích cải thiện cho một quiz cụ thể
async function analyzeQuizImprovement(quizId, userId = null) {
    const quiz = await Quiz.findByPk(quizId, {
        include: [{
            model: Question,
            as: 'Questions',
            through: { attributes: [] },
            attributes: ['question_id', 'question_text', 'level_id', 'lo_id'],
            include: [
                { model: Level, as: 'Level', attributes: ['level_id', 'name'] },
                {
                    model: LO,
                    as: 'LO',
                    attributes: ['lo_id', 'name', 'description'],
                    include: [{
                        model: Chapter,
                        as: 'Chapters',
                        through: { attributes: [] },
                        attributes: ['chapter_id', 'name', 'description', 'subject_id'],
                        include: [{
                            model: Subject,
                            as: 'Subject',
                            attributes: ['subject_id', 'name']
                        }]
                    }]
                }
            ]
        }]
    });

    if (!quiz) {
        throw new Error('Quiz không tồn tại');
    }

    // Lấy lịch sử trả lời câu hỏi
    const whereCondition = { quiz_id: quizId };
    if (userId) {
        whereCondition.user_id = userId;
    }

    const questionHistory = await UserQuestionHistory.findAll({
        where: whereCondition,
        include: [
            {
                model: Question,
                as: 'Question',
                attributes: ['question_id', 'level_id', 'lo_id'],
                include: [
                    { model: Level, as: 'Level', attributes: ['level_id', 'name'] },
                    { model: LO, as: 'LO', attributes: ['lo_id', 'name', 'description'] }
                ]
            },
            {
                model: User,
                as: 'User',
                attributes: ['user_id', 'name']
            }
        ],
        order: [['attempt_date', 'ASC']]
    });

    // Phân tích cấp độ yếu
    const weakLevelsAnalysis = calculateWeakLevelsAnalysis(quiz.Questions, questionHistory);

    // Phân tích chương cần cải thiện
    const chapterAnalysis = calculateChapterImprovementAnalysis(quiz.Questions, questionHistory);

    // Tạo gợi ý cải thiện
    const suggestions = generateImprovementSuggestions(weakLevelsAnalysis, chapterAnalysis);

    return {
        quiz_info: {
            quiz_id: quizId,
            quiz_name: quiz.name,
            total_questions: quiz.Questions.length
        },
        weak_levels: weakLevelsAnalysis,
        chapters_need_improvement: chapterAnalysis,
        improvement_suggestions: suggestions,
        analysis_scope: userId ? 'individual' : 'all_participants'
    };
}

// Hàm phân tích cấp độ yếu
function calculateWeakLevelsAnalysis(questions, questionHistory) {
    const levelStats = {};

    // Khởi tạo thống kê cho tất cả levels có trong quiz
    questions.forEach(question => {
        const levelName = question.Level.name.toLowerCase();
        if (!levelStats[levelName]) {
            levelStats[levelName] = {
                level_name: question.Level.name,
                total_questions: 0,
                total_attempts: 0,
                correct_attempts: 0,
                total_time: 0,
                questions_list: []
            };
        }
        levelStats[levelName].total_questions++;
        levelStats[levelName].questions_list.push({
            question_id: question.question_id,
            question_text: question.question_text
        });
    });

    // Phân tích lịch sử trả lời
    questionHistory.forEach(history => {
        const question = history.Question;
        if (!question || !question.Level) return;

        const levelName = question.Level.name.toLowerCase();
        if (levelStats[levelName]) {
            levelStats[levelName].total_attempts++;
            levelStats[levelName].total_time += history.time_spent || 0;

            if (history.is_correct) {
                levelStats[levelName].correct_attempts++;
            }
        }
    });

    // Tính toán accuracy và xếp hạng
    const levelAnalysis = Object.keys(levelStats).map(levelKey => {
        const stats = levelStats[levelKey];
        const accuracy = stats.total_attempts > 0 ?
            Math.round((stats.correct_attempts / stats.total_attempts) * 100) : 0;
        const avgTime = stats.total_attempts > 0 ?
            Math.round(stats.total_time / stats.total_attempts) : 0;

        return {
            level: stats.level_name,
            accuracy: accuracy,
            total_questions: stats.total_questions,
            total_attempts: stats.total_attempts,
            correct_attempts: stats.correct_attempts,
            average_time: avgTime,
            questions_list: stats.questions_list,
            improvement_priority: accuracy < 50 ? 'high' : accuracy < 70 ? 'medium' : 'low'
        };
    });

    // Sắp xếp theo accuracy tăng dần (yếu nhất trước)
    levelAnalysis.sort((a, b) => a.accuracy - b.accuracy);

    return {
        levels_analysis: levelAnalysis,
        weakest_level: levelAnalysis[0] || null,
        summary: {
            total_levels: levelAnalysis.length,
            levels_need_improvement: levelAnalysis.filter(l => l.improvement_priority !== 'low').length
        }
    };
}

// Hàm phân tích chương cần cải thiện dựa trên LO performance
function calculateChapterImprovementAnalysis(questions, questionHistory) {
    const loStats = {};
    const chapterStats = {};

    // Khởi tạo thống kê cho tất cả LOs và Chapters
    questions.forEach(question => {
        const loName = question.LO.name;
        const loId = question.LO.lo_id;

        // Thống kê LO
        if (!loStats[loName]) {
            loStats[loName] = {
                lo_id: loId,
                lo_name: loName,
                description: question.LO.description || '',
                total_questions: 0,
                total_attempts: 0,
                correct_attempts: 0,
                total_time: 0,
                chapters: new Set()
            };
        }
        loStats[loName].total_questions++;

        // Thêm chapters liên quan đến LO này
        if (question.LO.Chapters) {
            question.LO.Chapters.forEach(chapter => {
                loStats[loName].chapters.add(chapter.chapter_id);

                // Thống kê Chapter
                if (!chapterStats[chapter.chapter_id]) {
                    chapterStats[chapter.chapter_id] = {
                        chapter_id: chapter.chapter_id,
                        chapter_name: chapter.name,
                        description: chapter.description || '',
                        subject_id: chapter.subject_id,
                        subject_name: chapter.Subject ? chapter.Subject.name : '',
                        los: new Set(),
                        total_questions: 0,
                        total_attempts: 0,
                        correct_attempts: 0,
                        total_time: 0
                    };
                }
                chapterStats[chapter.chapter_id].los.add(loName);
                chapterStats[chapter.chapter_id].total_questions++;
            });
        }
    });

    // Phân tích lịch sử trả lời
    questionHistory.forEach(history => {
        const question = history.Question;
        if (!question || !question.LO) return;

        const loName = question.LO.name;
        if (loStats[loName]) {
            loStats[loName].total_attempts++;
            loStats[loName].total_time += history.time_spent || 0;

            if (history.is_correct) {
                loStats[loName].correct_attempts++;
            }
        }
    });

    // Tính toán accuracy cho LOs
    const loAnalysis = Object.keys(loStats).map(loKey => {
        const stats = loStats[loKey];
        const accuracy = stats.total_attempts > 0 ?
            Math.round((stats.correct_attempts / stats.total_attempts) * 100) : 0;
        const avgTime = stats.total_attempts > 0 ?
            Math.round(stats.total_time / stats.total_attempts) : 0;

        return {
            lo_id: stats.lo_id,
            lo_name: stats.lo_name,
            description: stats.description,
            accuracy: accuracy,
            total_questions: stats.total_questions,
            total_attempts: stats.total_attempts,
            correct_attempts: stats.correct_attempts,
            average_time: avgTime,
            chapters: Array.from(stats.chapters),
            improvement_priority: accuracy < 50 ? 'high' : accuracy < 70 ? 'medium' : 'low'
        };
    });

    // Tính toán accuracy cho Chapters dựa trên LOs
    Object.keys(chapterStats).forEach(chapterId => {
        const chapterStat = chapterStats[chapterId];
        const chapterLOs = Array.from(chapterStat.los);

        let totalAccuracy = 0;
        let totalAttempts = 0;
        let correctAttempts = 0;
        let totalTime = 0;
        let validLOCount = 0;

        chapterLOs.forEach(loName => {
            const loStat = loStats[loName];
            if (loStat && loStat.total_attempts > 0) {
                totalAttempts += loStat.total_attempts;
                correctAttempts += loStat.correct_attempts;
                totalTime += loStat.total_time;
                validLOCount++;
            }
        });

        chapterStat.total_attempts = totalAttempts;
        chapterStat.correct_attempts = correctAttempts;
        chapterStat.total_time = totalTime;
        chapterStat.accuracy = totalAttempts > 0 ?
            Math.round((correctAttempts / totalAttempts) * 100) : 0;
        chapterStat.average_time = totalAttempts > 0 ?
            Math.round(totalTime / totalAttempts) : 0;
        chapterStat.los = chapterLOs;
        chapterStat.improvement_priority = chapterStat.accuracy < 50 ? 'high' :
            chapterStat.accuracy < 70 ? 'medium' : 'low';
    });

    // Chuyển đổi chapterStats thành array và sắp xếp
    const chapterAnalysis = Object.values(chapterStats)
        .filter(chapter => chapter.total_attempts > 0)
        .sort((a, b) => a.accuracy - b.accuracy);

    // Sắp xếp LO analysis theo accuracy
    loAnalysis.sort((a, b) => a.accuracy - b.accuracy);

    return {
        lo_analysis: loAnalysis,
        chapter_analysis: chapterAnalysis,
        weakest_los: loAnalysis.filter(lo => lo.improvement_priority !== 'low').slice(0, 5),
        chapters_need_improvement: chapterAnalysis.filter(ch => ch.improvement_priority !== 'low').slice(0, 5),
        summary: {
            total_los: loAnalysis.length,
            total_chapters: chapterAnalysis.length,
            los_need_improvement: loAnalysis.filter(lo => lo.improvement_priority !== 'low').length,
            chapters_need_improvement: chapterAnalysis.filter(ch => ch.improvement_priority !== 'low').length
        }
    };
}

// Hàm tạo gợi ý cải thiện
function generateImprovementSuggestions(weakLevelsAnalysis, chapterAnalysis) {
    const suggestions = {
        level_suggestions: [],
        chapter_suggestions: [],
        general_recommendations: [],
        priority_actions: []
    };

    // Gợi ý cho cấp độ yếu
    if (weakLevelsAnalysis.weakest_level) {
        const weakestLevel = weakLevelsAnalysis.weakest_level;

        suggestions.level_suggestions.push({
            level: weakestLevel.level,
            accuracy: weakestLevel.accuracy,
            priority: weakestLevel.improvement_priority,
            suggestions: generateLevelSpecificSuggestions(weakestLevel)
        });

        // Thêm gợi ý cho các level khác cần cải thiện
        weakLevelsAnalysis.levels_analysis
            .filter(level => level.improvement_priority !== 'low' && level.level !== weakestLevel.level)
            .slice(0, 2)
            .forEach(level => {
                suggestions.level_suggestions.push({
                    level: level.level,
                    accuracy: level.accuracy,
                    priority: level.improvement_priority,
                    suggestions: generateLevelSpecificSuggestions(level)
                });
            });
    }

    // Gợi ý cho chương cần cải thiện
    chapterAnalysis.chapters_need_improvement.forEach(chapter => {
        suggestions.chapter_suggestions.push({
            chapter_id: chapter.chapter_id,
            chapter_name: chapter.chapter_name,
            subject_name: chapter.subject_name,
            accuracy: chapter.accuracy,
            priority: chapter.improvement_priority,
            weak_los: chapterAnalysis.lo_analysis
                .filter(lo => lo.improvement_priority !== 'low' && chapter.los.includes(lo.lo_name))
                .slice(0, 3),
            suggestions: generateChapterSpecificSuggestions(chapter)
        });
    });

    // Gợi ý chung
    suggestions.general_recommendations = generateGeneralRecommendations(
        weakLevelsAnalysis,
        chapterAnalysis
    );

    // Hành động ưu tiên
    suggestions.priority_actions = generatePriorityActions(
        weakLevelsAnalysis,
        chapterAnalysis
    );

    return suggestions;
}

// Gợi ý cụ thể cho từng cấp độ
function generateLevelSpecificSuggestions(levelData) {
    const suggestions = [];

    if (levelData.level.toLowerCase() === 'easy') {
        if (levelData.accuracy < 70) {
            suggestions.push("Ôn tập lại các khái niệm cơ bản");
            suggestions.push("Làm thêm bài tập dễ để củng cố nền tảng");
            suggestions.push("Đọc lại giáo trình phần lý thuyết cơ bản");
        }
    } else if (levelData.level.toLowerCase() === 'medium') {
        if (levelData.accuracy < 60) {
            suggestions.push("Luyện tập thêm các bài tập trung bình");
            suggestions.push("Tham gia thảo luận nhóm để hiểu sâu hơn");
            suggestions.push("Xem lại các ví dụ minh họa trong bài giảng");
        }
    } else if (levelData.level.toLowerCase() === 'hard') {
        if (levelData.accuracy < 50) {
            suggestions.push("Tập trung vào việc hiểu bản chất vấn đề");
            suggestions.push("Tìm kiếm sự hỗ trợ từ giảng viên");
            suggestions.push("Nghiên cứu thêm tài liệu tham khảo nâng cao");
            suggestions.push("Thực hành nhiều bài tập phức tạp");
        }
    }

    if (levelData.average_time > 120) { // > 2 phút
        suggestions.push("Luyện tập để cải thiện tốc độ giải bài");
    }

    return suggestions;
}

// Gợi ý cụ thể cho từng chương
function generateChapterSpecificSuggestions(chapterData) {
    const suggestions = [];

    if (chapterData.accuracy < 50) {
        suggestions.push(`Ôn tập lại toàn bộ chương "${chapterData.chapter_name}"`);
        suggestions.push("Đọc kỹ lại giáo trình và ghi chú bài giảng");
        suggestions.push("Làm lại các bài tập trong chương");
    } else if (chapterData.accuracy < 70) {
        suggestions.push(`Tập trung vào các phần khó trong chương "${chapterData.chapter_name}"`);
        suggestions.push("Làm thêm bài tập nâng cao");
    }

    suggestions.push("Tham khảo thêm tài liệu bổ sung cho chương này");
    suggestions.push("Thảo luận với bạn học hoặc giảng viên về những điểm chưa rõ");

    return suggestions;
}

// Gợi ý chung
function generateGeneralRecommendations(weakLevelsAnalysis, chapterAnalysis) {
    const recommendations = [];

    const totalLevelsNeedImprovement = weakLevelsAnalysis.summary.levels_need_improvement;
    const totalChaptersNeedImprovement = chapterAnalysis.summary.chapters_need_improvement;

    if (totalLevelsNeedImprovement > 1) {
        recommendations.push("Cần có kế hoạch học tập có hệ thống từ cơ bản đến nâng cao");
    }

    if (totalChaptersNeedImprovement > 2) {
        recommendations.push("Nên ôn tập lại từ đầu các chương có điểm yếu");
        recommendations.push("Tham gia các buổi học bổ trợ nếu có");
    }

    recommendations.push("Thường xuyên làm bài kiểm tra để đánh giá tiến độ");
    recommendations.push("Tạo lập kế hoạch học tập chi tiết theo tuần");

    return recommendations;
}

// Hành động ưu tiên
function generatePriorityActions(weakLevelsAnalysis, chapterAnalysis) {
    const actions = [];

    // Ưu tiên cao nhất: Level và Chapter có accuracy thấp nhất
    if (weakLevelsAnalysis.weakest_level && weakLevelsAnalysis.weakest_level.accuracy < 50) {
        actions.push({
            priority: 1,
            action: `Tập trung cải thiện cấp độ ${weakLevelsAnalysis.weakest_level.level}`,
            reason: `Accuracy chỉ ${weakLevelsAnalysis.weakest_level.accuracy}%`,
            timeline: "1-2 tuần"
        });
    }

    const weakestChapter = chapterAnalysis.chapters_need_improvement[0];
    if (weakestChapter && weakestChapter.accuracy < 50) {
        actions.push({
            priority: 2,
            action: `Ôn tập lại chương "${weakestChapter.chapter_name}"`,
            reason: `Accuracy chỉ ${weakestChapter.accuracy}%`,
            timeline: "2-3 tuần"
        });
    }

    // Ưu tiên trung bình: Các LO yếu
    const weakLOs = chapterAnalysis.weakest_los.slice(0, 2);
    weakLOs.forEach((lo, index) => {
        actions.push({
            priority: 3 + index,
            action: `Tăng cường luyện tập LO: ${lo.lo_name}`,
            reason: `Accuracy ${lo.accuracy}%`,
            timeline: "1 tuần"
        });
    });

    return actions.slice(0, 5); // Giới hạn 5 hành động ưu tiên
}

// Hàm phân tích cải thiện cho toàn bộ subject
async function analyzeSubjectImprovement(subjectId, userId = null) {
    // Lấy tất cả quiz trong subject
    const quizzes = await Quiz.findAll({
        where: { subject_id: subjectId },
        include: [{
            model: Question,
            as: 'Questions',
            through: { attributes: [] },
            attributes: ['question_id', 'question_text', 'level_id', 'lo_id'],
            include: [
                { model: Level, as: 'Level', attributes: ['level_id', 'name'] },
                {
                    model: LO,
                    as: 'LO',
                    attributes: ['lo_id', 'name', 'description'],
                    include: [{
                        model: Chapter,
                        as: 'Chapters',
                        through: { attributes: [] },
                        attributes: ['chapter_id', 'name', 'description', 'subject_id'],
                        include: [{
                            model: Subject,
                            as: 'Subject',
                            attributes: ['subject_id', 'name']
                        }]
                    }]
                }
            ]
        }]
    });

    if (!quizzes.length) {
        throw new Error('Không tìm thấy quiz nào trong subject này');
    }

    // Lấy tất cả questions từ các quiz
    const allQuestions = [];
    const quizIds = [];
    quizzes.forEach(quiz => {
        quizIds.push(quiz.quiz_id);
        allQuestions.push(...quiz.Questions);
    });

    // Lấy lịch sử trả lời câu hỏi cho tất cả quiz trong subject
    const whereCondition = { quiz_id: { [Op.in]: quizIds } };
    if (userId) {
        whereCondition.user_id = userId;
    }

    const questionHistory = await UserQuestionHistory.findAll({
        where: whereCondition,
        include: [
            {
                model: Question,
                as: 'Question',
                attributes: ['question_id', 'level_id', 'lo_id'],
                include: [
                    { model: Level, as: 'Level', attributes: ['level_id', 'name'] },
                    { model: LO, as: 'LO', attributes: ['lo_id', 'name', 'description'] }
                ]
            },
            {
                model: User,
                as: 'User',
                attributes: ['user_id', 'name']
            }
        ],
        order: [['attempt_date', 'ASC']]
    });

    // Phân tích cấp độ yếu
    const weakLevelsAnalysis = calculateWeakLevelsAnalysis(allQuestions, questionHistory);

    // Phân tích chương cần cải thiện
    const chapterAnalysis = calculateChapterImprovementAnalysis(allQuestions, questionHistory);

    // Tạo gợi ý cải thiện
    const suggestions = generateImprovementSuggestions(weakLevelsAnalysis, chapterAnalysis);

    // Lấy thông tin subject
    const subject = await Subject.findByPk(subjectId);

    return {
        subject_info: {
            subject_id: subjectId,
            subject_name: subject ? subject.name : 'Unknown',
            total_quizzes: quizzes.length,
            total_questions: allQuestions.length
        },
        weak_levels: weakLevelsAnalysis,
        chapters_need_improvement: chapterAnalysis,
        improvement_suggestions: suggestions,
        analysis_scope: userId ? 'individual' : 'all_participants'
    };
}

// Export function để sử dụng trong adaptive quiz service
exports.analyzeSubjectImprovement = analyzeSubjectImprovement;