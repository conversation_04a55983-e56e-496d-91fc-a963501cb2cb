/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { useState, useEffect, useCallback } from "react";
import {
  gamificationService,
  UserGamificationInfo,
  LeaderboardEntry,
} from "@/services/api/gamification.service";

interface UseGamificationReturn {
  // Data
  userGamification: UserGamificationInfo | null;
  leaderboard: LeaderboardEntry[];

  // Loading states
  isLoading: boolean;
  isLeaderboardLoading: boolean;

  // Error states
  error: string | null;
  leaderboardError: string | null;

  // Actions
  fetchUserGamification: () => Promise<void>;
  fetchLeaderboard: (limit?: number, timeframe?: string) => Promise<void>;
  refreshData: () => Promise<void>;

  // Computed values
  levelProgress: number;
  levelName: string;
  levelColor: string;
  formattedPoints: string;
  accuracyRate: number;
}

export const useGamification = (): UseGamificationReturn => {
  const [userGamification, setUserGamification] =
    useState<UserGamificationInfo | null>(null);
  const [leaderboard, setLeaderboard] = useState<LeaderboardEntry[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isLeaderboardLoading, setIsLeaderboardLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [leaderboardError, setLeaderboardError] = useState<string | null>(null);

  // Fetch user gamification data
  const fetchUserGamification = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const data = await gamificationService.getCurrentUserGamification();
      setUserGamification(data);
    } catch (err: any) {
      const errorMessage =
        err.response?.data?.message || "Không thể tải thông tin gamification";
      setError(errorMessage);
      console.error("Error fetching user gamification:", err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Fetch leaderboard
  const fetchLeaderboard = useCallback(
    async (limit: number = 10, timeframe: string = "all") => {
      try {
        setIsLeaderboardLoading(true);
        setLeaderboardError(null);

        const data = await gamificationService.getLeaderboard(limit, timeframe);
        setLeaderboard(data);
      } catch (err: any) {
        const errorMessage =
          err.response?.data?.message || "Không thể tải bảng xếp hạng";
        setLeaderboardError(errorMessage);
        console.error("Error fetching leaderboard:", err);
      } finally {
        setIsLeaderboardLoading(false);
      }
    },
    []
  );

  // Refresh all data
  const refreshData = useCallback(async () => {
    await Promise.all([fetchUserGamification(), fetchLeaderboard()]);
  }, [fetchUserGamification, fetchLeaderboard]);

  // Computed values
  const levelProgress = userGamification
    ? gamificationService.calculateLevelProgress(
        userGamification.experience_points
      )
    : 0;

  const levelName = userGamification
    ? gamificationService.getLevelName(userGamification.current_level)
    : "";

  const levelColor = userGamification
    ? gamificationService.getLevelColor(userGamification.current_level)
    : "#9CA3AF";

  const formattedPoints = userGamification
    ? gamificationService.formatPoints(userGamification.total_points)
    : "0";

  const accuracyRate = userGamification
    ? gamificationService.calculateAccuracyRate(
        userGamification.stats.total_correct_answers,
        userGamification.stats.total_questions_answered
      )
    : 0;

  // Auto-fetch on mount
  useEffect(() => {
    fetchUserGamification();
    fetchLeaderboard();
  }, [fetchUserGamification, fetchLeaderboard]);

  return {
    // Data
    userGamification,
    leaderboard,

    // Loading states
    isLoading,
    isLeaderboardLoading,

    // Error states
    error,
    leaderboardError,

    // Actions
    fetchUserGamification,
    fetchLeaderboard,
    refreshData,

    // Computed values
    levelProgress,
    levelName,
    levelColor,
    formattedPoints,
    accuracyRate,
  };
};

// Hook for admin gamification stats
interface UseGamificationStatsReturn {
  stats: any;
  isLoading: boolean;
  error: string | null;
  fetchStats: () => Promise<void>;
}

export const useGamificationStats = (): UseGamificationStatsReturn => {
  const [stats, setStats] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchStats = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const data = await gamificationService.getGamificationStats();
      setStats(data);
    } catch (err: any) {
      const errorMessage =
        err.response?.data?.message || "Không thể tải thống kê gamification";
      setError(errorMessage);
      console.error("Error fetching gamification stats:", err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchStats();
  }, [fetchStats]);

  return {
    stats,
    isLoading,
    error,
    fetchStats,
  };
};

export default useGamification;
