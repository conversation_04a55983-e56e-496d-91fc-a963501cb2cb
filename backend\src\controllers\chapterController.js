const { Chapter, Subject, LO, ChapterLO, ChapterSection, Question, sequelize } = require('../models');
const { Op } = require('sequelize');

// Get all chapters with pagination
exports.getAllChapters = async (req, res) => {
    try {
        const { page = 1, limit = 10, subject_id } = req.query;
        const offset = (page - 1) * limit;

        let whereClause = {};
        if (subject_id) {
            whereClause.subject_id = subject_id;
        }

        const chapters = await Chapter.findAndCountAll({
            where: whereClause,
            limit: parseInt(limit),
            offset: parseInt(offset),
            include: [
                {
                    model: Subject,
                    as: 'Subject',
                    attributes: ['subject_id', 'name']
                },
                {
                    model: LO,
                    as: 'LOs',
                    attributes: ['lo_id', 'name', 'description'],
                    through: { attributes: [] }
                },
                {
                    model: ChapterSection,
                    as: 'Sections',
                    attributes: ['section_id', 'name', 'content']
                }
            ],
            order: [['chapter_id', 'ASC']]
        });

        res.status(200).json({
            totalItems: chapters.count,
            totalPages: Math.ceil(chapters.count / limit),
            currentPage: parseInt(page),
            chapters: chapters.rows,
        });
    } catch (error) {
        console.error('Error getting chapters:', error);
        res.status(500).json({ message: 'Error getting chapters', error: error.message });
    }
};

// Get chapter by ID
exports.getChapterById = async (req, res) => {
    try {
        const { id } = req.params;

        const chapter = await Chapter.findByPk(id, {
            include: [
                {
                    model: Subject,
                    as: 'Subject',
                    attributes: ['subject_id', 'name', 'description']
                },
                {
                    model: LO,
                    as: 'LOs',
                    attributes: ['lo_id', 'name', 'description'],
                    through: { attributes: [] }
                },
                {
                    model: ChapterSection,
                    as: 'Sections',
                    attributes: ['section_id', 'name', 'content', 'order_index']
                }
            ]
        });

        if (!chapter) {
            return res.status(404).json({ message: 'Chapter not found' });
        }

        res.status(200).json(chapter);
    } catch (error) {
        console.error('Error getting chapter:', error);
        res.status(500).json({ message: 'Error getting chapter', error: error.message });
    }
};

// Get chapters by subject
exports.getChaptersBySubject = async (req, res) => {
    try {
        const { subject_id } = req.params;
        const { page = 1, limit = 10 } = req.query;
        const offset = (page - 1) * limit;

        const chapters = await Chapter.findAndCountAll({
            where: { subject_id },
            limit: parseInt(limit),
            offset: parseInt(offset),
            include: [
                {
                    model: LO,
                    as: 'LOs',
                    attributes: ['lo_id', 'name'],
                    through: { attributes: [] }
                }
            ],
            order: [['chapter_id', 'ASC']]
        });

        res.status(200).json({
            totalItems: chapters.count,
            totalPages: Math.ceil(chapters.count / limit),
            currentPage: parseInt(page),
            chapters: chapters.rows,
        });
    } catch (error) {
        console.error('Error getting chapters by subject:', error);
        res.status(500).json({ message: 'Error getting chapters by subject', error: error.message });
    }
};

// Create new chapter
exports.createChapter = async (req, res) => {
    const transaction = await sequelize.transaction();
    try {
        const { name, description, subject_id, lo_ids, sections } = req.body;

        if (!name || !subject_id) {
            await transaction.rollback();
            return res.status(400).json({ message: 'Name and subject_id are required' });
        }

        // Check if subject exists
        const subject = await Subject.findByPk(subject_id, { transaction });
        if (!subject) {
            await transaction.rollback();
            return res.status(404).json({ message: 'Subject not found' });
        }

        // Create chapter
        const chapter = await Chapter.create({
            name,
            description,
            subject_id
        }, { transaction });

        // Associate with LOs if provided
        if (lo_ids && Array.isArray(lo_ids) && lo_ids.length > 0) {
            const los = await LO.findAll({
                where: { lo_id: { [Op.in]: lo_ids } },
                transaction
            });

            if (los.length !== lo_ids.length) {
                await transaction.rollback();
                return res.status(400).json({ message: 'Some LO IDs are invalid' });
            }

            await chapter.addLOs(los, { transaction });
        }

        // Create sections if provided
        if (sections && Array.isArray(sections) && sections.length > 0) {
            const sectionsData = sections.map((section, index) => ({
                ...section,
                chapter_id: chapter.chapter_id,
                order_index: section.order_index || index + 1
            }));

            await ChapterSection.bulkCreate(sectionsData, { transaction });
        }

        await transaction.commit();

        // Fetch the created chapter with associations
        const createdChapter = await Chapter.findByPk(chapter.chapter_id, {
            include: [
                {
                    model: Subject,
                    as: 'Subject',
                    attributes: ['subject_id', 'name']
                },
                {
                    model: LO,
                    as: 'LOs',
                    attributes: ['lo_id', 'name'],
                    through: { attributes: [] }
                },
                {
                    model: ChapterSection,
                    as: 'Sections',
                    attributes: ['section_id', 'name', 'content', 'order_index']
                }
            ]
        });

        res.status(201).json({
            message: 'Chapter created successfully',
            chapter: createdChapter
        });

    } catch (error) {
        await transaction.rollback();
        console.error('Error creating chapter:', error);
        res.status(500).json({ message: 'Error creating chapter', error: error.message });
    }
};

// Update chapter
exports.updateChapter = async (req, res) => {
    const transaction = await sequelize.transaction();
    try {
        const { id } = req.params;
        const { name, description, subject_id, lo_ids, sections } = req.body;

        const chapter = await Chapter.findByPk(id, { transaction });
        if (!chapter) {
            await transaction.rollback();
            return res.status(404).json({ message: 'Chapter not found' });
        }

        // Update basic info
        await chapter.update({
            name: name || chapter.name,
            description: description !== undefined ? description : chapter.description,
            subject_id: subject_id || chapter.subject_id
        }, { transaction });

        // Update LO associations if provided
        if (lo_ids && Array.isArray(lo_ids)) {
            if (lo_ids.length > 0) {
                const los = await LO.findAll({
                    where: { lo_id: { [Op.in]: lo_ids } },
                    transaction
                });

                if (los.length !== lo_ids.length) {
                    await transaction.rollback();
                    return res.status(400).json({ message: 'Some LO IDs are invalid' });
                }

                await chapter.setLOs(los, { transaction });
            } else {
                await chapter.setLOs([], { transaction });
            }
        }

        // Update sections if provided
        if (sections && Array.isArray(sections)) {
            // Delete existing sections
            await ChapterSection.destroy({
                where: { chapter_id: id },
                transaction
            });

            // Create new sections
            if (sections.length > 0) {
                const sectionsData = sections.map((section, index) => ({
                    ...section,
                    chapter_id: id,
                    order_index: section.order_index || index + 1
                }));

                await ChapterSection.bulkCreate(sectionsData, { transaction });
            }
        }

        await transaction.commit();

        // Fetch updated chapter
        const updatedChapter = await Chapter.findByPk(id, {
            include: [
                {
                    model: Subject,
                    as: 'Subject',
                    attributes: ['subject_id', 'name']
                },
                {
                    model: LO,
                    as: 'LOs',
                    attributes: ['lo_id', 'name'],
                    through: { attributes: [] }
                },
                {
                    model: ChapterSection,
                    as: 'Sections',
                    attributes: ['section_id', 'name', 'content', 'order_index']
                }
            ]
        });

        res.status(200).json({
            message: 'Chapter updated successfully',
            chapter: updatedChapter
        });

    } catch (error) {
        await transaction.rollback();
        console.error('Error updating chapter:', error);
        res.status(500).json({ message: 'Error updating chapter', error: error.message });
    }
};

// Delete chapter
exports.deleteChapter = async (req, res) => {
    const transaction = await sequelize.transaction();
    try {
        const { id } = req.params;

        const chapter = await Chapter.findByPk(id, { transaction });
        if (!chapter) {
            await transaction.rollback();
            return res.status(404).json({ message: 'Chapter not found' });
        }

        // Check if chapter has associated questions
        const questionCount = await Question.count({
            include: [{
                model: LO,
                as: 'LO',
                include: [{
                    model: Chapter,
                    as: 'Chapters',
                    where: { chapter_id: id },
                    through: { attributes: [] }
                }]
            }],
            transaction
        });

        if (questionCount > 0) {
            await transaction.rollback();
            return res.status(400).json({
                message: 'Cannot delete chapter with associated questions',
                question_count: questionCount
            });
        }

        // Delete chapter (this will cascade delete sections and LO associations)
        await chapter.destroy({ transaction });

        await transaction.commit();

        res.status(200).json({ message: 'Chapter deleted successfully' });

    } catch (error) {
        await transaction.rollback();
        console.error('Error deleting chapter:', error);
        res.status(500).json({ message: 'Error deleting chapter', error: error.message });
    }
};

// =====================================================
// ADMIN STATISTICS AND BULK OPERATIONS
// =====================================================

// Get chapter statistics
exports.getChapterStatistics = async (req, res) => {
    try {
        const { subject_id, program_id } = req.query;

        let whereClause = {};
        if (subject_id) {
            whereClause.subject_id = subject_id;
        }

        // Get total chapters
        const totalChapters = await Chapter.count({ where: whereClause });

        // Get chapters with LOs
        const chaptersWithLOs = await Chapter.count({
            where: whereClause,
            include: [{
                model: LO,
                as: 'LOs',
                required: true,
                through: { attributes: [] }
            }]
        });

        // Get chapters with sections
        const chaptersWithSections = await Chapter.count({
            where: whereClause,
            include: [{
                model: ChapterSection,
                as: 'Sections',
                required: true
            }]
        });

        // Get LO distribution
        const chapterLOData = await Chapter.findAll({
            where: whereClause,
            include: [{
                model: LO,
                as: 'LOs',
                attributes: ['lo_id'],
                through: { attributes: [] }
            }],
            attributes: ['chapter_id', 'name']
        });

        const loDistribution = {};
        chapterLOData.forEach(chapter => {
            const loCount = chapter.LOs.length;
            loDistribution[loCount] = (loDistribution[loCount] || 0) + 1;
        });

        res.json({
            overview: {
                total_chapters: totalChapters,
                chapters_with_los: chaptersWithLOs,
                chapters_with_sections: chaptersWithSections,
                chapters_without_los: totalChapters - chaptersWithLOs,
                chapters_without_sections: totalChapters - chaptersWithSections
            },
            lo_distribution: loDistribution,
            generated_at: new Date()
        });

    } catch (error) {
        console.error('Error getting chapter statistics:', error);
        res.status(500).json({ message: 'Error getting chapter statistics', error: error.message });
    }
};

// Bulk create chapters
exports.bulkCreateChapters = async (req, res) => {
    const transaction = await sequelize.transaction();
    try {
        const { chapters } = req.body; // Array of chapter objects

        if (!Array.isArray(chapters) || chapters.length === 0) {
            await transaction.rollback();
            return res.status(400).json({ message: 'Invalid chapters list' });
        }

        // Validate each chapter
        for (const chapter of chapters) {
            if (!chapter.name || !chapter.subject_id) {
                await transaction.rollback();
                return res.status(400).json({ message: 'Name and subject_id are required for all chapters' });
            }
        }

        // Create chapters
        const createdChapters = await Chapter.bulkCreate(chapters, { transaction });
        await transaction.commit();

        res.status(201).json({
            message: `Successfully created ${createdChapters.length} chapters`,
            created_chapters: createdChapters
        });

    } catch (error) {
        await transaction.rollback();
        console.error('Error bulk creating chapters:', error);
        res.status(500).json({ message: 'Error bulk creating chapters', error: error.message });
    }
};

// Bulk delete chapters
exports.bulkDeleteChapters = async (req, res) => {
    const transaction = await sequelize.transaction();
    try {
        const { chapter_ids } = req.body; // Array of chapter IDs

        if (!Array.isArray(chapter_ids) || chapter_ids.length === 0) {
            await transaction.rollback();
            return res.status(400).json({ message: 'Invalid chapter IDs list' });
        }

        // Check if any chapter has associated questions
        const chaptersWithQuestions = await Chapter.findAll({
            where: { chapter_id: { [Op.in]: chapter_ids } },
            include: [{
                model: LO,
                as: 'LOs',
                include: [{
                    model: Question,
                    required: true,
                    attributes: ['question_id']
                }],
                through: { attributes: [] }
            }],
            transaction
        });

        const chaptersWithQuestionsFiltered = chaptersWithQuestions.filter(chapter =>
            chapter.LOs.some(lo => lo.Questions.length > 0)
        );

        if (chaptersWithQuestionsFiltered.length > 0) {
            await transaction.rollback();
            return res.status(400).json({
                message: 'Cannot delete chapters with associated questions',
                chapters_with_questions: chaptersWithQuestionsFiltered.map(chapter => ({
                    chapter_id: chapter.chapter_id,
                    name: chapter.name,
                    question_count: chapter.LOs.reduce((sum, lo) => sum + lo.Questions.length, 0)
                }))
            });
        }

        // Delete chapters
        const deletedCount = await Chapter.destroy({
            where: { chapter_id: { [Op.in]: chapter_ids } },
            transaction
        });

        await transaction.commit();

        res.json({
            message: `Successfully deleted ${deletedCount} chapters`,
            deleted_count: deletedCount
        });

    } catch (error) {
        await transaction.rollback();
        console.error('Error bulk deleting chapters:', error);
        res.status(500).json({ message: 'Error bulk deleting chapters', error: error.message });
    }
};
