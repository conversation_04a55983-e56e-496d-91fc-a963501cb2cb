'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
    class Program extends Model {
        static associate(models) {
            Program.hasMany(models.PO, { foreignKey: 'program_id' });
            Program.hasMany(models.PLO, { foreignKey: 'program_id' });
            Program.hasMany(models.Course, { foreignKey: 'program_id' });
        }
    }

    Program.init(
        {
            program_id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true,
                autoIncrementIdentity: true
            },
            name: {
                type: DataTypes.STRING,
                allowNull: false,
            },
            description: {
                type: DataTypes.TEXT,
            },
        },
        {
            sequelize,
            modelName: 'Program',
            tableName: 'Programs',
        }
    );

    return Program;
};