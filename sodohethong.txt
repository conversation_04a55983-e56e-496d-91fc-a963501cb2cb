Tài liệu Hệ thống Synlearnia
Tổng quan Hệ thống
Synlearnia là một hệ thống quản lý chương trình đào tạo hiện đại, đ<PERSON><PERSON><PERSON> xây dựng với kiến trúc full-stack, hỗ trợ thi trực tuyến realtime và quản lý học tập toàn diện.
<PERSON><PERSON><PERSON> đích ch<PERSON>
•	Quản lý chương trình đào tạo (Programs, PO, PLO)
•	Quản lý môn học và khóa học (Courses, Subjects, Chapters)
•	<PERSON>ệ thống thi trực tuyến realtime với Socket.IO
•	Quản lý người dùng và phân quyền (Admin, Teacher, Student)
•	Theo dõi kết quả học tập và analytics
•	Quản lý câu hỏi và bài kiểm tra

Mô tả kiến trúc hệ thống
 
Hệ thống được tổ chức thành ba tầng chính: <PERSON><PERSON> Layer, Application Layer và Database Layer với thành phần cân bằng tải (Load Balancer) nằm ở giữa để điều phối lưu lượng truy cập.



1. Client Layer 
Lớp này bao gồm các trình duyệt web và trình duyệt di động, nơi người dùng truy cập hệ thống. Giao diện người dùng được xây dựng bằng Next.js Frontend, đóng vai trò làm điểm truy cập chính từ phía người dùng.
	Chi tiết công nghệ sử dụng:
•	Framework: Next.js 15 với React 19
•	Language: TypeScript
•	Styling: Tailwind CSS
•	UI Components: Radix UI, Lucide React
•	State Management: React Hooks, Custom Hooks
•	Real-time: Socket.IO Client
•	Authentication: JWT với role-based access control	
2. Load Balancer
Tất cả các yêu cầu từ Next.js Frontend sẽ được chuyển đến Nginx Reverse Proxy, hoạt động như một Load Balancer để phân phối tải đồng đều đến backend, giúp nâng cao hiệu suất và độ ổn định của hệ thống.
3. Application Layer 
Tại lớp này, Express.js Backend là thành phần chính xử lý logic nghiệp vụ. Từ đây, hệ thống phân tán dữ liệu và giao tiếp thời gian thực được hỗ trợ thông qua:
•	Socket.IO Server: Xử lý các kết nối thời gian thực (real-time) giữa client và server.
•	Giao tiếp với các hệ quản trị cơ sở dữ liệu thông qua các API và trình quản lý kết nối.

Chi tiết công nghệ sử dụng:
•	Runtime: Node.js
•	Framework: Express.js
•	Real-time: Socket.IO Server
•	ORM: Sequelize
•	Authentication: JWT, bcrypt
•	File Upload: Multer
•	Validation: Custom middleware
4. Database Layer 
Dữ liệu được lưu trữ và truy xuất thông qua ba hệ thống chính:
•	PostgreSQL: Cơ sở dữ liệu quan hệ để lưu trữ thông tin có cấu trúc.
•	Redis Cache: Dùng để lưu trữ dữ liệu tạm thời giúp tăng tốc độ truy xuất.
•	Firebase Realtime DB: Cơ sở dữ liệu NoSQL hỗ trợ cập nhật theo thời gian thực, đặc biệt hữu ích cho các ứng dụng realtime sử dụng Socket.IO.
Chi tiết công nghệ sử dụng:
•	Primary Database: PostgreSQL
o	User data, Course data, Quiz data, Results
•	Cache Database: Redis
o	Session storage, Quiz state, Real-time data
•	External Database: Firebase
o	Real-time database, Push notifications


Chi tiết các layer
 


 
Sơ đồ usecase
  

Chi tiết quyền của các artor
Admin
•	Quản lý người dùng (CRUD operations)
•	Phân quyền vai trò
•	Quản lý chương trình đào tạo
•	Cấu hình hệ thống
•	Xem báo cáo tổng quan
Giảng viên (Teacher)
•	Quản lý môn học
•	Tạo/Chỉnh sửa câu hỏi
•	Tạo bài kiểm tra
•	Quản lý quiz realtime
•	Xem kết quả học sinh
•	Xuất báo cáo
•	Upload tài liệu
Sinh viên (Student)
•	Đăng ký môn học
•	Tham gia quiz realtime
•	Xem kết quả bài thi
•	Theo dõi tiến độ học tập
•	Tải tài liệu môn học


Sơ đồ luồng hoạt động quiz realtime
 
Luồng hoạt động
1.	Tạo Quiz: Giảng viên tạo quiz và câu hỏi
2.	Khởi tạo Session: Tạo phòng chờ với mã PIN
3.	Tham gia: Sinh viên nhập mã PIN để tham gia
4.	Bắt đầu: Giảng viên khởi động quiz
5.	Làm bài: Sinh viên trả lời câu hỏi realtime
6.	Kết quả: Hiển thị đáp án và điểm số ngay lập tức
7.	Bảng xếp hạng: Cập nhật thứ hạng theo thời gian thực
8.	Báo cáo: Xuất kết quả chi tiết
API Endpoints
Authentication
POST /api/users/login                  # Đăng nhập
GET /api/users                         # Danh sách người dùng (Admin)
POST /api/users/createStudent          # Tạo sinh viên (Admin/Teacher)
POST /api/users/createTeacher          # Tạo giảng viên (Admin)
Program & Course Management
GET /api/programs                      # Danh sách chương trình đào tạo
POST /api/programs                     # Tạo chương trình (Admin)
GET /api/courses                       # Danh sách khóa học
GET /api/subjects                      # Danh sách môn học
GET /api/chapters/subject/:subject_id  # Chương theo môn học
Question & Answer Management
GET /api/questions                     # Danh sách câu hỏi
POST /api/questions                    # Tạo câu hỏi (Teacher/Admin)
POST /api/questions/import-excel       # Import câu hỏi từ Excel
GET /api/answers                       # Danh sách đáp án
POST /api/answers                      # Tạo đáp án (Teacher/Admin)
Quiz Management (Realtime)
GET /api/quizzes                       # Danh sách quiz
POST /api/quizzes                      # Tạo quiz (Teacher/Admin)
POST /api/quizzes/:id/start            # Bắt đầu quiz (Teacher/Admin)
POST /api/quizzes/:id/join             # Tham gia quiz (Student)
POST /api/quizzes/realtime/answer      # Gửi đáp án realtime (Student)
GET /api/quizzes/:id/leaderboard       # Bảng xếp hạng
GET /api/quizzes/pin/:pin              # Tìm quiz theo PIN
Results & Analytics
GET /api/quiz-results/user/:user_id    # Kết quả của sinh viên
GET /api/quiz-results/quiz/:quiz_id    # Kết quả theo quiz
GET /api/quizzes/:id/statistics        # Thống kê quiz (Teacher/Admin)
GET /api/reports/program/:program_id/overview  # Báo cáo chương trình
Learning Outcomes
GET /api/pos/program/:program_id       # Program Outcomes theo chương trình
GET /api/plos/program/:program_id      # Program Learning Outcomes
GET /api/los/subject/:subjectId        # Learning Outcomes theo môn học

