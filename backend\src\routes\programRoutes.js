const express = require('express');
const router = express.Router();
const programController = require('../controllers/programController');

// L<PERSON><PERSON> danh sách tất cả chương trình
router.get('/', programController.getAllPrograms);

// L<PERSON>y thông tin chi tiết một chương trình
router.get('/:id', programController.getProgramById);

// Tạo một chương trình mới
router.post('/', programController.createProgram);

// Cập nhật thông tin một chương trình
router.put('/:id', programController.updateProgram);

// Xóa một chương trình
router.delete('/:id', programController.deleteProgram);

module.exports = router;