"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Loader2, Bar<PERSON>hart3, Target } from "lucide-react";
import { quizService } from "@/services/api";
import { UserRadarData, RadarChartConfig, AllRadarData } from "@/types/radar";
import { showErrorToast } from "@/lib/toast-utils";
import Radar<PERSON>hart, { transformRadarData, colorSchemes } from "./RadarChart";

interface StudentRadarChartProps {
  quizId: number;
  quizName?: string;
  className?: string;
}

export default function StudentRadarChart({
  quizId,
  quizName,
  className = "",
}: StudentRadarChartProps) {
  const [radarData, setRadarData] = useState<UserRadarData | null>(null);
  const [combinedRadarData, setCombinedRadarData] =
    useState<AllRadarData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchRadarData = async () => {
      try {
        setIsLoading(true);

        // Gọi 3 API song song
        const [currentUserData, averageData, topPerformerData] =
          await Promise.allSettled([
            quizService.getCurrentUserRadarData(quizId),
            quizService.getAverageRadarData(quizId),
            quizService.getTopPerformerRadarData(quizId),
          ]);

        // Lưu dữ liệu người dùng hiện tại (để hiển thị thông tin chi tiết)
        if (currentUserData.status === "fulfilled") {
          setRadarData(currentUserData.value);
        }

        // Tạo dữ liệu kết hợp cho radar chart chồng lại
        const processedData: AllRadarData = {
          quiz_id: quizId,
          quiz_name: quizName || "Bài kiểm tra",
          total_questions: 0,
          radar_data: {},
          summary: {
            total_participants: 0,
            total_answers: 0,
            average_score: 0,
            difficulty_levels: [],
            learning_outcomes: [],
          },
        };

        // Xử lý dữ liệu người dùng hiện tại
        if (currentUserData.status === "fulfilled") {
          processedData.radar_data.current_user = {
            user_id: currentUserData.value.user_id,
            data: currentUserData.value.radar_data,
          };
        }

        // Xử lý dữ liệu trung bình
        if (averageData.status === "fulfilled") {
          processedData.radar_data.average = averageData.value.radar_data;
        }

        // Xử lý dữ liệu top performer
        if (topPerformerData.status === "fulfilled") {
          processedData.radar_data.top_performer = {
            user_info: topPerformerData.value.top_performer,
            data: topPerformerData.value.radar_data,
          };
        }

        setCombinedRadarData(processedData);
        setError(null);
      } catch (err) {
        console.error("Lỗi khi lấy dữ liệu radar chart:", err);
        setError("Không thể tải dữ liệu phân tích. Vui lòng thử lại sau.");
        showErrorToast("Không thể tải dữ liệu phân tích");
      } finally {
        setIsLoading(false);
      }
    };

    if (quizId) {
      fetchRadarData();
    }
  }, [quizId]);

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="flex justify-center items-center py-20">
          <div className="flex flex-col items-center">
            <Loader2 className="h-10 w-10 animate-spin text-primary mb-4" />
            <span className="text-lg font-medium text-muted-foreground">
              Đang tải dữ liệu phân tích...
            </span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || !radarData) {
    return (
      <Card className={className}>
        <CardContent className="flex flex-col items-center py-20">
          <BarChart3 className="h-16 w-16 text-muted-foreground mb-4" />
          <p className="text-lg font-medium text-muted-foreground mb-2">
            {error || "Không có dữ liệu phân tích"}
          </p>
          <Button onClick={() => window.location.reload()} variant="outline">
            Thử lại
          </Button>
        </CardContent>
      </Card>
    );
  }

  // Tạo dữ liệu cho chart chồng lại (tương tự TeacherRadarChart)
  const getComparisonChartData = (): RadarChartConfig => {
    if (!combinedRadarData) {
      // Fallback về chart đơn nếu không có dữ liệu kết hợp
      return transformRadarData(
        radarData.radar_data,
        "Kết quả của bạn",
        colorSchemes.primary
      );
    }

    const datasets = [];

    // 1. Thêm dữ liệu trung bình (vòng ngoài cùng - màu xanh dương)
    if (combinedRadarData.radar_data.average) {
      const avgData = transformRadarData(
        combinedRadarData.radar_data.average,
        "Trung bình lớp",
        colorSchemes.primary
      );
      const customizedDataset = {
        ...avgData.datasets[0],
        backgroundColor: "rgba(59, 130, 246, 0.1)",
        borderColor: "rgb(59, 130, 246)",
        pointBackgroundColor: "rgb(59, 130, 246)",
        pointBorderColor: "#fff",
        pointHoverBackgroundColor: "#fff",
        pointHoverBorderColor: "rgb(59, 130, 246)",
      };
      datasets.push(customizedDataset);
    }

    // 2. Thêm dữ liệu top performer (vòng giữa - màu xanh lá)
    if (combinedRadarData.radar_data.top_performer) {
      const topData = transformRadarData(
        combinedRadarData.radar_data.top_performer.data,
        "Học viên xuất sắc",
        colorSchemes.success
      );
      const customizedDataset = {
        ...topData.datasets[0],
        backgroundColor: "rgba(34, 197, 94, 0.15)",
        borderColor: "rgb(34, 197, 94)",
        pointBackgroundColor: "rgb(34, 197, 94)",
        pointBorderColor: "#fff",
        pointHoverBackgroundColor: "#fff",
        pointHoverBorderColor: "rgb(34, 197, 94)",
      };
      datasets.push(customizedDataset);
    }

    // 3. Thêm dữ liệu user hiện tại (vòng trong cùng - màu cam)
    if (combinedRadarData.radar_data.current_user) {
      const currentData = transformRadarData(
        combinedRadarData.radar_data.current_user.data,
        "Kết quả của bạn",
        colorSchemes.warning
      );
      const customizedDataset = {
        ...currentData.datasets[0],
        backgroundColor: "rgba(249, 115, 22, 0.2)",
        borderColor: "rgb(249, 115, 22)",
        pointBackgroundColor: "rgb(249, 115, 22)",
        pointBorderColor: "#fff",
        pointHoverBackgroundColor: "#fff",
        pointHoverBorderColor: "rgb(249, 115, 22)",
      };
      datasets.push(customizedDataset);
    }

    // Lấy labels từ dataset đầu tiên có sẵn
    const labels = combinedRadarData.radar_data.average
      ? transformRadarData(
          combinedRadarData.radar_data.average,
          "",
          colorSchemes.primary
        ).labels
      : combinedRadarData.radar_data.current_user
      ? transformRadarData(
          combinedRadarData.radar_data.current_user.data,
          "",
          colorSchemes.warning
        ).labels
      : [];

    return { labels, datasets };
  };

  const chartData: RadarChartConfig = getComparisonChartData();

  // Tính toán dữ liệu để hiển thị
  const difficultyLevels = radarData.radar_data.difficulty_levels;
  const learningOutcomes = radarData.radar_data.learning_outcomes;

  // Helper function để map difficulty level
  const getDifficultyDisplay = (level: string) => {
    const levelLower = level.toLowerCase();
    if (
      levelLower.includes("easy") ||
      levelLower.includes("dễ") ||
      levelLower === "de"
    ) {
      return { icon: "🟢", name: "Dễ" };
    } else if (
      levelLower.includes("medium") ||
      levelLower.includes("trung") ||
      levelLower === "tb" ||
      levelLower === "medium"
    ) {
      return { icon: "🟡", name: "Trung bình" };
    } else if (
      levelLower.includes("hard") ||
      levelLower.includes("khó") ||
      levelLower === "kho"
    ) {
      return { icon: "🔴", name: "Khó" };
    } else {
      return { icon: "📝", name: level };
    }
  };

  // Format thời gian
  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  };

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <CardTitle className="text-xl sm:text-2xl flex items-center gap-2">
              <BarChart3 className="h-6 w-6" />
              Phân tích kết quả của bạn
            </CardTitle>
            {quizName && <p className="text-muted-foreground">{quizName}</p>}
            {radarData?.message && (
              <p className="text-sm text-amber-600 bg-amber-50 p-2 rounded mt-2">
                {radarData.message}
              </p>
            )}
          </div>

          {/* Legend cho 3 vòng chồng lên nhau (chỉ hiển thị khi có dữ liệu kết hợp) */}
          {combinedRadarData && (
            <div className="text-sm">
              <div className="bg-gray-50 p-3 rounded-lg">
                <div className="font-medium text-gray-700 mb-2">So sánh:</div>
                <div className="flex flex-wrap gap-4">
                  {combinedRadarData.radar_data.average && (
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full bg-blue-500"></div>
                      <span className="text-gray-600">Trung bình lớp</span>
                    </div>
                  )}
                  {combinedRadarData.radar_data.top_performer && (
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full bg-green-500"></div>
                      <span className="text-gray-600">Học viên xuất sắc</span>
                    </div>
                  )}
                  {combinedRadarData.radar_data.current_user && (
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full bg-orange-500"></div>
                      <span className="text-gray-600">Kết quả của bạn</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </CardHeader>

      <CardContent>
        {/* Radar Chart */}
        <div className="mb-8">
          <RadarChart
            data={chartData}
            title={quizName || "Bài kiểm tra"}
            height={400}
            radarData={radarData.radar_data}
          />
        </div>

        {/* Enhanced Recommendations */}
        {(radarData.weakest_lo ||
          radarData.weakest_difficulty ||
          Object.keys(difficultyLevels).length > 0) && (
          <div className="mt-8 bg-gradient-to-br from-amber-50 to-orange-50 border border-amber-200 rounded-xl p-6 shadow-sm">
            <h4 className="font-bold text-amber-900 mb-6 flex items-center gap-2 text-lg">
              <Target className="h-6 w-6" />
              Đề xuất cải thiện chi tiết
            </h4>

            <div className="space-y-6">
              {/* Weakest LO with enhanced details */}
              {radarData.weakest_lo && (
                <div className="bg-white p-5 rounded-xl border border-amber-200 shadow-sm">
                  <div className="flex items-start justify-between mb-4">
                    <div>
                      <h5 className="font-semibold text-amber-900 text-base mb-1">
                        🎯 Chuẩn đầu ra ưu tiên cải thiện
                      </h5>
                      <div className="text-lg font-bold text-amber-800 mb-1">
                        {radarData.weakest_lo.lo_name}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-red-600">
                        {radarData.weakest_lo.accuracy.toFixed(1)}%
                      </div>
                      <div className="text-xs text-amber-600">độ chính xác</div>
                    </div>
                  </div>

                  {radarData.weakest_lo.chapters &&
                    radarData.weakest_lo.chapters.length > 0 && (
                      <div className="mt-4">
                        <p className="text-sm font-semibold text-amber-800 mb-3 flex items-center gap-2">
                          📚 Chương học cần ôn tập:
                        </p>
                        <div className="grid grid-cols-1 gap-3">
                          {radarData.weakest_lo.chapters
                            .slice(0, 3)
                            .map((chapter) => (
                              <div
                                key={chapter.chapter_id}
                                className="bg-gradient-to-r from-amber-100 to-yellow-100 p-3 rounded-lg border border-amber-200"
                              >
                                <div className="font-semibold text-amber-800 mb-1">
                                  {chapter.chapter_name}
                                </div>
                                {chapter.description && (
                                  <div className="text-sm text-amber-700">
                                    {chapter.description}
                                  </div>
                                )}
                              </div>
                            ))}
                          {radarData.weakest_lo.chapters.length > 3 && (
                            <div className="text-sm text-amber-600 text-center py-2">
                              +{radarData.weakest_lo.chapters.length - 3} chương
                              khác cần ôn tập
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                </div>
              )}

              {/* Enhanced Difficulty Analysis */}
              {Object.keys(difficultyLevels).length > 0 && (
                <div className="bg-white p-5 rounded-xl border border-amber-200 shadow-sm">
                  <h5 className="font-semibold text-amber-900 text-base mb-4 flex items-center gap-2">
                    📊 Phân tích theo độ khó
                  </h5>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {Object.entries(difficultyLevels)
                      .sort(([, a], [, b]) => a.accuracy - b.accuracy)
                      .map(([level, data]) => {
                        const isWeakest =
                          data.accuracy ===
                          Math.min(
                            ...Object.values(difficultyLevels).map(
                              (d) => d.accuracy
                            )
                          );
                        return (
                          <div
                            key={level}
                            className={`p-4 rounded-lg border-2 ${
                              isWeakest
                                ? "bg-red-50 border-red-200"
                                : data.accuracy >= 70
                                ? "bg-green-50 border-green-200"
                                : "bg-yellow-50 border-yellow-200"
                            }`}
                          >
                            <div className="flex items-center justify-between mb-2">
                              <span className="font-medium capitalize text-gray-800">
                                {(() => {
                                  const display = getDifficultyDisplay(level);
                                  return `${display.icon} ${display.name}`;
                                })()}
                              </span>
                              <span
                                className={`text-lg font-bold ${
                                  isWeakest
                                    ? "text-red-600"
                                    : data.accuracy >= 70
                                    ? "text-green-600"
                                    : "text-yellow-600"
                                }`}
                              >
                                {data.accuracy}%
                              </span>
                            </div>
                            <div className="text-xs text-gray-600">
                              {data.questions_count} câu •{" "}
                              {formatTime(data.average_response_time)}
                            </div>
                            {isWeakest && (
                              <div className="mt-2 text-xs font-medium text-red-700 bg-red-100 p-2 rounded">
                                ⚠️ Cần tập trung cải thiện
                              </div>
                            )}
                          </div>
                        );
                      })}
                  </div>
                </div>
              )}

              {/* Learning Outcomes Analysis */}
              {Object.keys(learningOutcomes).length > 0 && (
                <div className="bg-white p-5 rounded-xl border border-amber-200 shadow-sm">
                  <h5 className="font-semibold text-amber-900 text-base mb-4 flex items-center gap-2">
                    🎯 Phân tích theo chuẩn đầu ra
                  </h5>

                  <div className="space-y-3">
                    {Object.entries(learningOutcomes)
                      .sort(([, a], [, b]) => a.accuracy - b.accuracy)
                      .map(([lo, data]) => {
                        const isWeakest =
                          data.accuracy ===
                          Math.min(
                            ...Object.values(learningOutcomes).map(
                              (d) => d.accuracy
                            )
                          );
                        return (
                          <div
                            key={lo}
                            className={`p-3 rounded-lg border ${
                              isWeakest
                                ? "bg-red-50 border-red-200"
                                : data.accuracy >= 70
                                ? "bg-green-50 border-green-200"
                                : "bg-yellow-50 border-yellow-200"
                            }`}
                          >
                            <div className="flex justify-between items-center mb-1">
                              <span className="font-medium text-gray-800">
                                {lo}
                              </span>
                              <span
                                className={`font-bold ${
                                  isWeakest
                                    ? "text-red-600"
                                    : data.accuracy >= 70
                                    ? "text-green-600"
                                    : "text-yellow-600"
                                }`}
                              >
                                {data.accuracy}%
                              </span>
                            </div>
                            {data.description && (
                              <div className="text-sm text-gray-600 mb-2 italic">
                                {data.description}
                              </div>
                            )}
                            <div className="text-xs text-gray-500">
                              {data.questions_count} câu •{" "}
                              {formatTime(data.average_response_time)}
                            </div>
                            {isWeakest && (
                              <div className="mt-2 text-xs font-medium text-red-700">
                                ⚠️ Ưu tiên cải thiện chuẩn đầu ra này
                              </div>
                            )}
                          </div>
                        );
                      })}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
