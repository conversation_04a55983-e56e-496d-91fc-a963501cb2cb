# API Phân Tích Cải Thiện Quiz Results

## Tổng quan

API này cung cấp phân tích chi tiết về cấp độ và chương cần cải thiện dựa trên kết quả quiz của học sinh. API có thể phân tích cho một quiz cụ thể hoặc toàn bộ subject.

## Endpoint

```
GET /api/quiz-results/improvement-analysis
```

## Quyền truy cập
- **Admin**: <PERSON><PERSON> thể xem phân tích cho tất cả học sinh
- **Teacher**: <PERSON><PERSON> thể xem phân tích cho tất cả học sinh trong lớp
- **Student**: Chỉ có thể xem phân tích cá nhân

## Parameters

### Query Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `quiz_id` | Integer | No* | ID của quiz cần phân tích |
| `subject_id` | Integer | No* | ID của subject cần phân tích |
| `user_id` | Integer | No | ID của user cụ thể (nếu không có sẽ phân tích tất cả) |

*Lưu ý: Phải cung cấp ít nhất một trong hai: `quiz_id` hoặc `subject_id`

## Response Format

### Thành công (200)

```json
{
  "success": true,
  "data": {
    "quiz_info": {
      "quiz_id": 1,
      "quiz_name": "Quiz Chương 1",
      "total_questions": 20
    },
    "weak_levels": {
      "levels_analysis": [
        {
          "level": "Hard",
          "accuracy": 35,
          "total_questions": 5,
          "total_attempts": 50,
          "correct_attempts": 17,
          "average_time": 180,
          "questions_list": [
            {
              "question_id": 1,
              "question_text": "Câu hỏi khó..."
            }
          ],
          "improvement_priority": "high"
        }
      ],
      "weakest_level": {
        "level": "Hard",
        "accuracy": 35,
        "improvement_priority": "high"
      },
      "summary": {
        "total_levels": 3,
        "levels_need_improvement": 2
      }
    },
    "chapters_need_improvement": {
      "lo_analysis": [
        {
          "lo_id": 1,
          "lo_name": "LO1.1",
          "description": "Hiểu khái niệm cơ bản",
          "accuracy": 45,
          "total_questions": 8,
          "total_attempts": 80,
          "correct_attempts": 36,
          "average_time": 120,
          "chapters": [1, 2],
          "improvement_priority": "high"
        }
      ],
      "chapter_analysis": [
        {
          "chapter_id": 1,
          "chapter_name": "Chương 1: Cơ bản",
          "description": "Các khái niệm cơ bản",
          "subject_id": 1,
          "subject_name": "Toán học",
          "accuracy": 42,
          "total_questions": 10,
          "total_attempts": 100,
          "correct_attempts": 42,
          "average_time": 150,
          "los": ["LO1.1", "LO1.2"],
          "improvement_priority": "high"
        }
      ],
      "weakest_los": [
        {
          "lo_id": 1,
          "lo_name": "LO1.1",
          "accuracy": 45,
          "improvement_priority": "high"
        }
      ],
      "chapters_need_improvement": [
        {
          "chapter_id": 1,
          "chapter_name": "Chương 1: Cơ bản",
          "accuracy": 42,
          "improvement_priority": "high"
        }
      ],
      "summary": {
        "total_los": 5,
        "total_chapters": 3,
        "los_need_improvement": 3,
        "chapters_need_improvement": 2
      }
    },
    "improvement_suggestions": {
      "level_suggestions": [
        {
          "level": "Hard",
          "accuracy": 35,
          "priority": "high",
          "suggestions": [
            "Tập trung vào việc hiểu bản chất vấn đề",
            "Tìm kiếm sự hỗ trợ từ giảng viên",
            "Nghiên cứu thêm tài liệu tham khảo nâng cao"
          ]
        }
      ],
      "chapter_suggestions": [
        {
          "chapter_id": 1,
          "chapter_name": "Chương 1: Cơ bản",
          "subject_name": "Toán học",
          "accuracy": 42,
          "priority": "high",
          "weak_los": [
            {
              "lo_id": 1,
              "lo_name": "LO1.1",
              "accuracy": 45
            }
          ],
          "suggestions": [
            "Ôn tập lại toàn bộ chương \"Chương 1: Cơ bản\"",
            "Đọc kỹ lại giáo trình và ghi chú bài giảng"
          ]
        }
      ],
      "general_recommendations": [
        "Cần có kế hoạch học tập có hệ thống từ cơ bản đến nâng cao",
        "Thường xuyên làm bài kiểm tra để đánh giá tiến độ"
      ],
      "priority_actions": [
        {
          "priority": 1,
          "action": "Tập trung cải thiện cấp độ Hard",
          "reason": "Accuracy chỉ 35%",
          "timeline": "1-2 tuần"
        },
        {
          "priority": 2,
          "action": "Ôn tập lại chương \"Chương 1: Cơ bản\"",
          "reason": "Accuracy chỉ 42%",
          "timeline": "2-3 tuần"
        }
      ]
    },
    "analysis_scope": "all_participants"
  },
  "generated_at": "2024-01-15T10:30:00.000Z"
}
```

### Lỗi (400)

```json
{
  "message": "Cần cung cấp quiz_id hoặc subject_id để phân tích"
}
```

### Lỗi (500)

```json
{
  "success": false,
  "message": "Lỗi khi phân tích dữ liệu cải thiện",
  "error": "Chi tiết lỗi..."
}
```

## Ví dụ sử dụng

### 1. Phân tích cải thiện cho một quiz cụ thể

```bash
GET /api/quiz-results/improvement-analysis?quiz_id=1
```

### 2. Phân tích cải thiện cho một học sinh cụ thể trong quiz

```bash
GET /api/quiz-results/improvement-analysis?quiz_id=1&user_id=123
```

### 3. Phân tích cải thiện cho toàn bộ subject

```bash
GET /api/quiz-results/improvement-analysis?subject_id=1
```

### 4. Phân tích cải thiện cho một học sinh trong toàn bộ subject

```bash
GET /api/quiz-results/improvement-analysis?subject_id=1&user_id=123
```

## Giải thích các trường dữ liệu

### Improvement Priority
- **high**: Accuracy < 50% - Cần cải thiện gấp
- **medium**: 50% ≤ Accuracy < 70% - Cần cải thiện
- **low**: Accuracy ≥ 70% - Tốt

### Analysis Scope
- **individual**: Phân tích cho một học sinh cụ thể
- **all_participants**: Phân tích cho tất cả học sinh tham gia

## Lưu ý

1. API này yêu cầu có dữ liệu lịch sử trả lời câu hỏi (UserQuestionHistory)
2. Kết quả phân tích dựa trên accuracy và thời gian trả lời
3. Gợi ý cải thiện được tạo tự động dựa trên mức độ yếu của từng cấp độ và chương
4. API hỗ trợ phân tích cả cá nhân và tổng thể
