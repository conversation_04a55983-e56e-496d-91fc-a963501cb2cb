# Dependencies
node_modules/
*/node_modules/
.pnpm-store/
.pnpm-debug.log*

# Specific paths
/backend/tools/~$tkw_questions.xlsx

# Environment variables (workspace level)
/frontend/.env
/frontend/.env.production
/backend/.env

# Environment variables
.env
.env.local
.env.development.local
.env.production.local
.env.production
# Build outputs
.next/
out/
build/
dist/
*.tsbuildinfo

# Log files
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Temporary files
tmp/
temp/
*.tmp

# IDE/Editor configuration
.vscode/*
!.vscode/extensions.json
.idea/
*.swp
*.swo
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# System files
.DS_Store
Thumbs.db
Desktop.ini
ehthumbs.db

# Compiled files
*.class
*.pyc

# Archives
*.zip
*.tar.gz
*.tar.bz2
*.tgz

# Cache directories
.cache/
.parcel-cache/
.npm/
.eslintcache

# Coverage
coverage/
*.lcov
.nyc_output/

# Database files
*.sqlite
*.db

# Uploads
uploads/
*/uploads/

# Specific files to ignore
next-env.d.ts

# Runtime data
pids/
*.pid
*.seed
*.pid.lock
