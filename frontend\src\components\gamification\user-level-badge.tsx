"use client";

import React from "react";
import { Progress } from "@/components/ui/progress";
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Trophy, Star, Zap, Crown, Medal } from "lucide-react";
import { useGamification } from "@/hooks/use-gamification";
import { cn } from "@/lib/utils";

interface UserLevelBadgeProps {
  variant?: "default" | "compact" | "detailed";
  showProgress?: boolean;
  showPoints?: boolean;
  className?: string;
}

export const UserLevelBadge: React.FC<UserLevelBadgeProps> = ({
  variant = "default",
  showProgress = true,
  showPoints = true,
  className,
}) => {
  const {
    userGamification,
    isLoading,
    levelProgress,
    levelName,
    formattedPoints,
  } = useGamification();

  if (isLoading) {
    return (
      <div className={cn("animate-pulse", className)}>
        <div className="h-6 bg-gray-200 rounded-md w-20"></div>
      </div>
    );
  }

  if (!userGamification) {
    return null;
  }

  const getLevelIcon = (level: number) => {
    if (level >= 15) return <Crown className="w-4 h-4" />;
    if (level >= 10) return <Trophy className="w-4 h-4" />;
    if (level >= 5) return <Medal className="w-4 h-4" />;
    if (level >= 3) return <Star className="w-4 h-4" />;
    return <Zap className="w-4 h-4" />;
  };

  const getLevelIconColor = (level: number) => {
    if (level >= 15) return "text-amber-600";
    if (level >= 10) return "text-yellow-600";
    if (level >= 5) return "text-orange-600";
    if (level >= 3) return "text-blue-600";
    return "text-slate-600";
  };

  if (variant === "compact") {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div
              className={cn(
                "inline-flex items-center gap-2 px-3 py-1.5 rounded-lg border-2 border-border",
                "hover:border-primary transition-all cursor-pointer",
                className
              )}
            >
              <div
                className={cn(
                  "w-5 h-5 flex items-center justify-center",
                  getLevelIconColor(userGamification.current_level)
                )}
              >
                {getLevelIcon(userGamification.current_level)}
              </div>
              <span className="text-sm font-semibold text-slate-900 dark:text-slate-100">
                Lv.{userGamification.current_level}
              </span>
            </div>
          </TooltipTrigger>
          <TooltipContent side="bottom" className="max-w-xs">
            <div className="space-y-1">
              <p className="font-semibold text-sm">{levelName}</p>
              <p className="text-xs text-muted-foreground">
                {formattedPoints} điểm tổng
              </p>
              <p className="text-xs text-muted-foreground">
                {userGamification.experience_points}/100 XP
              </p>
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  if (variant === "detailed") {
    return (
      <div
        className={cn(
          "p-6 rounded-xl border-2 border-border hover:border-primary transition-all",
          className
        )}
      >
        {/* Header Section */}
        <div className="flex items-start justify-between mb-6">
          <div className="flex items-center gap-4">
            <div
              className={cn(
                "w-12 h-12 rounded-xl border-2 flex items-center justify-center bg-slate-50 dark:bg-slate-800",
                getLevelIconColor(userGamification.current_level)
              )}
            >
              <div className="w-6 h-6 flex items-center justify-center">
                {getLevelIcon(userGamification.current_level)}
              </div>
            </div>
            <div>
              <h3 className="text-xl font-bold text-slate-900 dark:text-slate-100">
                {levelName}
              </h3>
              <p className="text-sm text-slate-600 dark:text-slate-400">
                Cấp độ {userGamification.current_level}
              </p>
            </div>
          </div>
          {showPoints && (
            <div className="text-right">
              <p className="text-2xl font-bold text-slate-900 dark:text-slate-100">
                {formattedPoints}
              </p>
              <p className="text-xs text-slate-500 dark:text-slate-400 uppercase tracking-wide">
                Điểm tổng
              </p>
            </div>
          )}
        </div>

        {/* Progress Section */}
        {showProgress && (
          <div className="space-y-4 mb-6">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                Tiến độ cấp độ
              </span>
              <span className="text-sm font-semibold text-slate-900 dark:text-slate-100">
                {userGamification.experience_points}/100 XP
              </span>
            </div>
            <div className="space-y-2">
              <Progress
                value={levelProgress}
                className="h-3 bg-slate-200 dark:bg-slate-700"
              />
              <p className="text-xs text-slate-500 dark:text-slate-400 text-center">
                Còn {userGamification.experience_to_next_level} XP để lên cấp độ
                tiếp theo
              </p>
            </div>
          </div>
        )}

        {/* Stats Grid */}
        <div className="grid grid-cols-3 gap-4 pt-4 border-t border-slate-200 dark:border-slate-700">
          <div className="text-center space-y-1">
            <p className="text-2xl font-bold text-emerald-600 dark:text-emerald-400">
              {userGamification.stats.total_correct_answers}
            </p>
            <p className="text-xs text-slate-500 dark:text-slate-400 uppercase tracking-wide">
              Câu đúng
            </p>
          </div>
          <div className="text-center space-y-1">
            <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
              {userGamification.stats.best_streak}
            </p>
            <p className="text-xs text-slate-500 dark:text-slate-400 uppercase tracking-wide">
              Streak tốt nhất
            </p>
          </div>
          <div className="text-center space-y-1">
            <p className="text-2xl font-bold text-violet-600 dark:text-violet-400">
              {userGamification.stats.perfect_scores}
            </p>
            <p className="text-xs text-slate-500 dark:text-slate-400 uppercase tracking-wide">
              Điểm tuyệt đối
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Default variant
  return (
    <div className={cn("flex items-center gap-4", className)}>
      <div className="flex items-center gap-2 px-3 py-2 rounded-lg border-2 border-border">
        <div
          className={cn(
            "w-5 h-5 flex items-center justify-center",
            getLevelIconColor(userGamification.current_level)
          )}
        >
          {getLevelIcon(userGamification.current_level)}
        </div>
        <span className="text-sm font-semibold text-slate-900 dark:text-slate-100">
          Lv.{userGamification.current_level}
        </span>
      </div>

      {showPoints && (
        <div className="flex items-center gap-1">
          <span className="text-lg font-bold text-slate-900 dark:text-slate-100">
            {formattedPoints}
          </span>
          <span className="text-xs text-slate-500 dark:text-slate-400 uppercase tracking-wide">
            điểm
          </span>
        </div>
      )}

      {showProgress && (
        <div className="flex-1 min-w-0 space-y-1">
          <div className="flex justify-between text-xs">
            <span className="text-slate-600 dark:text-slate-400 font-medium">
              {levelName}
            </span>
            <span className="text-slate-600 dark:text-slate-400 font-medium">
              {userGamification.experience_points}/100 XP
            </span>
          </div>
          <Progress
            value={levelProgress}
            className="h-2 bg-slate-200 dark:bg-slate-700"
          />
        </div>
      )}
    </div>
  );
};

export default UserLevelBadge;
