const { QuestionType, Level, LO, Question, Answer, sequelize } = require('../models'); // Thêm sequelize vào import
const fs = require('fs');
const csv = require('fast-csv');
const xlsx = require('xlsx');
const { Op, literal } = require('sequelize');
// Lấy danh sách tất cả câu hỏi (có phân trang và lọc theo lo_id)
exports.getAllQuestions = async (req, res) => {
    try {
        const { page = 1, limit = 10, lo_id } = req.query;
        const offset = (page - 1) * limit;

        const where = lo_id ? { lo_id } : {};

        const questions = await Question.findAndCountAll({
            where,
            limit: parseInt(limit),
            offset: parseInt(offset),
            include: [
                { model: QuestionType, attributes: ['question_type_id', 'name'] },
                { model: Level, attributes: ['level_id', 'name'] },
                { model: LO, attributes: ['lo_id', 'name'] },
                { model: Answer, attributes: ['answer_id', 'answer_text', 'iscorrect'] },
            ],
        });

        res.status(200).json({
            totalItems: questions.count,
            totalPages: Math.ceil(questions.count / limit),
            currentPage: parseInt(page),
            questions: questions.rows,
        });
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi lấy danh sách câu hỏi', error: error.message });
    }
};

// Lấy thông tin chi tiết một câu hỏi
exports.getQuestionById = async (req, res) => {
    try {
        const question = await Question.findByPk(req.params.id, {
            include: [
                { model: QuestionType, attributes: ['question_type_id', 'name'] },
                { model: Level, attributes: ['level_id', 'name'] },
                { model: LO, attributes: ['lo_id', 'name'] },
                { model: Answer, attributes: ['answer_id', 'answer_text', 'iscorrect'] },
            ],
        });

        if (!question) {
            return res.status(404).json({ message: 'Câu hỏi không tồn tại' });
        }

        res.status(200).json(question);
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi lấy thông tin câu hỏi', error: error.message });
    }
};

// Tạo một câu hỏi mới
exports.createQuestion = async (req, res) => {
    try {
        const { question_type_id, level_id, question_text, lo_id } = req.body;

        // Kiểm tra các trường bắt buộc
        if (!question_type_id || !level_id || !question_text || !lo_id) {
            return res.status(400).json({ message: 'Thiếu các trường bắt buộc' });
        }

        // Kiểm tra xem question_type_id, level_id, lo_id có tồn tại không
        const questionType = await QuestionType.findByPk(question_type_id);
        const level = await Level.findByPk(level_id);
        const lo = await LO.findByPk(lo_id);

        if (!questionType) {
            return res.status(400).json({ message: 'Loại câu hỏi không tồn tại' });
        }
        if (!level) {
            return res.status(400).json({ message: 'Độ khó không tồn tại' });
        }
        if (!lo) {
            return res.status(400).json({ message: 'Learning Outcome không tồn tại' });
        }

        const newQuestion = await Question.create({
            question_type_id,
            level_id,
            question_text,
            lo_id,
        });

        res.status(201).json(newQuestion);
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi tạo câu hỏi', error: error.message });
    }
};

// Cập nhật thông tin một câu hỏi
exports.updateQuestion = async (req, res) => {
    try {
        const { question_type_id, level_id, question_text } = req.body;

        const question = await Question.findByPk(req.params.id);
        if (!question) {
            return res.status(404).json({ message: 'Câu hỏi không tồn tại' });
        }

        // Kiểm tra các trường nếu được cung cấp
        if (question_type_id) {
            const questionType = await QuestionType.findByPk(question_type_id);
            if (!questionType) {
                return res.status(400).json({ message: 'Loại câu hỏi không tồn tại' });
            }
        }
        if (level_id) {
            const level = await Level.findByPk(level_id);
            if (!level) {
                return res.status(400).json({ message: 'Độ khó không tồn tại' });
            }
        }

        await question.update({
            question_type_id: question_type_id || question.question_type_id,
            level_id: level_id || question.level_id,
            question_text: question_text || question.question_text,
        });

        res.status(200).json(question);
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi cập nhật câu hỏi', error: error.message });
    }
};

// Xóa một câu hỏi
exports.deleteQuestion = async (req, res) => {
    try {
        const question = await Question.findByPk(req.params.id);
        if (!question) {
            return res.status(404).json({ message: 'Câu hỏi không tồn tại' });
        }

        await question.destroy();
        res.status(200).json({ message: 'Xóa câu hỏi thành công' });
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi xóa câu hỏi', error: error.message });
    }
};

// Lấy danh sách câu hỏi theo lo_id (có phân trang)
exports.getQuestionsByLoId = async (req, res) => {
    try {
        const { page = 1, limit = 10 } = req.query;
        const offset = (page - 1) * limit;

        const lo = await LO.findByPk(req.params.lo_id);
        if (!lo) {
            return res.status(404).json({ message: 'Learning Outcome không tồn tại' });
        }

        const questions = await Question.findAndCountAll({
            where: { lo_id: req.params.lo_id },
            limit: parseInt(limit),
            offset: parseInt(offset),
            include: [
                { model: QuestionType, attributes: ['question_type_id', 'name'] },
                { model: Level, attributes: ['level_id', 'name'] },
                { model: LO, attributes: ['lo_id', 'name'] },
                { model: Answer, attributes: ['answer_id', 'answer_text', 'iscorrect'] },
            ],
        });

        res.status(200).json({
            totalItems: questions.count,
            totalPages: Math.ceil(questions.count / limit),
            currentPage: parseInt(page),
            questions: questions.rows,
        });
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi lấy danh sách câu hỏi theo lo_id', error: error.message });
    }
};


exports.importQuestionsFromCSV = async (req, res) => {
    try {
        console.log('Received request to import questions');
        console.log('req.file:', req.file);
        console.log('req.body:', req.body);

        if (!req.file) {
            console.log('No file uploaded');
            return res.status(400).json({ message: 'Vui lòng tải lên file CSV' });
        }

        const questions = [];
        const errors = [];
        const rows = [];

        console.log('Starting to read CSV file:', req.file.path);
        fs.createReadStream(req.file.path)
            .pipe(csv.parse({ headers: true, delimiter: ',', skipEmptyLines: true }))
            .on('data', (row) => {
                rows.push(row);
            })
            .on('error', (error) => {
                console.error('Error reading CSV file:', error.message);
                res.status(500).json({ message: 'Lỗi khi đọc file CSV', error: error.message });
            })
            .on('end', async () => {
                console.log('Finished reading CSV file');
                try {
                    for (const row of rows) {
                        try {
                            console.log('Processing row:', row);
                            const { question_type_id, level_id, question_text, lo_id, explanation } = row;

                            if (!question_type_id || !level_id || !question_text || !lo_id) {
                                console.log('Missing required fields in row:', row);
                                errors.push({ row, error: 'Thiếu các trường bắt buộc' });
                                continue;
                            }

                            const questionType = await QuestionType.findByPk(question_type_id);
                            const level = await Level.findByPk(level_id);
                            const lo = await LO.findByPk(lo_id);

                            if (!questionType) {
                                errors.push({ row, error: `QuestionType với ID ${question_type_id} không tồn tại` });
                                continue;
                            }
                            if (!level) {
                                errors.push({ row, error: `Level với ID ${level_id} không tồn tại` });
                                continue;
                            }
                            if (!lo) {
                                errors.push({ row, error: `LO với ID ${lo_id} không tồn tại` });
                                continue;
                            }

                            const newQuestion = await Question.create({
                                question_type_id: parseInt(question_type_id),
                                level_id: parseInt(level_id),
                                question_text,
                                lo_id: parseInt(lo_id),
                                explanation: explanation || null, // thêm trường giải thích
                            });

                            const answers = [];
                            for (let i = 1; i <= 4; i++) {
                                const answerText = row[`answer_${i}`];
                                const isCorrect = row[`iscorrect_${i}`];

                                if (answerText && isCorrect !== undefined) {
                                    const answer = await Answer.create({
                                        question_id: newQuestion.question_id,
                                        answer_text: answerText,
                                        iscorrect: isCorrect === 'true' || isCorrect === '1',
                                    });
                                    answers.push(answer);
                                }
                            }

                            questions.push({ ...newQuestion.toJSON(), Answers: answers });
                        } catch (error) {
                            errors.push({ row, error: error.message });
                        }
                    }

                    try {
                        fs.unlinkSync(req.file.path);
                    } catch (error) {
                        console.error('Error deleting temporary file:', error.message);
                    }

                    if (errors.length > 0) {
                        return res.status(400).json({
                            message: 'Có lỗi xảy ra khi nhập câu hỏi',
                            errors,
                            questionsImported: questions,
                        });
                    }

                    res.status(201).json({
                        message: 'Nhập câu hỏi thành công',
                        questions: questions,
                    });
                } catch (error) {
                    res.status(500).json({ message: 'Lỗi khi xử lý dữ liệu CSV', error: error.message });
                }
            });
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi nhập câu hỏi từ CSV', error: error.message });
    }
};




exports.importQuestionsFromExcel = async (req, res) => {
    const file = req.file;
    if (!file) return res.status(400).json({ error: "No file uploaded" });

    try {
        const workbook = xlsx.readFile(file.path);
        const sheetNames = workbook.SheetNames;
        const sheet1 = workbook.Sheets[sheetNames[0]];
        const sheet2 = workbook.Sheets[sheetNames[1]];

        // 1. Đọc sheet 2: lấy đúng 2 cột KQHT và tên KQHT
        const loRows = xlsx.utils.sheet_to_json(sheet2, { header: 1 });
        const loMap = {}; // { KQHT: lo_id }

        for (let i = 1; i < loRows.length; i++) { // Bỏ header
            const row = loRows[i];
            if (!row || !row[0] || !row[1]) continue;
            const kqht = String(row[0]).trim(); // "KQHT1", "KQHT2", ...
            const tenKQHT = String(row[1]).trim();

            // Kiểm tra tên KQHT đã có trong DB chưa
            let lo = await LO.findOne({ where: { name: tenKQHT } });
            if (!lo) {
                lo = await LO.create({ name: tenKQHT });
            }
            loMap[kqht] = lo.lo_id;
        }

        // 2. Đọc sheet 1 như cũ, nhưng lấy lo_id từ loMap
        const rawData = xlsx.utils.sheet_to_json(sheet1, { header: 1 });
        fs.unlinkSync(file.path);

        let currentKQHT = '';
        let currentLoId = '';
        let i = 0;
        let imported = 0;
        let errors = [];
        const questions = [];

        while (i < rawData.length) {
            const row = rawData[i];
            // Check for KQHTx để cập nhật currentKQHT và currentLoId
            if (row && typeof row[0] === 'string' && row[0].trim().startsWith('KQHT')) {
                currentKQHT = row[0].trim().split(' ')[0]; // Lấy "KQHT1", "KQHT2",...
                currentLoId = loMap[currentKQHT] || '';
                if (!currentLoId) {
                    errors.push({ row: i + 1, error: `Không tìm thấy LO cho ${currentKQHT}` });
                }
                i += 2; // Skip header row
                continue;
            }
            // ... giữ nguyên logic cũ, chỉ thay lo_id thành currentLoId ...
            if (i + 3 >= rawData.length) break;
            const q_row = rawData[i];
            const a_row = rawData[i];
            const b_row = rawData[i + 1];
            const c_row = rawData[i + 2];
            const d_row = rawData[i + 3];
            const question_type_id = 1;
            const level_id = (q_row[0] !== undefined && q_row[0] !== null && q_row[0] !== '') ? parseInt(q_row[0]) : 1;
            const question_text = q_row[2] ? String(q_row[2]).trim() : '';
            const answer_a = a_row[3] ? String(a_row[3]).replace('A.', '').trim() : '';
            const answer_b = b_row[3] ? String(b_row[3]).replace('B.', '').trim() : '';
            const answer_c = c_row[3] ? String(c_row[3]).replace('C.', '').trim() : '';
            const answer_d = d_row[3] ? String(d_row[3]).replace('D.', '').trim() : '';
            const correct = a_row[4] ? String(a_row[4]).trim().toUpperCase() : '';
            const iscorrect = [
                correct === 'A' ? 1 : 0,
                correct === 'B' ? 1 : 0,
                correct === 'C' ? 1 : 0,
                correct === 'D' ? 1 : 0,
            ];
            const explanation = a_row[5] ? String(a_row[5]).trim() : '';
            if (!question_text || !currentLoId || !answer_a || !answer_b || !answer_c || !answer_d) {
                errors.push({ row: i + 1, error: 'Thiếu dữ liệu bắt buộc hoặc không xác định được LO' });
                i += 4;
                continue;
            }
            try {
                const newQuestion = await Question.create({
                    question_type_id,
                    level_id,
                    question_text,
                    lo_id: currentLoId,
                    explanation,
                });
                const answers = [
                    { answer_text: answer_a, iscorrect: iscorrect[0] },
                    { answer_text: answer_b, iscorrect: iscorrect[1] },
                    { answer_text: answer_c, iscorrect: iscorrect[2] },
                    { answer_text: answer_d, iscorrect: iscorrect[3] },
                ];
                for (const ans of answers) {
                    await Answer.create({
                        question_id: newQuestion.question_id,
                        answer_text: ans.answer_text,
                        iscorrect: ans.iscorrect,
                    });
                }
                imported++;
                questions.push(newQuestion);
            } catch (err) {
                errors.push({ row: i + 1, error: err.message });
            }
            i += 4;
        }
        res.status(errors.length > 0 ? 207 : 201).json({
            message: `Đã import ${imported} câu hỏi.`,
            errors,
            questions,
        });
    } catch (err) {
        res.status(500).json({ error: "Lỗi đọc file Excel", details: err.message });
    }
};


// controllers/questionController.js


// Hàm tiện ích để lấy câu hỏi (tách logic từ getQuestionsByLOs)
const fetchQuestionsByLOs = async (loIds, totalQuestions, difficultyRatio, type = null) => {
    // Kiểm tra đầu vào
    if (!Array.isArray(loIds) || loIds.length === 0) {
        throw new Error('loIds phải là một mảng không rỗng');
    }
    if (!Number.isInteger(totalQuestions) || totalQuestions <= 0) {
        throw new Error('totalQuestions phải là số nguyên dương');
    }
    if (!difficultyRatio || typeof difficultyRatio !== 'object') {
        throw new Error('difficultyRatio phải là một object');
    }

    const { easy = 0, medium = 0, hard = 0 } = difficultyRatio;
    const totalRatio = easy + medium + hard;
    if (totalRatio !== 100) {
        throw new Error('Tổng tỷ lệ (easy + medium + hard) phải bằng 100');
    }

    // Tính số lượng câu hỏi cho từng mức độ khó
    let easyCount = Math.round((easy / 100) * totalQuestions);
    let mediumCount = Math.round((medium / 100) * totalQuestions);
    let hardCount = totalQuestions - easyCount - mediumCount;

    // Truy vấn câu hỏi theo từng mức độ khó
    const questions = [];
    const excludeQuestionIds = new Set();

    // Hàm lấy câu hỏi ngẫu nhiên cho một mức độ khó
    const fetchQuestionsByDifficulty = async (levelId, count) => {
        if (count <= 0) return [];

        const fetchedQuestions = await Question.findAll({
            attributes: [
                'question_id',
                'question_type_id',
                'level_id',
                'question_text',
                'lo_id',
                'explanation',
            ],
            where: {
                lo_id: { [Op.in]: loIds },
                level_id: levelId,
                question_id: { [Op.notIn]: Array.from(excludeQuestionIds) },
                ...(type && { question_type_id: type }), // Lọc theo type nếu có
            },
            order: literal('random()'),
            limit: count,
            include: [
                {
                    model: LO,
                    as: 'LO',
                    attributes: ['lo_id', 'name'],
                },
                {
                    model: Answer,
                    as: 'Answers',
                    attributes: ['answer_id', 'answer_text', 'iscorrect'],
                },
                {
                    model: QuestionType,
                    as: 'QuestionType',
                    attributes: ['question_type_id', 'name'],
                },
                {
                    model: Level,
                    as: 'Level',
                    attributes: ['level_id', 'name'],
                },
            ],
        });

        fetchedQuestions.forEach(q => excludeQuestionIds.add(q.question_id));
        return fetchedQuestions;
    };

    // Lấy câu hỏi dễ (level_id = 1)
    let easyQuestions = await fetchQuestionsByDifficulty(1, easyCount);
    questions.push(...easyQuestions);
    easyCount = easyQuestions.length;

    // Lấy câu hỏi trung bình (level_id = 2)
    let mediumQuestions = await fetchQuestionsByDifficulty(2, mediumCount);
    questions.push(...mediumQuestions);
    mediumCount = mediumQuestions.length;

    // Lấy câu hỏi khó (level_id = 3)
    let hardQuestions = await fetchQuestionsByDifficulty(3, hardCount);
    questions.push(...hardQuestions);
    hardCount = hardQuestions.length;

    // Nếu không đủ câu hỏi, thử lấy thêm từ các mức độ khác
    let remainingCount = totalQuestions - questions.length;
    if (remainingCount > 0) {
        const additionalMedium = await fetchQuestionsByDifficulty(2, remainingCount);
        questions.push(...additionalMedium);
        remainingCount -= additionalMedium.length;

        if (remainingCount > 0) {
            const additionalEasy = await fetchQuestionsByDifficulty(1, remainingCount);
            questions.push(...additionalEasy);
            remainingCount -= additionalEasy.length;
        }

        if (remainingCount > 0) {
            const additionalHard = await fetchQuestionsByDifficulty(3, remainingCount);
            questions.push(...additionalHard);
            remainingCount -= additionalHard.length;
        }
    }

    // Kiểm tra số lượng câu hỏi thực tế
    if (questions.length < totalQuestions) {
        throw new Error(
            `Không đủ câu hỏi theo yêu cầu. Yêu cầu ${totalQuestions} câu, nhưng chỉ tìm thấy ${questions.length} câu.`
        );
    }

    // Trả về danh sách câu hỏi
    return questions.map(q => ({
        question_id: q.question_id,
        question_type: {
            question_type_id: q.QuestionType?.question_type_id,
            name: q.QuestionType?.name,
        },
        level: {
            level_id: q.Level?.level_id,
            name: q.Level?.name,
        },
        question_text: q.question_text,
        lo_id: q.lo_id,
        lo_name: q.LO?.name,
        explanation: q.explanation,
        answers: q.Answers.map(a => ({
            answer_id: a.answer_id,
            answer_text: a.answer_text,
            iscorrect: a.iscorrect,
        })),
    }));
};

// Giữ nguyên getQuestionsByLOs để sử dụng như một route handler
exports.getQuestionsByLOs = async (req, res) => {
    try {
        const { loIds, totalQuestions, difficultyRatio } = req.body;

        const questions = await fetchQuestionsByLOs(loIds, totalQuestions, difficultyRatio);

        res.status(200).json({
            total: questions.length,
            questions,
        });
    } catch (error) {
        res.status(500).json({
            error: 'Lỗi khi lấy danh sách câu hỏi',
            details: error.message,
        });
    }
};

