// Test script để kiểm tra toàn bộ flow bảng xếp hạng
const axios = require('axios');

const BASE_URL = 'http://localhost:8888/api';

async function testLeaderboardFlow() {
    try {
        console.log('Testing Leaderboard Flow...');
        
        // Test 1: <PERSON><PERSON> as teacher để lấy token
        console.log('\n1. <PERSON><PERSON> as teacher...');
        let token = null;
        try {
            const loginResponse = await axios.post(`${BASE_URL}/users/login`, {
                email: '<EMAIL>', // Thay bằng email teacher thực tế
                password: 'password123'
            });
            token = loginResponse.data.token;
            console.log('✅ Login successful, token:', token ? 'received' : 'not received');
        } catch (error) {
            console.log('❌ Login failed:', error.response?.data || error.message);
            console.log('Trying with different credentials...');
            
            // Thử với credentials khác
            try {
                const loginResponse2 = await axios.post(`${BASE_URL}/users/login`, {
                    email: '<EMAIL>',
                    password: 'admin123'
                });
                token = loginResponse2.data.token;
                console.log('✅ Login successful with admin, token:', token ? 'received' : 'not received');
            } catch (error2) {
                console.log('❌ Admin login also failed:', error2.response?.data || error2.message);
            }
        }

        if (!token) {
            console.log('❌ Cannot proceed without authentication token');
            return;
        }

        // Test 2: Lấy danh sách quiz
        console.log('\n2. Getting quiz list...');
        try {
            const quizzesResponse = await axios.get(`${BASE_URL}/quizzes`);
            const quizzes = quizzesResponse.data.quizzes || [];
            console.log('✅ Available quizzes:', quizzes.map(q => ({
                id: q.quiz_id,
                name: q.name,
                status: q.status
            })));
            
            if (quizzes.length === 0) {
                console.log('❌ No quizzes available for testing');
                return;
            }

            // Lấy quiz đầu tiên để test
            const testQuiz = quizzes[0];
            console.log(`Using quiz ${testQuiz.quiz_id} for testing`);

            // Test 3: Lấy bảng xếp hạng hiện tại
            console.log(`\n3. Getting current leaderboard for quiz ${testQuiz.quiz_id}...`);
            try {
                const leaderboardResponse = await axios.get(`${BASE_URL}/quizzes/${testQuiz.quiz_id}/leaderboard`);
                console.log('✅ Current leaderboard:', leaderboardResponse.data);
            } catch (error) {
                console.log('❌ Error getting leaderboard:', error.response?.data || error.message);
            }

            // Test 4: Trigger hiển thị bảng xếp hạng
            console.log(`\n4. Triggering leaderboard display for quiz ${testQuiz.quiz_id}...`);
            try {
                const triggerResponse = await axios.post(
                    `${BASE_URL}/quizzes/${testQuiz.quiz_id}/leaderboard`,
                    {},
                    {
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    }
                );
                console.log('✅ Leaderboard display triggered:', triggerResponse.data);
            } catch (error) {
                console.log('❌ Error triggering leaderboard:', error.response?.data || error.message);
            }

        } catch (error) {
            console.log('❌ Error getting quizzes:', error.response?.data || error.message);
        }

    } catch (error) {
        console.error('Test failed:', error.message);
    }
}

// Chạy test
testLeaderboardFlow();
