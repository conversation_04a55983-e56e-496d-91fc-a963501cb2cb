"use client";
import React from "react";
import Link from "next/link";
import { useAuthStatus } from "@/hooks/use-auth";
import { useLogout } from "@/hooks/use-auth";
import { User, LogOut, Trophy, Star, Zap } from "lucide-react";
import { cn } from "@/lib/utils";
import { useSidebarContext } from "@/hooks/use-sidebar";
import { getCurrentRole } from "@/lib/auth/role-manager";
import { useGamification } from "@/hooks/use-gamification";
import { Badge } from "@/components/ui/badge";

export const UserButton: React.FC = () => {
  const { getUser } = useAuthStatus();
  const { isMobile } = useSidebarContext();
  const user = getUser();
  const displayName = user?.fullName || "Người dùng";
  const { logout } = useLogout();

  // Gamification data
  const { userGamification, levelColor, formattedPoints } = useGamification();

  // <PERSON><PERSON><PERSON> chuyển đổi vai trò từ tiế<PERSON>h sang tiếng Việt
  const translateRole = (role?: string | null): string => {
    if (!role) return "Người dùng";

    switch (role.toLowerCase()) {
      case "admin":
        return "Quản trị viên";
      case "teacher":
        return "Giảng viên";
      case "student":
        return "Sinh viên";
      default:
        return "Người dùng";
    }
  };

  // Lấy vai trò của người dùng từ role-manager
  const currentRole = getCurrentRole();
  const translatedRole = translateRole(currentRole);

  // Get level icon
  const getLevelIcon = (level: number) => {
    if (level >= 10) return <Trophy className="w-3 h-3" />;
    if (level >= 5) return <Star className="w-3 h-3" />;
    return <Zap className="w-3 h-3" />;
  };

  return (
    <div className="flex items-center gap-2">
      {/* Level Badge - chỉ hiển thị cho student */}
      {currentRole === "student" && userGamification && !isMobile && (
        <Badge
          variant="secondary"
          className="flex items-center gap-1 px-2 py-1"
          style={{ backgroundColor: `${levelColor}20`, color: levelColor }}
        >
          {getLevelIcon(userGamification.current_level)}
          <span className="font-medium text-xs">
            Lv.{userGamification.current_level}
          </span>
          <span className="text-xs opacity-75">{formattedPoints}</span>
        </Badge>
      )}

      {/* User Button */}
      <Link
        href="#"
        onClick={logout}
        className={cn(
          "flex items-center gap-1.5 rounded-md font-semibold text-sm text-primary whitespace-nowrap",
          "hover:bg-accent focus:bg-accent transition-colors border border-transparent hover:border-primary focus:border-primary group",
          isMobile ? "px-2 py-1.5" : "px-4 py-2"
        )}
      >
        {/* Avatar user */}
        <div
          className={cn(
            "flex items-center justify-center rounded-full bg-primary/10 text-primary",
            isMobile ? "h-7 w-7" : "h-8 w-8"
          )}
        >
          <User className={cn(isMobile ? "h-3.5 w-3.5" : "h-4 w-4")} />
        </div>

        {/* Thông tin user - nhỏ gọn trên mobile */}
        <div className="flex flex-col items-start flex-grow">
          <span
            className={cn(
              "font-medium leading-tight",
              isMobile ? "text-xs" : "text-sm"
            )}
          >
            {displayName}
          </span>
          <div className="flex items-center gap-1">
            <span
              className={cn(
                "text-muted-foreground",
                isMobile ? "text-[10px]" : "text-xs"
              )}
            >
              {translatedRole}
            </span>
            {/* Level info cho mobile student */}
            {currentRole === "student" && userGamification && isMobile && (
              <span
                className="text-[10px] font-medium"
                style={{ color: levelColor }}
              >
                • Lv.{userGamification.current_level}
              </span>
            )}
          </div>
        </div>

        {/* Icon đăng xuất */}
        <div
          className={cn(
            "flex items-center justify-center rounded-full text-destructive",
            isMobile ? "h-7 w-7" : "h-8 w-8"
          )}
        >
          <LogOut className={cn(isMobile ? "h-3.5 w-3.5" : "h-4 w-4")} />
        </div>
      </Link>
    </div>
  );
};
