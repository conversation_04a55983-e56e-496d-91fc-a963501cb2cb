const { Course, User, Program, Subject, CourseResult } = require('../models');

exports.getAllCourses = async (req, res) => {
    try {
        const { page = 1, limit = 10 } = req.query;
        const offset = (page - 1) * limit;

        const courses = await Course.findAndCountAll({
            limit: parseInt(limit),
            offset: parseInt(offset),
            include: [
                { model: User, attributes: ['user_id', 'name'] },
                { model: Program, attributes: ['program_id', 'name'] },
                { model: Subject, attributes: ['subject_id', 'name'] },
                { model: CourseResult, attributes: ['result_id', 'average_score'] },
            ],
        });

        res.status(200).json({
            totalItems: courses.count,
            totalPages: Math.ceil(courses.count / limit),
            currentPage: parseInt(page),
            courses: courses.rows,
        });
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi lấy danh sách khóa học', error: error.message });
    }
};

exports.getCourseById = async (req, res) => {
    try {
        const course = await Course.findByPk(req.params.id, {
            include: [
                { model: User, attributes: ['user_id', 'name'] },
                { model: Program, attributes: ['program_id', 'name'] },
                { model: Subject, attributes: ['subject_id', 'name'] },
                { model: CourseResult, attributes: ['result_id', 'average_score'] },
            ],
        });

        if (!course) return res.status(404).json({ message: 'Khóa học không tồn tại' });
        res.status(200).json(course);
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi lấy thông tin khóa học', error: error.message });
    }
};

exports.createCourse = async (req, res) => {
    try {
        const { user_id, name, description, start_date, end_date, program_id } = req.body;

        if (!user_id || !name || !program_id) {
            return res.status(400).json({ message: 'Thiếu các trường bắt buộc' });
        }

        const user = await User.findByPk(user_id);
        const program = await Program.findByPk(program_id);

        if (!user) return res.status(400).json({ message: 'Người dùng không tồn tại' });
        if (!program) return res.status(400).json({ message: 'Chương trình không tồn tại' });

        const newCourse = await Course.create({
            user_id,
            name,
            description,
            start_date,
            end_date,
            program_id,
        });

        res.status(201).json(newCourse);
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi tạo khóa học', error: error.message });
    }
};

exports.updateCourse = async (req, res) => {
    try {
        const { user_id, name, description, start_date, end_date, program_id } = req.body;

        const course = await Course.findByPk(req.params.id);
        if (!course) return res.status(404).json({ message: 'Khóa học không tồn tại' });

        if (user_id) {
            const user = await User.findByPk(user_id);
            if (!user) return res.status(400).json({ message: 'Người dùng không tồn tại' });
        }
        if (program_id) {
            const program = await Program.findByPk(program_id);
            if (!program) return res.status(400).json({ message: 'Chương trình không tồn tại' });
        }

        await course.update({
            user_id: user_id || course.user_id,
            name: name || course.name,
            description: description || course.description,
            start_date: start_date || course.start_date,
            end_date: end_date || course.end_date,
            program_id: program_id || course.program_id,
        });

        res.status(200).json(course);
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi cập nhật khóa học', error: error.message });
    }
};

exports.deleteCourse = async (req, res) => {
    try {
        const course = await Course.findByPk(req.params.id);
        if (!course) return res.status(404).json({ message: 'Khóa học không tồn tại' });

        await course.destroy();
        res.status(200).json({ message: 'Xóa khóa học thành công' });
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi xóa khóa học', error: error.message });
    }
};