const { Role, User } = require('../models');

// L<PERSON>y danh sách tất cả vai trò
exports.getAllRoles = async (req, res) => {
    try {
        const { page = 1, limit = 10 } = req.query;
        const offset = (page - 1) * limit;

        const roles = await Role.findAndCountAll({
            limit: parseInt(limit),
            offset: parseInt(offset),
            include: [{ model: User, attributes: ['user_id', 'name'] }],
        });

        res.status(200).json({
            totalItems: roles.count,
            totalPages: Math.ceil(roles.count / limit),
            currentPage: parseInt(page),
            roles: roles.rows,
        });
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi lấy danh sách vai trò', error: error.message });
    }
};

// Lấy thông tin chi tiết một vai trò
exports.getRoleById = async (req, res) => {
    try {
        const role = await Role.findByPk(req.params.id, {
            include: [{ model: User, attributes: ['user_id', 'name'] }],
        });

        if (!role) {
            return res.status(404).json({ message: 'Vai trò không tồn tại' });
        }

        res.status(200).json(role);
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi lấy thông tin vai trò', error: error.message });
    }
};

// Tạo một vai trò mới
exports.createRole = async (req, res) => {
    try {
        const { name } = req.body;

        if (!name) {
            return res.status(400).json({ message: 'Tên vai trò là bắt buộc' });
        }

        const newRole = await Role.create({ name });

        res.status(201).json(newRole);
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi tạo vai trò', error: error.message });
    }
};

// Cập nhật thông tin một vai trò
exports.updateRole = async (req, res) => {
    try {
        const { name } = req.body;

        const role = await Role.findByPk(req.params.id);
        if (!role) {
            return res.status(404).json({ message: 'Vai trò không tồn tại' });
        }

        await role.update({ name: name || role.name });

        res.status(200).json(role);
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi cập nhật vai trò', error: error.message });
    }
};

// Xóa một vai trò
exports.deleteRole = async (req, res) => {
    try {
        const role = await Role.findByPk(req.params.id);
        if (!role) {
            return res.status(404).json({ message: 'Vai trò không tồn tại' });
        }

        await role.destroy();
        res.status(200).json({ message: 'Xóa vai trò thành công' });
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi xóa vai trò', error: error.message });
    }
};