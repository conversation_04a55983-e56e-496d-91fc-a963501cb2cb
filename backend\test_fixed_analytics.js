/**
 * Test script for fixed Advanced Analytics APIs
 * Tests the new LO-Chapter relationship approach
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function testFixedAnalytics() {
    console.log('🚀 Testing Fixed Advanced Analytics APIs\n');

    try {
        // Step 1: Login
        console.log('📡 Step 1: Login...');
        const loginResponse = await axios.post(`${BASE_URL}/api/auth/login`, {
            email: '<EMAIL>',  // Update with your admin credentials
            password: 'admin123'
        });

        if (!loginResponse.data.success || !loginResponse.data.token) {
            console.log('❌ Login failed');
            return;
        }

        const token = loginResponse.data.token;
        console.log('✅ Login successful');

        const headers = { 'Authorization': `Bearer ${token}` };

        // Step 2: Test LO-Chapter Relationship
        console.log('\n📡 Step 2: Testing LO-Chapter Relationship...');
        try {
            const relationshipResponse = await axios.get(`${BASE_URL}/api/advanced-analytics/test/lo-chapter-relationship`, { headers });
            
            if (relationshipResponse.data.success) {
                console.log('✅ LO-Chapter Relationship works!');
                const data = relationshipResponse.data.data;
                console.log(`   - Found ${data.total_los} LOs`);
                console.log(`   - Mapped ${data.total_chapters_mapped} chapters`);
                console.log(`   - Question history: ${data.total_question_history} records`);
                
                if (data.total_chapters_mapped === 0) {
                    console.log('⚠️  Warning: No LO-Chapter mappings found. Check ChapterLO table.');
                }
                
                if (data.total_question_history === 0) {
                    console.log('⚠️  Warning: No question history found. Check UserQuestionHistory table.');
                }
            } else {
                console.log('❌ LO-Chapter Relationship failed');
                return;
            }
        } catch (error) {
            console.log('❌ LO-Chapter Relationship error:', error.response?.data?.message || error.message);
            return;
        }

        // Step 3: Test Student Score Analysis
        console.log('\n📡 Step 3: Testing Student Score Analysis...');
        try {
            const scoreResponse = await axios.get(`${BASE_URL}/api/advanced-analytics/student/score-analysis?user_id=122&time_period=3m`, { headers });
            
            if (scoreResponse.data.success) {
                console.log('✅ Student Score Analysis works!');
                
                if (scoreResponse.data.data.message) {
                    console.log('   Message:', scoreResponse.data.data.message);
                } else {
                    const overall = scoreResponse.data.data.overall_performance;
                    console.log(`   - Total attempts: ${overall.total_attempts}`);
                    console.log(`   - Accuracy: ${overall.accuracy_rate}%`);
                    console.log(`   - Avg response time: ${overall.avg_response_time}s`);
                }
            } else {
                console.log('❌ Student Score Analysis failed');
            }
        } catch (error) {
            console.log('❌ Student Score Analysis error:', error.response?.data?.message || error.message);
            console.log('   This might be due to:');
            console.log('   - User ID 122 does not exist');
            console.log('   - No data for this user in the time period');
            console.log('   - Database connection issues');
        }

        // Step 4: Test Learning Outcome Mastery
        console.log('\n📡 Step 4: Testing Learning Outcome Mastery...');
        try {
            const masteryResponse = await axios.get(`${BASE_URL}/api/advanced-analytics/student/learning-outcome-mastery?user_id=122&mastery_threshold=0.7`, { headers });
            
            if (masteryResponse.data.success) {
                console.log('✅ Learning Outcome Mastery works!');
                
                if (masteryResponse.data.data.message) {
                    console.log('   Message:', masteryResponse.data.data.message);
                } else {
                    const summary = masteryResponse.data.data.mastery_summary;
                    console.log(`   - Total LOs: ${summary.total_los}`);
                    console.log(`   - Mastered: ${summary.mastered_count}`);
                    console.log(`   - In Progress: ${summary.in_progress_count}`);
                }
            } else {
                console.log('❌ Learning Outcome Mastery failed');
            }
        } catch (error) {
            console.log('❌ Learning Outcome Mastery error:', error.response?.data?.message || error.message);
        }

        // Step 5: Test Improvement Suggestions
        console.log('\n📡 Step 5: Testing Improvement Suggestions...');
        try {
            const suggestionsResponse = await axios.get(`${BASE_URL}/api/advanced-analytics/student/improvement-suggestions?user_id=122&suggestion_depth=detailed`, { headers });
            
            if (suggestionsResponse.data.success) {
                console.log('✅ Improvement Suggestions works!');
                
                if (suggestionsResponse.data.data.message) {
                    console.log('   Message:', suggestionsResponse.data.data.message);
                } else {
                    const suggestions = suggestionsResponse.data.data.suggestions;
                    console.log(`   - Generated ${suggestions.length} suggestions`);
                    if (suggestions.length > 0) {
                        console.log(`   - First suggestion: ${suggestions[0].lo_name} (${suggestions[0].priority} priority)`);
                    }
                }
            } else {
                console.log('❌ Improvement Suggestions failed');
            }
        } catch (error) {
            console.log('❌ Improvement Suggestions error:', error.response?.data?.message || error.message);
        }

        // Step 6: Test Difficulty Heatmap
        console.log('\n📡 Step 6: Testing Difficulty Heatmap...');
        try {
            const heatmapResponse = await axios.get(`${BASE_URL}/api/advanced-analytics/difficulty/heatmap?time_period=30d&subject_id=1`, { headers });
            
            if (heatmapResponse.data.success) {
                console.log('✅ Difficulty Heatmap works!');
                
                const heatmap = heatmapResponse.data.data.heatmap_data;
                console.log(`   - Generated ${heatmap.length} heatmap cells`);
                if (heatmap.length > 0) {
                    console.log(`   - Sample: ${heatmap[0].chapter} - ${heatmap[0].level} (${heatmap[0].difficulty_score} difficulty)`);
                }
            } else {
                console.log('❌ Difficulty Heatmap failed');
            }
        } catch (error) {
            console.log('❌ Difficulty Heatmap error:', error.response?.data?.message || error.message);
        }

        console.log('\n🎉 All tests completed!');
        console.log('\n📊 Summary:');
        console.log('- If LO-Chapter relationship works, the core fix is successful');
        console.log('- If student analytics work, the data processing is correct');
        console.log('- If you see "No data" messages, that\'s normal for empty databases');
        console.log('- Any column errors indicate remaining issues to fix');

    } catch (error) {
        console.error('💥 Test runner error:', error.message);
    }
}

// Run the tests
if (require.main === module) {
    testFixedAnalytics().catch(error => {
        console.error('💥 Test error:', error.message);
        process.exit(1);
    });
}

module.exports = { testFixedAnalytics };
