# 📊 Advanced Analytics API Documentation

## Overview
Comprehensive data analysis endpoints for educational insights, providing professional-grade analytics for learning outcomes assessment.

## Base URL
```
/api/advanced-analytics
```

## Authentication
All endpoints require authentication token and appropriate role authorization.

---

## 📈 Performance Analytics

### 1. Time Series Performance Data
**GET** `/performance/time-series`

Analyzes score trends over time with various aggregation options.

**Query Parameters:**
- `program_id` (optional): Filter by program
- `subject_id` (optional): Filter by subject  
- `quiz_id` (optional): Filter by specific quiz
- `user_id` (optional): Filter by specific user
- `time_period` (optional): `7d`, `30d`, `3m`, `6m`, `1y` (default: `7d`)
- `aggregation` (optional): `daily`, `weekly`, `monthly` (default: `daily`)

**Response Example:**
```json
{
  "success": true,
  "data": {
    "time_series": [
      {
        "period": "2024-01-15T00:00:00.000Z",
        "avg_score": "7.25",
        "total_attempts": 45,
        "high_score_rate": "22.22",
        "score_range": 8.5,
        "score_stddev": "1.85"
      }
    ],
    "trend_analysis": {
      "trend_direction": "improving",
      "trend_strength": "0.0234",
      "slope": "0.0234",
      "r_squared": "0.7845",
      "confidence": "high"
    }
  }
}
```

### 2. Score Distribution Analysis
**GET** `/performance/score-distribution`

Provides histogram data and statistical analysis of score distributions.

**Query Parameters:**
- `program_id`, `subject_id`, `quiz_id` (optional): Filtering options
- `bins` (optional): Number of histogram bins (default: 10)
- `comparison_period` (optional): `previous_month`, `previous_quarter`, etc.

**Response Example:**
```json
{
  "success": true,
  "data": {
    "current_period": {
      "histogram": [
        {
          "bin_start": 0,
          "bin_end": 1,
          "count": 5,
          "percentage": "8.33"
        }
      ],
      "statistics": {
        "mean": "6.75",
        "median": "7.00",
        "std_dev": "2.15",
        "skewness": "-0.234",
        "kurtosis": "0.567"
      }
    }
  }
}
```

### 3. Learning Outcomes Comparison
**GET** `/performance/learning-outcomes`

Provides radar chart data for LO performance comparison.

**Query Parameters:**
- `program_id`, `subject_id`, `user_id` (optional): Filtering options
- `comparison_type` (optional): `average`, `top_performer`, `user_vs_average`

### 4. Completion Funnel Analysis
**GET** `/performance/completion-funnel`

Shows conversion rates through different stages of quiz completion.

**Response Example:**
```json
{
  "success": true,
  "data": {
    "funnel_stages": [
      {
        "stage": "Registered Users",
        "count": 150,
        "percentage": 100,
        "conversion_rate": 100
      },
      {
        "stage": "Started Quiz", 
        "count": 120,
        "percentage": "80.00",
        "conversion_rate": "80.00"
      }
    ],
    "summary": {
      "completion_rate": "65.33",
      "pass_rate": "78.57",
      "excellence_rate": "45.23"
    }
  }
}
```

---

## 🔥 Learning Difficulty Analysis

### 1. Difficulty Heatmap
**GET** `/difficulty/heatmap`

Shows question difficulty across chapters and levels.

**Query Parameters:**
- `program_id`, `subject_id` (optional): Filtering options
- `time_period` (optional): Analysis time window

**Response Example:**
```json
{
  "success": true,
  "data": {
    "heatmap_data": [
      {
        "chapter": "Introduction to Programming",
        "level": "Basic",
        "difficulty_score": "25.67",
        "accuracy_rate": "78.45",
        "avg_response_time": "45.23"
      }
    ],
    "axis_labels": {
      "chapters": ["Chapter 1", "Chapter 2"],
      "levels": ["Basic", "Intermediate", "Advanced"]
    },
    "summary": {
      "avg_difficulty": "32.45",
      "most_difficult": {
        "chapter": "Advanced Algorithms",
        "level": "Expert",
        "difficulty_score": "89.23"
      }
    }
  }
}
```

### 2. Time vs Score Correlation
**GET** `/difficulty/time-score-correlation`

Analyzes relationship between response time and accuracy.

**Response Example:**
```json
{
  "success": true,
  "data": {
    "correlation_analysis": {
      "correlation_coefficient": "-0.3456",
      "sample_size": 1250,
      "significance": "medium",
      "interpretation": "Mối quan hệ trung bình"
    },
    "scatter_plot_data": [
      {
        "x": 120.5,
        "y": 8.5,
        "user_id": 123,
        "quiz_id": 45,
        "is_outlier": false
      }
    ],
    "outliers": {
      "score_outliers": {
        "outlier_count": 15,
        "outlier_percentage": "1.20"
      }
    }
  }
}
```

---

## 👥 Student Behavior Analytics

### 1. Activity Timeline
**GET** `/behavior/activity-timeline`

Shows learning activity patterns over time.

**Query Parameters:**
- `program_id`, `subject_id`, `user_id` (optional): Filtering options
- `time_period` (optional): Analysis window
- `granularity` (optional): `hourly`, `daily`, `weekly`

**Response Example:**
```json
{
  "success": true,
  "data": {
    "timeline": [
      {
        "time_period": "2024-01-15T00:00:00.000Z",
        "total_attempts": 45,
        "accuracy_rate": "76.67",
        "avg_response_time": "32.45",
        "active_users": 12,
        "activity_intensity": "medium"
      }
    ],
    "patterns": {
      "activity_trend": "increasing",
      "consistency_score": "78.45",
      "peak_periods": [
        {
          "period": "2024-01-20T00:00:00.000Z",
          "attempts": 89
        }
      ]
    }
  }
}
```

### 2. Learning Flow Analysis
**GET** `/behavior/learning-flow`

Analyzes sequence and flow of learning activities.

**Response Example:**
```json
{
  "success": true,
  "data": {
    "flow_patterns": [
      {
        "path": "Chapter 1 -> Chapter 2 -> Chapter 3",
        "count": 45
      }
    ],
    "transition_matrix": [
      {
        "transition": "Basic -> Intermediate",
        "count": 234
      }
    ],
    "summary": {
      "total_users": 150,
      "avg_path_length": "12.34",
      "completion_rate": 78.5
    }
  }
}
```

---

## 📊 Comprehensive Dashboard

### Dashboard Overview
**GET** `/dashboard/overview`

Combines multiple analytics for comprehensive insights.

**Query Parameters:**
- `program_id`, `subject_id` (optional): Filtering options
- `time_period` (optional): Analysis window

---

## 🎯 Student Score Analysis

### 1. Comprehensive Student Score Analysis
**GET** `/student/score-analysis`

Provides detailed analysis of student performance with strengths, weaknesses, and personalized recommendations.

**Query Parameters:**
- `user_id` (required): Student ID to analyze
- `program_id`, `subject_id` (optional): Filtering options
- `time_period` (optional): `1m`, `3m`, `6m`, `1y` (default: `3m`)
- `include_comparison` (optional): Include peer comparison (default: `true`)

**Response Example:**
```json
{
  "success": true,
  "data": {
    "user_id": 123,
    "overall_performance": {
      "total_attempts": 45,
      "correct_attempts": 32,
      "accuracy_rate": "71.11",
      "avg_response_time": "145.50",
      "performance_grade": "B"
    },
    "strengths_weaknesses": {
      "strengths": [
        {
          "type": "learning_outcome",
          "name": "Cấu trúc dữ liệu cơ bản",
          "accuracy": "85.50",
          "reason": "Thành thạo Cấu trúc dữ liệu cơ bản với độ chính xác 85.50%"
        }
      ],
      "weaknesses": [
        {
          "type": "learning_outcome",
          "name": "Thuật toán sắp xếp",
          "accuracy": "45.20",
          "reason": "Cần cải thiện Thuật toán sắp xếp - chỉ đạt 45.20% độ chính xác",
          "priority": "high"
        }
      ]
    },
    "personalized_recommendations": [
      {
        "priority": "high",
        "category": "learning_outcome",
        "title": "Cải thiện Thuật toán sắp xếp",
        "description": "Tập trung ôn tập Thuật toán sắp xếp với độ chính xác hiện tại chỉ 45.20%",
        "actions": [
          "Xem lại lý thuyết cơ bản",
          "Làm thêm bài tập thực hành",
          "Tham khảo tài liệu bổ sung",
          "Thảo luận với giảng viên"
        ],
        "estimated_time": "2-3 tuần"
      }
    ],
    "comparison_with_peers": {
      "user_accuracy": "71.11",
      "peer_average": "68.45",
      "comparison": "slightly_above_average",
      "estimated_percentile": 65,
      "message": "Tốt! Bạn đang có kết quả cao hơn trung bình lớp"
    }
  }
}
```

### 2. Learning Outcome Mastery Analysis
**GET** `/student/learning-outcome-mastery`

Detailed analysis of mastery level for each Learning Outcome.

**Query Parameters:**
- `user_id` (required): Student ID to analyze
- `subject_id`, `program_id` (optional): Filtering options
- `mastery_threshold` (optional): Mastery threshold (default: 0.7)

**Response Example:**
```json
{
  "success": true,
  "data": {
    "user_id": 123,
    "mastery_threshold": 0.7,
    "summary": {
      "total_los": 8,
      "mastered_count": 3,
      "developing_count": 2,
      "needs_improvement_count": 2,
      "not_started_count": 1,
      "overall_mastery_rate": "37.50"
    },
    "learning_outcomes": [
      {
        "lo_id": 1,
        "lo_name": "Cấu trúc dữ liệu cơ bản",
        "chapter": "Chương 1",
        "total_attempts": 12,
        "correct_attempts": 10,
        "accuracy_rate": 0.83,
        "mastery_level": "mastered",
        "improvement_trend": "stable",
        "difficulty_distribution": {
          "Basic": {
            "total": 5,
            "correct": 5,
            "accuracy": 1.0
          },
          "Intermediate": {
            "total": 4,
            "correct": 3,
            "accuracy": 0.75
          }
        }
      }
    ],
    "recommendations": [
      {
        "priority": "high",
        "lo_id": 2,
        "lo_name": "Thuật toán sắp xếp",
        "current_accuracy": "45.20",
        "target_accuracy": "70",
        "recommendation": "Cần tập trung cải thiện Thuật toán sắp xếp",
        "specific_actions": [
          "Ôn tập lý thuyết cơ bản",
          "Làm thêm bài tập thực hành",
          "Tìm hiểu các ví dụ minh họa",
          "Thảo luận với giảng viên về những điểm chưa hiểu"
        ]
      }
    ]
  }
}
```

### 3. Improvement Suggestions
**GET** `/student/improvement-suggestions`

Detailed improvement suggestions with actionable recommendations and study plans.

**Query Parameters:**
- `user_id` (required): Student ID to analyze
- `lo_id` (optional): Specific LO to analyze
- `subject_id`, `program_id` (optional): Filtering options
- `suggestion_depth` (optional): `basic`, `detailed`, `comprehensive` (default: `detailed`)

**Response Example:**
```json
{
  "success": true,
  "data": {
    "user_id": 123,
    "suggestions_count": 3,
    "improvement_suggestions": [
      {
        "lo_id": 2,
        "lo_name": "Thuật toán sắp xếp",
        "current_accuracy": "45.20",
        "priority": "high",
        "main_issues": [
          {
            "issue": "Hiểu biết cơ bản chưa vững",
            "severity": "high",
            "description": "Độ chính xác chỉ 45.2% cho thấy cần ôn tập lại kiến thức nền tảng"
          }
        ],
        "specific_recommendations": [
          {
            "action": "Ôn tập lý thuyết",
            "details": "Đọc lại phần Thuật toán sắp xếp trong giáo trình",
            "time_needed": "2-3 giờ",
            "priority": 1
          },
          {
            "action": "Thực hành có hướng dẫn",
            "details": "Làm bài tập từ dễ đến khó với giải thích chi tiết",
            "time_needed": "3-4 giờ",
            "priority": 2
          }
        ],
        "practice_plan": {
          "week_1": {
            "focus": "Nền tảng lý thuyết",
            "daily_time": "30-45 phút",
            "activities": ["Đọc giáo trình", "Ghi chú khái niệm chính", "Làm bài tập cơ bản"]
          },
          "week_2": {
            "focus": "Thực hành và ứng dụng",
            "daily_time": "45-60 phút",
            "activities": ["Làm bài tập nâng cao", "Thảo luận nhóm", "Tự kiểm tra"]
          }
        },
        "estimated_improvement_time": "3-4 tuần với 45-60 phút/ngày"
      }
    ],
    "study_plan": {
      "phase_1": {
        "title": "Khắc phục khẩn cấp (Tuần 1-2)",
        "focus": ["Thuật toán sắp xếp"],
        "daily_time": "1-2 giờ",
        "priority": "critical"
      },
      "phase_2": {
        "title": "Cải thiện chính (Tuần 3-5)",
        "focus": ["Cấu trúc dữ liệu nâng cao"],
        "daily_time": "45-60 phút",
        "priority": "high"
      },
      "total_duration": "6-8 tuần",
      "success_metrics": [
        "Độ chính xác tăng lên trên 70% cho tất cả LO",
        "Thời gian làm bài giảm xuống dưới 2 phút/câu",
        "Tự tin hơn khi làm bài kiểm tra"
      ]
    }
  }
}
```

---

## 🧪 Testing & Development

### 1. Test Endpoints
**GET** `/test/endpoints`

Test all analytics endpoints for validation and debugging.

### 2. Sample Data
**GET** `/test/sample-data`

Get sample data structure for frontend development.

---

## 📤 Export Functionality

### Export Report
**GET** `/export/report`

Export comprehensive analytics report.

**Query Parameters:**
- `program_id`, `subject_id` (optional): Filtering options
- `time_period` (optional): Analysis window
- `format` (optional): `json`, `csv`, `pdf` (default: `json`)

---

## 🔐 Authorization

- **Admin**: Full access to all analytics
- **Teacher**: Access to data for their subjects/programs
- **Student**: Limited access to personal analytics only

## Error Handling

All endpoints return standardized error responses:

```json
{
  "success": false,
  "message": "Error description",
  "error": "Detailed error message"
}
```

## Rate Limiting

Analytics endpoints are rate-limited to prevent abuse:
- 100 requests per minute for regular endpoints
- 10 requests per minute for export endpoints
