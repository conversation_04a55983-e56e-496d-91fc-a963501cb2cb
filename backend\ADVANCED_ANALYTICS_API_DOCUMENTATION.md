# 📊 Advanced Analytics API Documentation

## Overview
Comprehensive data analysis endpoints for educational insights, providing professional-grade analytics for learning outcomes assessment.

## Base URL
```
/api/advanced-analytics
```

## Authentication
All endpoints require authentication token and appropriate role authorization.

---

## 📈 Performance Analytics

### 1. Time Series Performance Data
**GET** `/performance/time-series`

Analyzes score trends over time with various aggregation options.

**Query Parameters:**
- `program_id` (optional): Filter by program
- `subject_id` (optional): Filter by subject  
- `quiz_id` (optional): Filter by specific quiz
- `user_id` (optional): Filter by specific user
- `time_period` (optional): `7d`, `30d`, `3m`, `6m`, `1y` (default: `7d`)
- `aggregation` (optional): `daily`, `weekly`, `monthly` (default: `daily`)

**Response Example:**
```json
{
  "success": true,
  "data": {
    "time_series": [
      {
        "period": "2024-01-15T00:00:00.000Z",
        "avg_score": "7.25",
        "total_attempts": 45,
        "high_score_rate": "22.22",
        "score_range": 8.5,
        "score_stddev": "1.85"
      }
    ],
    "trend_analysis": {
      "trend_direction": "improving",
      "trend_strength": "0.0234",
      "slope": "0.0234",
      "r_squared": "0.7845",
      "confidence": "high"
    }
  }
}
```

### 2. Score Distribution Analysis
**GET** `/performance/score-distribution`

Provides histogram data and statistical analysis of score distributions.

**Query Parameters:**
- `program_id`, `subject_id`, `quiz_id` (optional): Filtering options
- `bins` (optional): Number of histogram bins (default: 10)
- `comparison_period` (optional): `previous_month`, `previous_quarter`, etc.

**Response Example:**
```json
{
  "success": true,
  "data": {
    "current_period": {
      "histogram": [
        {
          "bin_start": 0,
          "bin_end": 1,
          "count": 5,
          "percentage": "8.33"
        }
      ],
      "statistics": {
        "mean": "6.75",
        "median": "7.00",
        "std_dev": "2.15",
        "skewness": "-0.234",
        "kurtosis": "0.567"
      }
    }
  }
}
```

### 3. Learning Outcomes Comparison
**GET** `/performance/learning-outcomes`

Provides radar chart data for LO performance comparison.

**Query Parameters:**
- `program_id`, `subject_id`, `user_id` (optional): Filtering options
- `comparison_type` (optional): `average`, `top_performer`, `user_vs_average`

### 4. Completion Funnel Analysis
**GET** `/performance/completion-funnel`

Shows conversion rates through different stages of quiz completion.

**Response Example:**
```json
{
  "success": true,
  "data": {
    "funnel_stages": [
      {
        "stage": "Registered Users",
        "count": 150,
        "percentage": 100,
        "conversion_rate": 100
      },
      {
        "stage": "Started Quiz", 
        "count": 120,
        "percentage": "80.00",
        "conversion_rate": "80.00"
      }
    ],
    "summary": {
      "completion_rate": "65.33",
      "pass_rate": "78.57",
      "excellence_rate": "45.23"
    }
  }
}
```

---

## 🔥 Learning Difficulty Analysis

### 1. Difficulty Heatmap
**GET** `/difficulty/heatmap`

Shows question difficulty across chapters and levels.

**Query Parameters:**
- `program_id`, `subject_id` (optional): Filtering options
- `time_period` (optional): Analysis time window

**Response Example:**
```json
{
  "success": true,
  "data": {
    "heatmap_data": [
      {
        "chapter": "Introduction to Programming",
        "level": "Basic",
        "difficulty_score": "25.67",
        "accuracy_rate": "78.45",
        "avg_response_time": "45.23"
      }
    ],
    "axis_labels": {
      "chapters": ["Chapter 1", "Chapter 2"],
      "levels": ["Basic", "Intermediate", "Advanced"]
    },
    "summary": {
      "avg_difficulty": "32.45",
      "most_difficult": {
        "chapter": "Advanced Algorithms",
        "level": "Expert",
        "difficulty_score": "89.23"
      }
    }
  }
}
```

### 2. Time vs Score Correlation
**GET** `/difficulty/time-score-correlation`

Analyzes relationship between response time and accuracy.

**Response Example:**
```json
{
  "success": true,
  "data": {
    "correlation_analysis": {
      "correlation_coefficient": "-0.3456",
      "sample_size": 1250,
      "significance": "medium",
      "interpretation": "Mối quan hệ trung bình"
    },
    "scatter_plot_data": [
      {
        "x": 120.5,
        "y": 8.5,
        "user_id": 123,
        "quiz_id": 45,
        "is_outlier": false
      }
    ],
    "outliers": {
      "score_outliers": {
        "outlier_count": 15,
        "outlier_percentage": "1.20"
      }
    }
  }
}
```

---

## 👥 Student Behavior Analytics

### 1. Activity Timeline
**GET** `/behavior/activity-timeline`

Shows learning activity patterns over time.

**Query Parameters:**
- `program_id`, `subject_id`, `user_id` (optional): Filtering options
- `time_period` (optional): Analysis window
- `granularity` (optional): `hourly`, `daily`, `weekly`

**Response Example:**
```json
{
  "success": true,
  "data": {
    "timeline": [
      {
        "time_period": "2024-01-15T00:00:00.000Z",
        "total_attempts": 45,
        "accuracy_rate": "76.67",
        "avg_response_time": "32.45",
        "active_users": 12,
        "activity_intensity": "medium"
      }
    ],
    "patterns": {
      "activity_trend": "increasing",
      "consistency_score": "78.45",
      "peak_periods": [
        {
          "period": "2024-01-20T00:00:00.000Z",
          "attempts": 89
        }
      ]
    }
  }
}
```

### 2. Learning Flow Analysis
**GET** `/behavior/learning-flow`

Analyzes sequence and flow of learning activities.

**Response Example:**
```json
{
  "success": true,
  "data": {
    "flow_patterns": [
      {
        "path": "Chapter 1 -> Chapter 2 -> Chapter 3",
        "count": 45
      }
    ],
    "transition_matrix": [
      {
        "transition": "Basic -> Intermediate",
        "count": 234
      }
    ],
    "summary": {
      "total_users": 150,
      "avg_path_length": "12.34",
      "completion_rate": 78.5
    }
  }
}
```

---

## 📊 Comprehensive Dashboard

### Dashboard Overview
**GET** `/dashboard/overview`

Combines multiple analytics for comprehensive insights.

**Query Parameters:**
- `program_id`, `subject_id` (optional): Filtering options
- `time_period` (optional): Analysis window

---

## 📤 Export Functionality

### Export Report
**GET** `/export/report`

Export comprehensive analytics report.

**Query Parameters:**
- `program_id`, `subject_id` (optional): Filtering options
- `time_period` (optional): Analysis window
- `format` (optional): `json`, `csv`, `pdf` (default: `json`)

---

## 🔐 Authorization

- **Admin**: Full access to all analytics
- **Teacher**: Access to data for their subjects/programs
- **Student**: Limited access to personal analytics only

## Error Handling

All endpoints return standardized error responses:

```json
{
  "success": false,
  "message": "Error description",
  "error": "Detailed error message"
}
```

## Rate Limiting

Analytics endpoints are rate-limited to prevent abuse:
- 100 requests per minute for regular endpoints
- 10 requests per minute for export endpoints
