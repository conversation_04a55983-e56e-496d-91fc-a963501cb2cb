{"info": {"_postman_id": "advanced-analytics-apis", "name": "Advanced Analytics APIs", "description": "Comprehensive collection for testing Advanced Analytics APIs", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{jwt_token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"exec": ["// Auto-set Authorization header if not already set", "if (!pm.request.headers.has('Authorization')) {", "    pm.request.headers.add({", "        key: 'Authorization',", "        value: 'Bearer ' + pm.environment.get('jwt_token')", "    });", "}"], "type": "text/javascript"}}], "variable": [{"key": "base_url", "value": "http://localhost:3000", "type": "string"}, {"key": "test_user_id", "value": "1", "type": "string"}, {"key": "test_subject_id", "value": "1", "type": "string"}], "item": [{"name": "Authentication", "item": [{"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success && response.token) {", "        pm.environment.set('jwt_token', response.token);", "        console.log('Token saved to environment');", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"admin123\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/login", "host": ["{{base_url}}"], "path": ["api", "auth", "login"]}}}]}, {"name": "Testing & Validation", "item": [{"name": "LO-Chapter Relationship Test", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/advanced-analytics/test/lo-chapter-relationship", "host": ["{{base_url}}"], "path": ["api", "advanced-analytics", "test", "lo-chapter-relationship"]}}}, {"name": "Sample Data", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/advanced-analytics/test/sample-data", "host": ["{{base_url}}"], "path": ["api", "advanced-analytics", "test", "sample-data"]}}}, {"name": "Test Endpoints", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/advanced-analytics/test/endpoints?user_id={{test_user_id}}", "host": ["{{base_url}}"], "path": ["api", "advanced-analytics", "test", "endpoints"], "query": [{"key": "user_id", "value": "{{test_user_id}}"}]}}}]}, {"name": "Performance Analytics", "item": [{"name": "Time Series Performance", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/advanced-analytics/performance/time-series?time_period=30d&aggregation=daily&subject_id={{test_subject_id}}", "host": ["{{base_url}}"], "path": ["api", "advanced-analytics", "performance", "time-series"], "query": [{"key": "time_period", "value": "30d"}, {"key": "aggregation", "value": "daily"}, {"key": "subject_id", "value": "{{test_subject_id}}"}]}}}, {"name": "Score Distribution", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/advanced-analytics/performance/score-distribution?bins=10&subject_id={{test_subject_id}}", "host": ["{{base_url}}"], "path": ["api", "advanced-analytics", "performance", "score-distribution"], "query": [{"key": "bins", "value": "10"}, {"key": "subject_id", "value": "{{test_subject_id}}"}]}}}, {"name": "Learning Outcomes Comparison", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/advanced-analytics/performance/learning-outcomes?comparison_type=average&subject_id={{test_subject_id}}", "host": ["{{base_url}}"], "path": ["api", "advanced-analytics", "performance", "learning-outcomes"], "query": [{"key": "comparison_type", "value": "average"}, {"key": "subject_id", "value": "{{test_subject_id}}"}]}}}, {"name": "Completion Funnel", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/advanced-analytics/performance/completion-funnel?time_period=30d&subject_id={{test_subject_id}}", "host": ["{{base_url}}"], "path": ["api", "advanced-analytics", "performance", "completion-funnel"], "query": [{"key": "time_period", "value": "30d"}, {"key": "subject_id", "value": "{{test_subject_id}}"}]}}}]}, {"name": "Student Score Analysis", "item": [{"name": "Comprehensive Score Analysis", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/advanced-analytics/student/score-analysis?user_id={{test_user_id}}&time_period=3m&include_comparison=true", "host": ["{{base_url}}"], "path": ["api", "advanced-analytics", "student", "score-analysis"], "query": [{"key": "user_id", "value": "{{test_user_id}}"}, {"key": "time_period", "value": "3m"}, {"key": "include_comparison", "value": "true"}]}}}, {"name": "Learning Outcome Mastery", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/advanced-analytics/student/learning-outcome-mastery?user_id={{test_user_id}}&mastery_threshold=0.7", "host": ["{{base_url}}"], "path": ["api", "advanced-analytics", "student", "learning-outcome-mastery"], "query": [{"key": "user_id", "value": "{{test_user_id}}"}, {"key": "mastery_threshold", "value": "0.7"}]}}}, {"name": "Improvement Suggestions", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/advanced-analytics/student/improvement-suggestions?user_id={{test_user_id}}&suggestion_depth=detailed", "host": ["{{base_url}}"], "path": ["api", "advanced-analytics", "student", "improvement-suggestions"], "query": [{"key": "user_id", "value": "{{test_user_id}}"}, {"key": "suggestion_depth", "value": "detailed"}]}}}]}, {"name": "Difficulty Analysis", "item": [{"name": "Difficulty Heatmap", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/advanced-analytics/difficulty/heatmap?time_period=30d&subject_id={{test_subject_id}}", "host": ["{{base_url}}"], "path": ["api", "advanced-analytics", "difficulty", "heatmap"], "query": [{"key": "time_period", "value": "30d"}, {"key": "subject_id", "value": "{{test_subject_id}}"}]}}}, {"name": "Time Score Correlation", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/advanced-analytics/difficulty/time-score-correlation?time_period=30d&subject_id={{test_subject_id}}", "host": ["{{base_url}}"], "path": ["api", "advanced-analytics", "difficulty", "time-score-correlation"], "query": [{"key": "time_period", "value": "30d"}, {"key": "subject_id", "value": "{{test_subject_id}}"}]}}}]}, {"name": "Predictive Analytics", "item": [{"name": "Completion Probability", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/advanced-analytics/predictive/completion-probability?user_id={{test_user_id}}&prediction_horizon=3m", "host": ["{{base_url}}"], "path": ["api", "advanced-analytics", "predictive", "completion-probability"], "query": [{"key": "user_id", "value": "{{test_user_id}}"}, {"key": "prediction_horizon", "value": "3m"}]}}}, {"name": "Risk Assessment", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/advanced-analytics/predictive/risk-assessment?subject_id={{test_subject_id}}&risk_threshold=0.3", "host": ["{{base_url}}"], "path": ["api", "advanced-analytics", "predictive", "risk-assessment"], "query": [{"key": "subject_id", "value": "{{test_subject_id}}"}, {"key": "risk_threshold", "value": "0.3"}]}}}]}, {"name": "Quiz Analytics", "item": [{"name": "Quiz Overall Analytics", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/advanced-analytics/quiz/analytics?quiz_id=1&include_individual_performance=true&include_question_breakdown=true&include_lo_analysis=true", "host": ["{{base_url}}"], "path": ["api", "advanced-analytics", "quiz", "analytics"], "query": [{"key": "quiz_id", "value": "1"}, {"key": "include_individual_performance", "value": "true"}, {"key": "include_question_breakdown", "value": "true"}, {"key": "include_lo_analysis", "value": "true"}]}}}, {"name": "Student Quiz Performance", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/advanced-analytics/quiz/student-performance?user_id={{test_user_id}}&quiz_id=1", "host": ["{{base_url}}"], "path": ["api", "advanced-analytics", "quiz", "student-performance"], "query": [{"key": "user_id", "value": "{{test_user_id}}"}, {"key": "quiz_id", "value": "1"}]}}}]}]}