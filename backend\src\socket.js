// src/socket.js
const socketIO = require('socket.io');

const init = (server) => {
    const io = socketIO(server, {
        path: '/socket.io', // Khớp với location trong Nginx
        cors: {
            origin: [
                'https://stardust.id.vn', // Production
                'https://www.stardust.id.vn', // Hỗ trợ www
                'http://localhost:3000', // Phát triển
                'http://frontend:3000', // Docker
            ],
            methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
            credentials: true,
        },
    });

    io.on('connection', (socket) => {
        console.log('New client connected');

        // Xử lý khi client tham gia vào phòng
        socket.on('joinRoom', (room) => {
            socket.join(room);
            console.log(`Client joined room: ${room}`);
        });

        // Xử lý khi client rời khỏi phòng
        socket.on('leaveRoom', (room) => {
            socket.leave(room);
            console.log(`Client left room: ${room}`);
        });

        // Xử lý khi client ngắt kết nối
        socket.on('disconnect', () => {
            console.log('Client disconnected');
        });
    });

    return io;
};

module.exports = { init };
