const GamificationService = require('../services/gamificationService');
const { User } = require('../models');

class GamificationController {
    
    // Lấy thông tin gamification của user hiện tại
    static async getUserGamificationInfo(req, res) {
        try {
            const userId = req.user.user_id;
            const gamificationInfo = await GamificationService.getUserGamificationInfo(userId);
            
            return res.status(200).json({
                success: true,
                message: 'Lấy thông tin gamification thành công',
                data: gamificationInfo
            });
        } catch (error) {
            console.error('Error getting user gamification info:', error);
            return res.status(500).json({
                success: false,
                message: 'Lỗi khi lấy thông tin gamification',
                error: error.message
            });
        }
    }

    // Lấy bảng xếp hạng theo điểm
    static async getPointsLeaderboard(req, res) {
        try {
            const { limit = 10, timeframe = 'all' } = req.query;
            const leaderboard = await GamificationService.getPointsLeaderboard(
                parseInt(limit), 
                timeframe
            );
            
            return res.status(200).json({
                success: true,
                message: 'Lấy bảng xếp hạng thành công',
                data: {
                    leaderboard,
                    total_users: leaderboard.length,
                    timeframe
                }
            });
        } catch (error) {
            console.error('Error getting points leaderboard:', error);
            return res.status(500).json({
                success: false,
                message: 'Lỗi khi lấy bảng xếp hạng',
                error: error.message
            });
        }
    }

    // Lấy thông tin gamification của user cụ thể (cho admin/teacher)
    static async getUserGamificationInfoById(req, res) {
        try {
            const { userId } = req.params;
            const gamificationInfo = await GamificationService.getUserGamificationInfo(userId);
            
            return res.status(200).json({
                success: true,
                message: 'Lấy thông tin gamification thành công',
                data: gamificationInfo
            });
        } catch (error) {
            console.error('Error getting user gamification info by id:', error);
            return res.status(500).json({
                success: false,
                message: 'Lỗi khi lấy thông tin gamification',
                error: error.message
            });
        }
    }

    // Thêm điểm thủ công (cho admin)
    static async addPointsManually(req, res) {
        try {
            const { userId, points, reason } = req.body;
            
            if (!userId || !points) {
                return res.status(400).json({
                    success: false,
                    message: 'Thiếu thông tin userId hoặc points'
                });
            }

            const user = await User.findByPk(userId);
            if (!user) {
                return res.status(404).json({
                    success: false,
                    message: 'Không tìm thấy người dùng'
                });
            }

            const result = await user.addPoints(points, reason || 'manual_addition');
            
            return res.status(200).json({
                success: true,
                message: 'Thêm điểm thành công',
                data: result
            });
        } catch (error) {
            console.error('Error adding points manually:', error);
            return res.status(500).json({
                success: false,
                message: 'Lỗi khi thêm điểm',
                error: error.message
            });
        }
    }

    // Lấy thống kê tổng quan gamification (cho admin)
    static async getGamificationStats(req, res) {
        try {
            // Thống kê cơ bản
            const totalUsers = await User.count();
            const activeUsers = await User.count({
                where: {
                    total_points: { [require('sequelize').Op.gt]: 0 }
                }
            });

            // Top performers
            const topPerformers = await GamificationService.getPointsLeaderboard(5);
            
            // Level distribution
            const levelDistribution = await User.findAll({
                attributes: [
                    'current_level',
                    [require('sequelize').fn('COUNT', require('sequelize').col('user_id')), 'count']
                ],
                group: ['current_level'],
                order: [['current_level', 'ASC']]
            });

            return res.status(200).json({
                success: true,
                message: 'Lấy thống kê gamification thành công',
                data: {
                    overview: {
                        total_users: totalUsers,
                        active_users: activeUsers,
                        engagement_rate: totalUsers > 0 ? (activeUsers / totalUsers * 100).toFixed(2) : 0
                    },
                    top_performers: topPerformers,
                    level_distribution: levelDistribution
                }
            });
        } catch (error) {
            console.error('Error getting gamification stats:', error);
            return res.status(500).json({
                success: false,
                message: 'Lỗi khi lấy thống kê gamification',
                error: error.message
            });
        }
    }
}

module.exports = GamificationController;
