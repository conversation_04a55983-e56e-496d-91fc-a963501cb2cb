-- Database Optimization for Learning Analytics
-- Tối ưu hóa database cho phân tích dữ liệu học tập

-- =====================================================
-- INDEXES FOR PERFORMANCE OPTIMIZATION
-- =====================================================

-- Indexes cho bảng UserQuestionHistory (quan trọng cho phân tích)
CREATE INDEX IF NOT EXISTS idx_user_question_history_user_quiz 
ON "UserQuestionHistory" (user_id, quiz_id);

CREATE INDEX IF NOT EXISTS idx_user_question_history_attempt_date 
ON "UserQuestionHistory" (attempt_date);

CREATE INDEX IF NOT EXISTS idx_user_question_history_question_lo 
ON "UserQuestionHistory" (question_id) 
INCLUDE (is_correct, time_spent);

-- Indexes cho bảng QuizResults
CREATE INDEX IF NOT EXISTS idx_quiz_results_user_score 
ON "QuizResults" (user_id, score);

CREATE INDEX IF NOT EXISTS idx_quiz_results_quiz_completed 
ON "QuizResults" (quiz_id, completed_at);

-- Indexes cho bảng StudentProgramProgress
CREATE INDEX IF NOT EXISTS idx_student_program_progress_status 
ON "StudentProgramProgress" (student_status, last_updated);

CREATE INDEX IF NOT EXISTS idx_student_program_progress_gpa 
ON "StudentProgramProgress" (program_id) 
WHERE (overall_progress->>'gpa')::float > 0;

-- Indexes cho bảng ProgramOutcomeTracking
CREATE INDEX IF NOT EXISTS idx_program_outcome_tracking_active 
ON "ProgramOutcomeTracking" (program_id, outcome_type, is_active);

CREATE INDEX IF NOT EXISTS idx_program_outcome_tracking_score 
ON "ProgramOutcomeTracking" (user_id, current_score) 
WHERE is_active = true;

-- Indexes cho bảng SubjectOutcomeAnalysis
CREATE INDEX IF NOT EXISTS idx_subject_outcome_analysis_semester 
ON "SubjectOutcomeAnalysis" (program_id, analysis_semester, academic_year);

CREATE INDEX IF NOT EXISTS idx_subject_outcome_analysis_status 
ON "SubjectOutcomeAnalysis" (analysis_status, analysis_date);

-- Indexes cho bảng LearningAnalytics
CREATE INDEX IF NOT EXISTS idx_learning_analytics_type_status 
ON "LearningAnalytics" (analysis_type, analysis_status);

CREATE INDEX IF NOT EXISTS idx_learning_analytics_program_date 
ON "LearningAnalytics" (program_id, data_snapshot_date);

-- =====================================================
-- COMPOSITE INDEXES FOR COMPLEX QUERIES
-- =====================================================

-- Composite index cho phân tích theo thời gian và chương trình
CREATE INDEX IF NOT EXISTS idx_quiz_results_program_time 
ON "QuizResults" (quiz_id, "createdAt") 
INCLUDE (user_id, score);

-- Composite index cho tracking outcomes theo user và program
CREATE INDEX IF NOT EXISTS idx_outcome_tracking_user_program 
ON "ProgramOutcomeTracking" (user_id, program_id, outcome_type) 
WHERE is_active = true;

-- =====================================================
-- PARTIAL INDEXES FOR FILTERED QUERIES
-- =====================================================

-- Partial index cho active students
CREATE INDEX IF NOT EXISTS idx_active_students 
ON "StudentProgramProgress" (program_id, last_updated) 
WHERE student_status = 'active';

-- Partial index cho completed analyses
CREATE INDEX IF NOT EXISTS idx_completed_analyses 
ON "SubjectOutcomeAnalysis" (program_id, analysis_date) 
WHERE analysis_status = 'completed';

-- =====================================================
-- JSON INDEXES FOR ANALYTICS DATA
-- =====================================================

-- GIN indexes cho JSON fields (PostgreSQL specific)
CREATE INDEX IF NOT EXISTS idx_student_progress_po_gin 
ON "StudentProgramProgress" USING GIN (po_progress);

CREATE INDEX IF NOT EXISTS idx_student_progress_plo_gin 
ON "StudentProgramProgress" USING GIN (plo_progress);

CREATE INDEX IF NOT EXISTS idx_outcome_tracking_history_gin 
ON "ProgramOutcomeTracking" USING GIN (score_history);

CREATE INDEX IF NOT EXISTS idx_learning_analytics_overview_gin 
ON "LearningAnalytics" USING GIN (overview_metrics);

-- =====================================================
-- MATERIALIZED VIEWS FOR HEAVY ANALYTICS
-- =====================================================

-- Materialized view cho program overview statistics
CREATE MATERIALIZED VIEW IF NOT EXISTS mv_program_statistics AS
SELECT 
    p.program_id,
    p.name as program_name,
    COUNT(DISTINCT spp.user_id) as total_students,
    COUNT(DISTINCT CASE WHEN spp.student_status = 'active' THEN spp.user_id END) as active_students,
    COUNT(DISTINCT CASE WHEN spp.student_status = 'graduated' THEN spp.user_id END) as graduated_students,
    AVG((spp.overall_progress->>'gpa')::float) as average_gpa,
    AVG((spp.overall_progress->>'completion_percentage')::float) as average_completion,
    NOW() as last_updated
FROM "Programs" p
LEFT JOIN "StudentProgramProgress" spp ON p.program_id = spp.program_id
GROUP BY p.program_id, p.name;

-- Index cho materialized view
CREATE UNIQUE INDEX IF NOT EXISTS idx_mv_program_statistics_program_id 
ON mv_program_statistics (program_id);

-- Materialized view cho subject performance summary
CREATE MATERIALIZED VIEW IF NOT EXISTS mv_subject_performance AS
SELECT 
    s.subject_id,
    s.name as subject_name,
    s.course_id,
    c.program_id,
    COUNT(DISTINCT qr.user_id) as total_students,
    AVG(qr.score) as average_score,
    COUNT(qr.result_id) as total_assessments,
    COUNT(DISTINCT CASE WHEN qr.score >= 70 THEN qr.user_id END) as passing_students,
    NOW() as last_updated
FROM "Subjects" s
JOIN "Courses" c ON s.course_id = c.course_id
LEFT JOIN "Quizzes" q ON s.subject_id = q.subject_id
LEFT JOIN "QuizResults" qr ON q.quiz_id = qr.quiz_id
GROUP BY s.subject_id, s.name, s.course_id, c.program_id;

-- Index cho materialized view
CREATE UNIQUE INDEX IF NOT EXISTS idx_mv_subject_performance_subject_id 
ON mv_subject_performance (subject_id);

-- =====================================================
-- FUNCTIONS FOR REFRESHING MATERIALIZED VIEWS
-- =====================================================

-- Function để refresh materialized views
CREATE OR REPLACE FUNCTION refresh_analytics_views()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY mv_program_statistics;
    REFRESH MATERIALIZED VIEW CONCURRENTLY mv_subject_performance;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- QUERY OPTIMIZATION HINTS
-- =====================================================

-- Tăng work_mem cho các queries phân tích lớn
-- SET work_mem = '256MB';

-- Tăng shared_buffers cho cache
-- SET shared_buffers = '1GB';

-- Enable parallel queries
-- SET max_parallel_workers_per_gather = 4;

-- =====================================================
-- MAINTENANCE COMMANDS
-- =====================================================

-- Analyze tables để update statistics
ANALYZE "UserQuestionHistory";
ANALYZE "QuizResults";
ANALYZE "StudentProgramProgress";
ANALYZE "ProgramOutcomeTracking";
ANALYZE "SubjectOutcomeAnalysis";
ANALYZE "LearningAnalytics";

-- Vacuum để cleanup
VACUUM ANALYZE "UserQuestionHistory";
VACUUM ANALYZE "QuizResults";
VACUUM ANALYZE "StudentProgramProgress";

-- =====================================================
-- MONITORING QUERIES
-- =====================================================

-- Query để check index usage
-- SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch
-- FROM pg_stat_user_indexes
-- WHERE schemaname = 'public'
-- ORDER BY idx_scan DESC;

-- Query để check table sizes
-- SELECT schemaname, tablename, 
--        pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
-- FROM pg_tables 
-- WHERE schemaname = 'public'
-- ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
