"use client";
import React from "react";
import { UserButton } from "./user-button";
import { But<PERSON> } from "@/components/ui/button";
import { PlusCircle, KeyRound, Menu } from "lucide-react";
import { useRouter } from "next/navigation";
import { useAuthStatus } from "@/hooks/use-auth";
import { useSidebarContext } from "@/hooks/use-sidebar";
import JoinQuizModal from "@/components/quiz/join-quiz-modal";
import { TeacherOnly, StudentOnly } from "@/components/auth/role-guard";

// Sidebar header height là h-16 (tương đương 64px)
export const TopNavBar: React.FC<TopNavBarProps> = ({ className = "" }) => {
  const router = useRouter();
  const { isAuthenticated } = useAuthStatus();
  const { isMobile, toggleMobileSidebar, isSidebarVisible } =
    useSidebarContext();

  const handleCreateQuiz = () => {
    router.push("/dashboard/teaching/quizzes/new");
  };

  const handleToggleMenu = () => {
    console.log("Toggle menu clicked. Current state:", isSidebarVisible);
    toggleMobileSidebar();
  };

  return (
    <header
      className={`w-full h-16 min-h-16 max-h-16 bg-background px-6 flex items-center border-b border-border/60 transition-colors duration-200 ${className}`}
      style={{ boxShadow: "none" }}
    >
      {/* Left section - Menu button for mobile */}
      <div className="flex items-center gap-2 flex-shrink-0">
        {isMobile && (
          <Button
            variant="ghost"
            size="icon"
            onClick={handleToggleMenu}
            aria-label="Mở menu"
          >
            <Menu className="w-5 h-5" />
          </Button>
        )}
      </div>

      {/* Center section - Quiz actions */}
      <div className="flex-1 flex items-center justify-center">
        {isAuthenticated() && (
          <>
            <TeacherOnly>
              <Button
                onClick={handleCreateQuiz}
                variant="default"
                size="lg"
                is3DNoLayout={true}
                className="flex items-center gap-1.5 bg-primary text-primary-foreground hover:bg-primary/90 cursor-pointer"
              >
                <PlusCircle className="w-4 h-4" />
                <span>Tạo Quiz</span>
              </Button>
            </TeacherOnly>
            <StudentOnly>
              <JoinQuizModal
                trigger={
                  <Button
                    variant="default"
                    size="lg"
                    is3DNoLayout={true}
                    className="flex items-center gap-1.5 bg-primary text-primary-foreground hover:bg-primary/90 cursor-pointer"
                  >
                    <KeyRound className="w-4 h-4" />
                    <span>Nhập PIN</span>
                  </Button>
                }
              />
            </StudentOnly>
          </>
        )}
      </div>

      {/* Right section - User button */}
      <div className="flex items-center gap-3 flex-shrink-0">
        <UserButton />
      </div>
    </header>
  );
};

interface TopNavBarProps {
  className?: string;
}
