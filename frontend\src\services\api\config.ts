// Cấu hình API và các biến môi trường

export const API_CONFIG = {
  // URL API backend
  API_URL: process.env.NEXT_PUBLIC_API_URL || "http://localhost:8888/api/",

  // Thời gian timeout cho các request (ms)
  TIMEOUT: 30000,

  // Thời gian hết hạn token (ms) - 1 giờ
  TOKEN_EXPIRY: 60 * 60 * 1000,
};

// Các đường dẫn trong ứng dụng
export const APP_ROUTES = {
  // Trang công khai
  PUBLIC: {
    LOGIN: "/login",
    REGISTER: "/register",
    FORGOT_PASSWORD: "/forgot-password",
    HOME: "/",
  },

  // Trang dành cho sinh viên
  STUDENT: {
    DASHBOARD: "/dashboard",
    COURSES: "/courses",
    PROFILE: "/profile",
    QUIZZES: "/quizzes",
  },

  // Trang dành cho giáo viên
  TEACHER: {
    DASHBOARD: "/teacher/dashboard",
    COURSES: "/teacher/courses",
    STUDENTS: "/teacher/students",
    QUIZZES: "/teacher/quizzes",
    PROFILE: "/teacher/profile",
  },

  // Trang dành cho admin
  ADMIN: {
    DASHBOARD: "/admin/dashboard",
    USERS: "/admin/users",
    COURSES: "/admin/courses",
    SETTINGS: "/admin/settings",
  },
};

// Các thông báo lỗi chung
export const ERROR_MESSAGES = {
  NETWORK_ERROR: "Đã xảy ra lỗi khi kết nối đến máy chủ.",
  UNAUTHORIZED: "Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.",
  FORBIDDEN: "Bạn không có quyền truy cập vào tài nguyên này.",
  NOT_FOUND: "Không tìm thấy tài nguyên yêu cầu.",
  SERVER_ERROR: "Đã xảy ra lỗi từ phía máy chủ.",
  BAD_REQUEST: "Yêu cầu không hợp lệ. Vui lòng kiểm tra lại thông tin.",
};

const config = {
  API_CONFIG,
  APP_ROUTES,
  ERROR_MESSAGES,
};

export default config;
