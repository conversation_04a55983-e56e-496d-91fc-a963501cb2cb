// Test script để kiểm tra API bảng xếp hạng
const axios = require('axios');

const BASE_URL = 'http://localhost:8888/api';

async function testLeaderboardAPI() {
    try {
        console.log('Testing Leaderboard API...');
        
        // Test 1: <PERSON><PERSON><PERSON> bảng xếp hạng cho quiz ID = 1
        console.log('\n1. Testing GET /quizzes/1/leaderboard');
        try {
            const response = await axios.get(`${BASE_URL}/quizzes/1/leaderboard`);
            console.log('✅ Success:', response.data);
        } catch (error) {
            console.log('❌ Error:', error.response?.data || error.message);
        }

        // Test 2: <PERSON><PERSON><PERSON> bảng xếp hạng cho quiz ID = 2
        console.log('\n2. Testing GET /quizzes/2/leaderboard');
        try {
            const response = await axios.get(`${BASE_URL}/quizzes/2/leaderboard`);
            console.log('✅ Success:', response.data);
        } catch (error) {
            console.log('❌ Error:', error.response?.data || error.message);
        }

        // Test 3: <PERSON><PERSON><PERSON> da<PERSON> sách quiz để xem có quiz nào
        console.log('\n3. Testing GET /quizzes');
        try {
            const response = await axios.get(`${BASE_URL}/quizzes`);
            console.log('✅ Available quizzes:', response.data.quizzes?.map(q => ({
                id: q.quiz_id,
                name: q.name,
                status: q.status
            })));
        } catch (error) {
            console.log('❌ Error:', error.response?.data || error.message);
        }

    } catch (error) {
        console.error('Test failed:', error.message);
    }
}

// Chạy test
testLeaderboardAPI();
