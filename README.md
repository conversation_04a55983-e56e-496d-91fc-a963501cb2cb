# QL_CTDT - Quản lý Chương trình Đào tạo

Hệ thống quản lý chương trình đào tạo được xây dựng dưới dạng monorepo với pnpm workspace.

## Cấu trúc dự án

```
QL_CTDT_FRONT/
├── frontend/          # Next.js frontend application
├── backend/           # Node.js/Express backend API
├── package.json       # Root package.json cho workspace
├── pnpm-workspace.yaml # Cấu hình pnpm workspace
└── .npmrc            # Cấu hình pnpm
```

## Yêu cầu hệ thống

- Node.js >= 18.0.0
- pnpm >= 8.0.0

## Cài đặt

### 1. Cài đặt pnpm (nếu chưa có)

```bash
npm install -g pnpm
```

### 2. Cài đặt dependencies cho toàn bộ workspace

```bash
pnpm install
```

## C<PERSON><PERSON> lệnh chính

### Development

```bash
# Chạy cả frontend và backend cùng lúc
pnpm dev

# Chỉ chạy frontend
pnpm dev:frontend

# Chỉ chạy backend
pnpm dev:backend
```

### Build

```bash
# Build cả frontend và backend
pnpm build

# Build riêng từng phần
pnpm build:frontend
pnpm build:backend
```

### Production

```bash
# Chạy production mode
pnpm start

# Chạy riêng từng phần
pnpm start:frontend
pnpm start:backend
```

### Utilities

```bash
# Lint code
pnpm lint

# Clean node_modules và build files
pnpm clean

# Chạy tests
pnpm test
```

## Làm việc với workspace

### Cài đặt package cho workspace cụ thể

```bash
# Cài package cho frontend
pnpm --filter frontend add <package-name>

# Cài package cho backend
pnpm --filter backend add <package-name>

# Cài dev dependency
pnpm --filter frontend add -D <package-name>
```

### Chạy script cho workspace cụ thể

```bash
# Chạy script trong frontend
pnpm --filter frontend <script-name>

# Chạy script trong backend
pnpm --filter backend <script-name>
```

## Ports

- Frontend: http://localhost:3000
- Backend: http://localhost:5000 (hoặc theo cấu hình trong backend)

## Đóng góp

1. Clone repository
2. Cài đặt dependencies: `pnpm install`
3. Tạo branch mới cho feature
4. Commit và push changes
5. Tạo Pull Request
