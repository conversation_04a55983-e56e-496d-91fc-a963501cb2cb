const { Subject, Course, TypeSubject, TypeOfKnowledge, PLO, LO, Quiz, TienQuyet, Chapter } = require('../models');

exports.getAllSubjects = async (req, res) => {
    try {
        const { page = 1, limit = 10 } = req.query;
        const offset = (page - 1) * limit;

        const subjects = await Subject.findAndCountAll({
            limit: parseInt(limit),
            offset: parseInt(offset),
            include: [
                { model: Course, attributes: ['course_id', 'name'] },
                { model: TypeSubject, attributes: ['type_id', 'description'] },
                { model: TypeOfKnowledge, attributes: ['noidung_id', 'description'] },
                { model: PLO, attributes: ['plo_id', 'description'] },
                { model: Quiz, attributes: ['quiz_id', 'name'] },
                { model: Subject, as: 'PrerequisiteSubjects', attributes: ['subject_id', 'name'] },
                {
                    model: Chapter,
                    as: 'Chapters',
                    attributes: ['chapter_id', 'name', 'description'],
                    include: [
                        {
                            model: LO,
                            as: 'LOs',
                            attributes: ['lo_id', 'name', 'description']
                        }
                    ]
                },
            ],
        });

        res.status(200).json({
            totalItems: subjects.count,
            totalPages: Math.ceil(subjects.count / limit),
            currentPage: parseInt(page),
            subjects: subjects.rows,
        });
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi lấy danh sách Subject', error: error.message });
    }
};

exports.getSubjectById = async (req, res) => {
    try {
        const subject = await Subject.findByPk(req.params.id, {
            include: [
                { model: Course, attributes: ['course_id', 'name'] },
                { model: TypeSubject, attributes: ['type_id', 'description'] },
                { model: TypeOfKnowledge, attributes: ['noidung_id', 'description'] },
                { model: PLO, attributes: ['plo_id', 'description'] },
                { model: LO, attributes: ['lo_id', 'name'] },
                { model: Quiz, attributes: ['quiz_id', 'name'] },
                { model: TienQuyet, as: 'PrerequisiteSubjects', attributes: ['prerequisite_subject_id'] },
            ],
        });

        if (!subject) return res.status(404).json({ message: 'Subject không tồn tại' });
        res.status(200).json(subject);
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi lấy thông tin Subject', error: error.message });
    }
};

exports.createSubject = async (req, res) => {
    try {
        const { course_id, type_id, noidung_id, name, description, created_at, plo_id } = req.body;

        if (!course_id || !type_id || !noidung_id || !name || !plo_id) {
            return res.status(400).json({ message: 'Thiếu các trường bắt buộc' });
        }

        const course = await Course.findByPk(course_id);
        const typeSubject = await TypeSubject.findByPk(type_id);
        const typeOfKnowledge = await TypeOfKnowledge.findByPk(noidung_id);
        const plo = await PLO.findByPk(plo_id);

        if (!course) return res.status(400).json({ message: 'Khóa học không tồn tại' });
        if (!typeSubject) return res.status(400).json({ message: 'TypeSubject không tồn tại' });
        if (!typeOfKnowledge) return res.status(400).json({ message: 'TypeOfKnowledge không tồn tại' });
        if (!plo) return res.status(400).json({ message: 'PLO không tồn tại' });

        const newSubject = await Subject.create({
            course_id,
            type_id,
            noidung_id,
            name,
            description,
            created_at,
            plo_id,
        });

        res.status(201).json(newSubject);
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi tạo Subject', error: error.message });
    }
};

exports.updateSubject = async (req, res) => {
    try {
        const { course_id, type_id, noidung_id, name, description, created_at, plo_id } = req.body;

        const subject = await Subject.findByPk(req.params.id);
        if (!subject) return res.status(404).json({ message: 'Subject không tồn tại' });

        if (course_id) {
            const course = await Course.findByPk(course_id);
            if (!course) return res.status(400).json({ message: 'Khóa học không tồn tại' });
        }
        if (type_id) {
            const typeSubject = await TypeSubject.findByPk(type_id);
            if (!typeSubject) return res.status(400).json({ message: 'TypeSubject không tồn tại' });
        }
        if (noidung_id) {
            const typeOfKnowledge = await TypeOfKnowledge.findByPk(noidung_id);
            if (!typeOfKnowledge) return res.status(400).json({ message: 'TypeOfKnowledge không tồn tại' });
        }
        if (plo_id) {
            const plo = await PLO.findByPk(plo_id);
            if (!plo) return res.status(400).json({ message: 'PLO không tồn tại' });
        }

        await subject.update({
            course_id: course_id || subject.course_id,
            type_id: type_id || subject.type_id,
            noidung_id: noidung_id || subject.noidung_id,
            name: name || subject.name,
            description: description || subject.description,
            created_at: created_at || subject.created_at,
            plo_id: plo_id || subject.plo_id,
        });

        res.status(200).json(subject);
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi cập nhật Subject', error: error.message });
    }
};

exports.deleteSubject = async (req, res) => {
    try {
        const subject = await Subject.findByPk(req.params.id);
        if (!subject) return res.status(404).json({ message: 'Subject không tồn tại' });

        await subject.destroy();
        res.status(200).json({ message: 'Xóa Subject thành công' });
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi xóa Subject', error: error.message });
    }
};