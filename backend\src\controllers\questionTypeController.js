const { QuestionType, Question } = require('../models');

exports.getAllQuestionTypes = async (req, res) => {
    try {
        const { page = 1, limit = 10 } = req.query;
        const offset = (page - 1) * limit;

        const questionTypes = await QuestionType.findAndCountAll({
            limit: parseInt(limit),
            offset: parseInt(offset),
            include: [{ model: Question, attributes: ['question_id', 'question_text'] }],
        });

        res.status(200).json({
            totalItems: questionTypes.count,
            totalPages: Math.ceil(questionTypes.count / limit),
            currentPage: parseInt(page),
            questionTypes: questionTypes.rows,
        });
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi lấy danh sách QuestionType', error: error.message });
    }
};

exports.getQuestionTypeById = async (req, res) => {
    try {
        const questionType = await QuestionType.findByPk(req.params.id, {
            include: [{ model: Question, attributes: ['question_id', 'question_text'] }],
        });

        if (!questionType) return res.status(404).json({ message: 'QuestionType không tồn tại' });
        res.status(200).json(questionType);
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi lấy thông tin QuestionType', error: error.message });
    }
};

exports.createQuestionType = async (req, res) => {
    try {
        const { name } = req.body;

        if (!name) {
            return res.status(400).json({ message: 'Tên QuestionType là bắt buộc' });
        }

        const newQuestionType = await QuestionType.create({ name });
        res.status(201).json(newQuestionType);
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi tạo QuestionType', error: error.message });
    }
};

exports.updateQuestionType = async (req, res) => {
    try {
        const { name } = req.body;

        const questionType = await QuestionType.findByPk(req.params.id);
        if (!questionType) return res.status(404).json({ message: 'QuestionType không tồn tại' });

        await questionType.update({ name: name || questionType.name });
        res.status(200).json(questionType);
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi cập nhật QuestionType', error: error.message });
    }
};

exports.deleteQuestionType = async (req, res) => {
    try {
        const questionType = await QuestionType.findByPk(req.params.id);
        if (!questionType) return res.status(404).json({ message: 'QuestionType không tồn tại' });

        await questionType.destroy();
        res.status(200).json({ message: 'Xóa QuestionType thành công' });
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi xóa QuestionType', error: error.message });
    }
};