const { Program, PO, PLO, Course } = require('../models');

// L<PERSON>y danh sách tất cả chương trình (có phân trang)
exports.getAllPrograms = async (req, res) => {
    try {
        const { page = 1, limit = 10 } = req.query;
        const offset = (page - 1) * limit;

        const programs = await Program.findAndCountAll({
            limit: parseInt(limit),
            offset: parseInt(offset),
            include: [
                { model: PO, attributes: ['po_id', 'name'] },
                { model: PLO, attributes: ['plo_id', 'description'] },
                { model: Course, attributes: ['course_id', 'name'] },
            ],
        });

        res.status(200).json({
            totalItems: programs.count,
            totalPages: Math.ceil(programs.count / limit),
            currentPage: parseInt(page),
            programs: programs.rows,
        });
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi lấy danh sách chương trình', error: error.message });
    }
};

// L<PERSON>y thông tin chi tiết một chương trình
exports.getProgramById = async (req, res) => {
    try {
        const program = await Program.findByPk(req.params.id, {
            include: [
                { model: PO, attributes: ['po_id', 'name'] },
                { model: PLO, attributes: ['plo_id', 'description'] },
                { model: Course, attributes: ['course_id', 'name'] },
            ],
        });

        if (!program) {
            return res.status(404).json({ message: 'Chương trình không tồn tại' });
        }

        res.status(200).json(program);
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi lấy thông tin chương trình', error: error.message });
    }
};

// Tạo một chương trình mới
exports.createProgram = async (req, res) => {
    try {
        const { name, description } = req.body;

        if (!name) {
            return res.status(400).json({ message: 'Tên chương trình là bắt buộc' });
        }

        const newProgram = await Program.create({
            name,
            description,
        });

        res.status(201).json(newProgram);
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi tạo chương trình', error: error.message });
    }
};

// Cập nhật thông tin một chương trình
exports.updateProgram = async (req, res) => {
    try {
        const { name, description } = req.body;

        const program = await Program.findByPk(req.params.id);
        if (!program) {
            return res.status(404).json({ message: 'Chương trình không tồn tại' });
        }

        await program.update({
            name: name || program.name,
            description: description || program.description,
        });

        res.status(200).json(program);
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi cập nhật chương trình', error: error.message });
    }
};

// Xóa một chương trình
exports.deleteProgram = async (req, res) => {
    try {
        const program = await Program.findByPk(req.params.id);
        if (!program) {
            return res.status(404).json({ message: 'Chương trình không tồn tại' });
        }

        await program.destroy();
        res.status(200).json({ message: 'Xóa chương trình thành công' });
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi xóa chương trình', error: error.message });
    }
};