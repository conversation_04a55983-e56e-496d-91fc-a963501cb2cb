# API Tracking Tiến Trình Quiz Realtime

## Tổng quan

API này cung cấp dữ liệu tracking tiến trình làm việc realtime của các người tham gia quiz, đ<PERSON><PERSON><PERSON> thiết kế đặc biệt để hiển thị bằng biểu đồ đường (line chart) cho giáo viên theo dõi.

## Endpoint

```
GET /api/quizzes/:quizId/progress-tracking
```

## Quyền truy cập
- **Admin**: <PERSON><PERSON> thể xem tracking cho tất cả quiz
- **Teacher**: <PERSON><PERSON> thể xem tracking cho quiz của mình

## Parameters

### Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `quizId` | Integer | Yes | ID của quiz cần tracking |

### Query Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `interval` | String | No | `30s` | Khoảng thời gian cho mỗi data point (30s, 1m, 5m) |
| `user_id` | Integer | No | null | ID của user cụ thể (nếu muốn tracking 1 user) |

## Response Format

### Tracking tất cả participants (không có user_id)

```json
{
  "success": true,
  "quiz_id": 1,
  "quiz_name": "Quiz Chương 1",
  "total_questions": 20,
  "interval": "30s",
  "data": {
    "participants_progress": [
      {
        "user_id": 123,
        "user_name": "Nguyễn Văn A",
        "progress_data": [
          {
            "timestamp": "2024-01-15T10:00:00.000Z",
            "score": 0,
            "correct_answers": 0,
            "total_answers": 0,
            "accuracy": 0,
            "answers_in_interval": 0
          },
          {
            "timestamp": "2024-01-15T10:00:30.000Z",
            "score": 20,
            "correct_answers": 2,
            "total_answers": 3,
            "accuracy": 67,
            "answers_in_interval": 3
          },
          {
            "timestamp": "2024-01-15T10:01:00.000Z",
            "score": 40,
            "correct_answers": 4,
            "total_answers": 6,
            "accuracy": 67,
            "answers_in_interval": 3
          }
        ]
      },
      {
        "user_id": 124,
        "user_name": "Trần Thị B",
        "progress_data": [
          {
            "timestamp": "2024-01-15T10:00:00.000Z",
            "score": 0,
            "correct_answers": 0,
            "total_answers": 0,
            "accuracy": 0,
            "answers_in_interval": 0
          },
          {
            "timestamp": "2024-01-15T10:00:30.000Z",
            "score": 30,
            "correct_answers": 3,
            "total_answers": 4,
            "accuracy": 75,
            "answers_in_interval": 4
          }
        ]
      }
    ],
    "overall_progress": [
      {
        "timestamp": "2024-01-15T10:00:00.000Z",
        "total_participants": 0,
        "average_score": 0,
        "total_answers": 0,
        "total_correct": 0,
        "overall_accuracy": 0,
        "answers_in_interval": 0,
        "active_participants": 0
      },
      {
        "timestamp": "2024-01-15T10:00:30.000Z",
        "total_participants": 2,
        "average_score": 25,
        "total_answers": 7,
        "total_correct": 5,
        "overall_accuracy": 71,
        "answers_in_interval": 7,
        "active_participants": 2
      }
    ],
    "summary": {
      "total_participants": 2,
      "total_answers": 15,
      "interval_used": "30s",
      "time_range": {
        "start": "2024-01-15T10:00:00.000Z",
        "end": "2024-01-15T10:05:00.000Z"
      }
    }
  },
  "generated_at": "2024-01-15T10:30:00.000Z"
}
```

### Tracking user cụ thể (có user_id)

```json
{
  "success": true,
  "quiz_id": 1,
  "quiz_name": "Quiz Chương 1",
  "total_questions": 20,
  "interval": "30s",
  "data": {
    "user_progress": [
      {
        "timestamp": "2024-01-15T10:00:00.000Z",
        "score": 0,
        "correct_answers": 0,
        "total_answers": 0,
        "accuracy": 0,
        "answers_in_interval": 0,
        "questions_answered": []
      },
      {
        "timestamp": "2024-01-15T10:00:30.000Z",
        "score": 20,
        "correct_answers": 2,
        "total_answers": 3,
        "accuracy": 67,
        "answers_in_interval": 3,
        "questions_answered": [
          {
            "question_id": 1,
            "is_correct": true,
            "time_spent": 15000,
            "level": "Easy",
            "lo": "LO1.1"
          },
          {
            "question_id": 2,
            "is_correct": false,
            "time_spent": 20000,
            "level": "Medium",
            "lo": "LO1.2"
          },
          {
            "question_id": 3,
            "is_correct": true,
            "time_spent": 12000,
            "level": "Easy",
            "lo": "LO1.1"
          }
        ]
      }
    ],
    "summary": {
      "total_answers": 10,
      "interval_used": "30s",
      "time_range": {
        "start": "2024-01-15T10:00:00.000Z",
        "end": "2024-01-15T10:03:00.000Z"
      }
    }
  },
  "generated_at": "2024-01-15T10:30:00.000Z"
}
```

## WebSocket Events

### Event: progressTrackingUpdate

Được gửi realtime cho giáo viên khi có cập nhật tiến trình:

```javascript
socket.on('progressTrackingUpdate', (data) => {
  console.log('Progress update:', data);
  // data structure:
  {
    quiz_id: 1,
    timestamp: 1642248600000,
    progress_data: {
      participants_summary: [
        {
          user_id: 123,
          user_name: "Nguyễn Văn A",
          current_score: 40,
          progress_percentage: 60,
          total_answers: 12,
          correct_answers: 8,
          status: "in_progress",
          is_active: true,
          last_activity: 1642248590000
        }
      ],
      overall_metrics: {
        total_participants: 25,
        active_participants: 20,
        average_progress: 45,
        average_score: 35
      }
    }
  }
});
```

## Ví dụ sử dụng

### 1. Lấy tracking cho tất cả participants với interval 1 phút

```bash
GET /api/quizzes/1/progress-tracking?interval=1m
```

### 2. Lấy tracking cho user cụ thể với interval 30 giây

```bash
GET /api/quizzes/1/progress-tracking?user_id=123&interval=30s
```

### 3. Sử dụng với Chart.js cho biểu đồ đường

```javascript
// Lấy dữ liệu từ API
const response = await fetch('/api/quizzes/1/progress-tracking?interval=1m');
const data = await response.json();

// Chuẩn bị dữ liệu cho Chart.js
const chartData = {
  labels: data.data.overall_progress.map(point => 
    new Date(point.timestamp).toLocaleTimeString()
  ),
  datasets: [
    {
      label: 'Điểm trung bình',
      data: data.data.overall_progress.map(point => point.average_score),
      borderColor: 'rgb(75, 192, 192)',
      backgroundColor: 'rgba(75, 192, 192, 0.2)',
      tension: 0.1
    },
    {
      label: 'Độ chính xác tổng thể (%)',
      data: data.data.overall_progress.map(point => point.overall_accuracy),
      borderColor: 'rgb(255, 99, 132)',
      backgroundColor: 'rgba(255, 99, 132, 0.2)',
      tension: 0.1
    }
  ]
};

// Tạo biểu đồ
const ctx = document.getElementById('progressChart').getContext('2d');
new Chart(ctx, {
  type: 'line',
  data: chartData,
  options: {
    responsive: true,
    scales: {
      y: {
        beginAtZero: true
      }
    }
  }
});
```

## Lưu ý

1. **Interval**: Hỗ trợ các format: `30s`, `1m`, `5m`, `10m`, `1h`
2. **Real-time**: Dữ liệu được cập nhật realtime qua WebSocket events
3. **Performance**: API tối ưu cho việc hiển thị biểu đồ với lượng data points hợp lý
4. **Caching**: Dữ liệu được cache để tăng performance
5. **Time Range**: API tự động tính toán time range dựa trên dữ liệu có sẵn
