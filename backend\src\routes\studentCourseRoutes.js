const express = require('express');
const router = express.Router();
const studentCourseController = require('../controllers/studentCourseController');

router.get('/', studentCourseController.getAllStudentCourses);
router.get('/:user_id/:course_id', studentCourseController.getStudentCourseById);
router.post('/', studentCourseController.createStudentCourse);
router.delete('/:user_id/:course_id', studentCourseController.deleteStudentCourse);

module.exports = router;