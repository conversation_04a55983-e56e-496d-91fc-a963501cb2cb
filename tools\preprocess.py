import pandas as pd

def parse_question_rows(raw_rows):
    rows = []
    lo_id = ""
    i = 0
    while i < len(raw_rows):
        row = raw_rows[i]
        # Check for KQHTx to update lo_id and skip header
        if row and isinstance(row[0], str) and row[0].strip().startswith("KQHT"):
            kqht_text = row[0].strip().replace("KQHT", "").strip()
            number = "".join(filter(str.isdigit, kqht_text))
            lo_id = f"{number}"
            i += 2  # Skip header row
            continue
        if i + 3 >= len(raw_rows):
            break
        q_row = raw_rows[i]
        a_row = raw_rows[i]
        b_row = raw_rows[i + 1]
        c_row = raw_rows[i + 2]
        d_row = raw_rows[i + 3]
        question_type_id = 1
        level_id = int(q_row[0]) if q_row[0] and str(q_row[0]).isdigit() else 1
        question_text = str(q_row[2]).strip() if len(q_row) > 2 else ""
        answer_a = str(a_row[3]).replace("A.", "").strip() if len(a_row) > 3 else ""
        answer_b = str(b_row[3]).replace("B.", "").strip() if len(b_row) > 3 else ""
        answer_c = str(c_row[3]).replace("C.", "").strip() if len(c_row) > 3 else ""
        answer_d = str(d_row[3]).replace("D.", "").strip() if len(d_row) > 3 else ""
        correct = str(a_row[4]).strip().upper() if len(a_row) > 4 else ""
        iscorrect = [
            1 if correct == "A" else 0,
            1 if correct == "B" else 0,
            1 if correct == "C" else 0,
            1 if correct == "D" else 0,
        ]
        explanation = str(a_row[5]).strip() if len(a_row) > 5 else ""
        rows.append([
            question_type_id, level_id, question_text, lo_id,
            answer_a, iscorrect[0], answer_b, iscorrect[1],
            answer_c, iscorrect[2], answer_d, iscorrect[3], explanation
        ])
        i += 4
    columns = [
        "question_type_id", "level_id", "question_text", "lo_id",
        "answer_1", "iscorrect_1", "answer_2", "iscorrect_2",
        "answer_3", "iscorrect_3", "answer_4", "iscorrect_4", "explanation"
    ]
    return pd.DataFrame(rows, columns=columns)

# --- Đọc từng loại file và chuyển thành raw_rows ---

def read_excel(path):
    import pandas as pd
    df = pd.read_excel(path, header=None)
    return df.values.tolist()

def read_csv(path):
    import pandas as pd
    df = pd.read_csv(path, header=None)
    return df.values.tolist()

def read_docx(path):
    from docx import Document
    doc = Document(path)
    # Giả sử mỗi dòng là 1 câu, tách bằng tab hoặc dấu phân cách khác
    lines = [p.text.strip().split('\t') for p in doc.paragraphs if p.text.strip()]
    return lines

# --- Sử dụng ---
# raw_rows = read_excel("tkw_questions.xlsx")
# raw_rows = read_csv("questions_output.csv")
# raw_rows = read_docx("tkw_questions.docx")
# df = parse_question_rows(raw_rows)
# df.to_csv("questions_output.csv", index=False, encoding="utf-8-sig")
