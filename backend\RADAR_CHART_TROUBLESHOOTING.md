# Khắc phục vấn đề Radar Chart API

## 🔍 Vấn đề đã gặp

### 1. Validation Error
```
Error saving UserQuestionHistory for user 120, question 7: Validation error
```

**<PERSON><PERSON><PERSON><PERSON> nhân:** <PERSON><PERSON> gắng lưu field `attempt_number` không tồn tại trong model `UserQuestionHistory`

**Gi<PERSON>i pháp:** Đã sửa trong `quizRealtimeService.js` - bỏ field `attempt_number`

### 2. Tất cả chỉ số đều là 0
**Nguy<PERSON>n nhân có thể:**
- Dữ liệu `UserQuestionHistory` không có hoặc không khớp với `quiz_id`
- <PERSON><PERSON> giữa các bảng không đúng
- Dữ liệu `Level` hoặc `LO` bị null

## 🛠️ Các bước khắc phục

### Bước 1: <PERSON><PERSON><PERSON> tra dữ liệu cơ bản

```sql
-- <PERSON><PERSON><PERSON> tra quiz có tồn tại không
SELECT * FROM "Quizzes" WHERE quiz_id = 1;

-- <PERSON><PERSON><PERSON> tra questions của quiz
SELECT q.question_id, q.question_text, l.name as level_name, lo.name as lo_name
FROM "Questions" q
JOIN "QuizQuestions" qq ON q.question_id = qq.question_id
JOIN "Levels" l ON q.level_id = l.level_id
JOIN "LOs" lo ON q.lo_id = lo.lo_id
WHERE qq.quiz_id = 1;

-- Kiểm tra UserQuestionHistory
SELECT * FROM "UserQuestionHistories" WHERE quiz_id = 1;
```

### Bước 2: Kiểm tra join relationships

```sql
-- Kiểm tra join UserQuestionHistory với Question, Level, LO
SELECT 
    uqh.history_id,
    uqh.user_id,
    uqh.question_id,
    uqh.is_correct,
    uqh.time_spent,
    q.question_text,
    l.name as level_name,
    lo.name as lo_name
FROM "UserQuestionHistories" uqh
JOIN "Questions" q ON uqh.question_id = q.question_id
JOIN "Levels" l ON q.level_id = l.level_id
JOIN "LOs" lo ON q.lo_id = lo.lo_id
WHERE uqh.quiz_id = 1;
```

### Bước 3: Debug với API

Sử dụng script `test_simple_radar.js` để test:

```bash
node test_simple_radar.js
```

### Bước 4: Kiểm tra logs

Xem logs của server để tìm thông tin debug:

```
🔍 Getting radar data for user 120, quiz 1
✅ Quiz found: Quiz Test with 10 questions
✅ Found 5 question history records for user 120
🔧 Calculating radar data for 5 history records
📋 Initialized analysis for 3 difficulty levels and 4 LOs
📊 Processed 5 valid question records (3 correct)
✅ Radar data calculation completed
```

## 🔧 Các sửa đổi đã thực hiện

### 1. Sửa validation error
```javascript
// Trong quizRealtimeService.js
await UserQuestionHistory.create({
    user_id: userId,
    question_id: questionId,
    quiz_id: quizId,
    selected_answer: answerId,
    is_correct: isCorrect,
    time_spent: responseTime,
    attempt_date: new Date(),
    difficulty_level: null
    // Bỏ attempt_number
});
```

### 2. Thêm debug logs
```javascript
// Trong quizResultController.js
console.log(`🔍 Getting radar data for user ${userId}, quiz ${quizId}`);
console.log(`✅ Found ${questionHistory.length} question history records for user ${userId}`);
```

### 3. Xử lý trường hợp không có dữ liệu
```javascript
if (questionHistory.length === 0) {
    // Trả về dữ liệu mặc định với 0
    return res.status(200).json({
        user_id: userId,
        quiz_id: quizId,
        radar_data: defaultRadarData,
        message: 'Chưa có dữ liệu trả lời câu hỏi'
    });
}
```

### 4. Kiểm tra dữ liệu null
```javascript
if (!question.Level || !question.LO) {
    console.log(`⚠️ History record ${index} has incomplete Question data`);
    return; // Skip
}
```

## 📊 Cấu trúc dữ liệu mong đợi

### Response thành công:
```json
{
  "quiz_id": 1,
  "quiz_name": "Quiz Test",
  "total_questions": 10,
  "radar_data": {
    "current_user": {
      "user_id": 120,
      "data": {
        "difficulty_levels": {
          "easy": {
            "accuracy": 80,
            "questions_count": 3,
            "average_response_time": 15000
          },
          "medium": {
            "accuracy": 60,
            "questions_count": 4,
            "average_response_time": 20000
          },
          "hard": {
            "accuracy": 40,
            "questions_count": 3,
            "average_response_time": 25000
          }
        },
        "learning_outcomes": {
          "LO1": {
            "accuracy": 75,
            "questions_count": 4,
            "average_response_time": 18000
          }
        },
        "performance_metrics": {
          "average_response_time": 20000,
          "completion_rate": 100,
          "first_attempt_accuracy": 70,
          "overall_accuracy": 60
        }
      }
    }
  },
  "summary": {
    "total_participants": 5,
    "total_answers": 50,
    "difficulty_levels": ["easy", "medium", "hard"],
    "learning_outcomes": ["LO1", "LO2", "LO3"]
  }
}
```

## 🚨 Các lỗi thường gặp

### 1. "Không có dữ liệu trả lời câu hỏi"
**Nguyên nhân:** `UserQuestionHistory` trống cho quiz/user này
**Giải pháp:** Kiểm tra xem user đã tham gia quiz chưa

### 2. "Tất cả accuracy = 0"
**Nguyên nhân:** Dữ liệu join không đúng hoặc `is_correct` = false
**Giải pháp:** Kiểm tra dữ liệu trong database

### 3. "Validation error"
**Nguyên nhân:** Field không tồn tại trong model
**Giải pháp:** Kiểm tra model definition và migration

## 📞 Hỗ trợ

Nếu vẫn gặp vấn đề, hãy:
1. Chạy script debug để xem logs chi tiết
2. Kiểm tra dữ liệu trong database
3. Xem logs server để tìm lỗi cụ thể 