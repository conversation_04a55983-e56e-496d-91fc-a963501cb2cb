{"name": "ql-ctdt-monorepo", "version": "1.0.0", "description": "<PERSON><PERSON><PERSON><PERSON> lý chương trình đào tạo - Monorepo", "private": true, "scripts": {"dev": "concurrently \"pnpm --filter backend dev\" \"pnpm --filter frontend dev\"", "dev:frontend": "pnpm --filter frontend dev", "dev:backend": "pnpm --filter backend dev", "build": "pnpm --filter backend build && pnpm --filter frontend build", "build:frontend": "pnpm --filter frontend build", "build:backend": "pnpm --filter backend build", "start": "concurrently \"pnpm --filter backend start\" \"pnpm --filter frontend start\"", "start:frontend": "pnpm --filter frontend start", "start:backend": "pnpm --filter backend start", "lint": "pnpm --filter frontend lint", "clean": "pnpm --filter backend clean && pnpm --filter frontend clean", "install:all": "pnpm install", "test": "pnpm --filter backend test && pnpm --filter frontend test"}, "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@8.15.0"}