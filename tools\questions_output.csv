﻿question_type_id,level_id,question_text,lo_id,answer_1,iscorrect_1,answer_2,iscorrect_2,answer_3,iscorrect_3,answer_4,iscorrect_4,explanation
1,1,Web Server là gì?,1,<PERSON><PERSON> máy chủ dùng để lưu trữ các trang web trên Internet.,1,<PERSON><PERSON> máy tính đang được sử dụng để xem nội dung các trang web.,0,<PERSON><PERSON> một dịch vụ được cung cấp trên Internet.,0,<PERSON><PERSON> một máy chủ chia sẻ file trên mạng Internet.,0,<PERSON><PERSON><PERSON> chủ dùng để lưu trữ các trang web trên Internet gọi là Web server
1,1,HTTP là gì?,1,<PERSON><PERSON><PERSON> thức dùng để giao tiếp giữa Web Browser và Web Server.,1,<PERSON><PERSON><PERSON> thứ<PERSON> truyền tải file giữa Client và Server.,0,<PERSON><PERSON><PERSON> chỉ dùng để định vị nguồn tài nguyên trong dịch vụ web.,0,<PERSON><PERSON>t dịch vụ cho phép tra cứu tài nguyên trên Internet.,0,HTTP là giao thức dùng để giao tiếp giữa trình duyệt web và web server
1,1,Sitemap nghĩa là gì?,1,Bản đồ chỉ đường đến nơi sở hữu trang web,0,Bản đồ hỗ trợ người dùng,0,Danh sách liệt kê tất cả các mục thông tin trên trang web,1,Danh sách liệt kê tất cả các bản đồ của website,0,Để liệt kê danh sách tất cả các mục thông tin trên trang web ta sử dụng sitemap
1,1,Cấu trúc URL nào là ĐÚNG?,1,[Giao thức]://[Đường dẫn]/[Tên miền],0,[Tên miền]/[Giao thức]/[Đường dẫn],0,[Đường dẫn]://[Tên miền]/[Giao thức],0,[Giao thức]://[Tên miền]/[Đường dẫn],1,Cấu trúc đúng của URL là: http://tên miền/đường dẫn (http là giao thức) vd: https://www.tvu.edu.vn/huong-dan-dang-ky-xet-tuyen-truc-tuyen-phuong-thuc-2-xet-hoc-ba-2/
1,1,Cấu trúc của một địa chỉ email nào là hợp lệ?,1,<Tên người dùng>@<Tên miền>,1,<Tên người dùng>.<Tên miền>,0,<Tên miền>@<Tên người dùng>,0,<Tên miền>.<Tên người dùng>,0,Ví dụ:
1,1,Cho biết phát biểu SAI về giao thức TCP/IP,1,Là nền tảng để HTTP có thể hoạt động,0,"Giao thức để truyền tin giữa các máy tính, thiết bị",0,Giao thức TCP/IP có 4 tầng,0,Cần có giao thức FTP để chạy TCP/IP,1,FTP để truyền tập tin sử dụng TCP/IP
1,1,Trang chủ của một website được gọi là gì?,1,Website,0,Webpage,0,Homepage,1,Site,0,"Trang chủ gọi là homepage (tùy theo ngôn ngữ có thể là index.html, index.php hoặc default.aspx)"
1,2,Để HTTP có thể hoạt động cần có giao thức gì?,1,UDP,0,TCP/IP,1,SCP,0,SMB,0,Http là một giao thức ứng dụng được sử dụng thường xuyên nhất trong bộ các giao thức TCP/IP
1,2,"Khi truy cập vào website, trình duyệt thông báo “Bandwidth Exceeded” có nghĩa là:",1,Mạng Internet không đủ băng thông,0,Hosting website bị sự cố,0,Dịch vụ host hết băng thông,1,Máy tính bị lỗi,0,“Bandwidth Exceeded” báo dịch vụ host của bạn hết băng thông
1,2,HTML là gì?,1,Một ngôn ngữ viết web,1,Một phần mềm viết web,0,Một chương trình duyệt web,0,Một ứng dụng trên web,0,HTML (Hypertext Markup Language) là ngôn ngữ để viết web.
1,2,Siêu văn bản là gì?,1,"Là loại văn bản chỉ chứa các thành phần âm thanh, hình ảnh",0,Là loại văn bản có thể tham chiếu tới khái niệm hay văn bản khác,1,Là loại văn bản mà bản thân nó có nội dung rất lớn,0,Là loại văn bản có thể chứa thêm văn bản khác,0,Siêu văn bản là loại văn bản có thể tham chiếu tới khái niệm hay văn bản khác
1,2,Thẻ nào sau đây dùng để ghi chú trong ngôn ngữ HTML là:,1,</*…/>,0,<#.../>,0,<?.../>,0,<!--…-->,1,<!-- nội dung ghi chú -->
1,2,Cách viết nào sau đây là SAI khi gọi thẻ HTML?,1,<HTML>,0,< HTML>,1,<Html>,0,<html>,0,< HTML> sai bởi vì có dấu khoảng cách phía trước từ HTML.
1,2,"Trong HTML, thẻ kép là thẻ:",1,Có thẻ mở và thẻ đóng,1,Chỉ có một thẻ mở,0,Có chứa thẻ khác,0,Được mở hai lần,0,Có thẻ mở và thẻ đóng gọi là thẻ kép
1,3,"Trong HTML, thẻ đơn là thẻ:",1,Chỉ có thẻ mở,1,Chỉ có thẻ đóng,0,Chỉ gọi được một lần,0,Có thể chứa thẻ khác,0,Thẻ chỉ có thẻ mở gọi là thẻ đơn
1,3,Nhóm thẻ nào sau đây đều là thẻ đơn?,1,"<big>, <br>, <hr>, <input>",0,"<sup>, <link>, <br>, <hr>",0,"<sub>, <hr>, <img>, <input>",0,"<br>, <hr>, <input>, <link>",1,"<br>, <hr>, <input>, <link>"
1,3,Thẻ nào sau đây là phần tử gốc của một tài liệu HTML?,1,<html>,1,<body>,0,<table>,0,<form>,0,<html>
1,3,"Theo quy tắc, tên trang web không được chứa ký tự nào sau đâu?",1,Ký tự gạch dưới “_”,0,Ký tự gạch ngang “–”,0,Dấu chấm hỏi “?”,1,Dấu khoảng trắng “ ”,0,Dấu chấm hỏi (?) không được dùng để đặt tên.
1,1,"Trong HTML, thẻ <ol> dùng để:",2,Tạo danh sách không thứ tự,0,Tạo danh sách có thứ tự,1,Tạo các dòng cho bảng biểu,0,Tạo các cột cho bảng biểu,0,Thẻ <ol> được sử dụng để định nghĩa danh sách có thứ tự
1,1,"Trong HTML, khi tạo danh sách có thứ tự, thuộc tính nào được sử dụng để thiết lập giá trị bắt đầu của danh sách?",2,type,0,align,0,title,0,start,1,Thuộc tính start để thiết lập ký tự bắt đầu
1,1,"Trong HTML, thuộc tính nào của thẻ <font> được sử dụng để định dạng màu chữ?",2,bgcolor,0,size,0,color,1,face,0,Thuộc tính color để định dạng màu chữ
1,1,"Trong HTML, thuộc tính nào của thẻ <body> được sử dụng để định dạng màu nền cho toàn trang?",2,bgcolor,1,background,0,text,0,alink,0,Thuộc tính bgcolor để thay đổi màu nền trang web
1,1,"Trong HTML, thuộc tính nào của thẻ <body> được sử dụng để định dạng ảnh nền cho toàn trang?",2,bgcolor,0,background,1,text,0,alink,0,Thuộc tính background để định dạng ảnh nền cho trang web
1,1,"Trong trang web, liên kết (hay siêu liên kết) là gì?",2,Là địa chỉ thư điện tử.,0,Là địa chỉ của một trang web.,0,Là nội dung được thể hiện trên trình duyệt.,0,Là thành phần trỏ đến vị trí khác trên cùng trang web hoặc trỏ đến một trang web khác.,1,"Trong trang web, liên kết (hay siêu liên kết) là một thành phần trong trang web trỏ đến vị trí khác trên cùng trang web đó hoặc trỏ đến một trang web khác."
1,1,Có bao nhiêu loại siêu liên kết?,2,01 loại,0,02 loại,1,03 loại,0,04 loại,0,Liên kết trong và liên kết ngoài
1,1,Siêu liên kết trong là gì?,2,Liên kết đến phần tử thông tin khác trong cùng một tài liệu,1,Liên kết đến một tài liệu khác trong cùng một website,0,Liên kết đến một tài liệu khác trong cùng một thư mục,0,Liên kết đến phần tử thông tin trong một website khác,0,Liên kết trong là liên kết đến phần tử thông tin khác trong cùng một tài liệu
1,1,Siêu liên kết có thể gọi thực thi một chương trình hoặc một đoạn mã lệnh được gọi là gì?,2,Liên kết trong,0,Liên kết ngoài,0,Liên kết có thể thực thi được,1,Liên kết trong và liên kết ngoài,0,Liên kết có thể thực thi được
1,1,Siêu liên kết có thể liên kết đến một tài liệu khác nằm trong cùng một Website hoặc liên kết ra Website khác được gọi là gì?,2,Liên kết trong,0,Liên kết ngoài,1,Liên kết có thể thực thi được,0,Liên kết trong và liên kết ngoài,0,Siêu liên kết có thể liên kết đến một tài liệu khác nằm trong cùng một Website hoặc liên kết ra Website khác được gọi liên kế ngoài
1,1,"Để thực hiện việc liên kết từ vị trí 1 chuyển hướng đến vị trí 2 trong cùng một trang HTML, ta thực hiện:",2,"Tại vị trí 1 sử dụng thẻ <a> với thuộc tính href, tại vị trí 2 sử dụng thẻ <a> với thuộc tính name",1,"Tại vị trí 1 sử dụng thẻ <a> với thuộc tính name, tại vị trí 2 sử dụng thẻ <a> với thuộc tính href",0,Tại cả hai vị trí 1 và 2 đều sử dụng thẻ <a> với thuộc tính href,0,Tại cả hai vị trí 1 và 2 đều sử dụng thẻ <a> với thuộc tính name,0,"Tại vị trí 1 sử dụng thẻ <a> với thuộc tính href, tại vị trí 2 sử dụng thẻ <a> với thuộc tính name"
1,1,"Đối với liên kết trong, giá trị của thuộc tính href gồm những gì?",2,Dấu $ và giá trị của thuộc tính name,0,Dấu # và giá trị của thuộc tính name,1,Dấu * và giá trị của thuộc tính name,0,Dấu ? và giá trị của thuộc tính name,0,Sử dụng dấu # và tên của thẻ <a> chỉ vị trí cần chuyển hướng đến
1,1,Giá trị nào KHÔNG có trong thuộc tính align của thẻ <img>?,2,center,1,left,0,right,0,top,0,Center
1,1,"Trong HTML, cặp thẻ nào bắt buộc phải sử dụng khi cần tạo một biểu mẫu?",2,<table>,0,<body>,0,<form>,1,<iframe>,0,<form action='' method='' name=''> </form>
1,1,"Trong HTML, khi thiết kế biểu mẫu, thẻ nào được sử dụng để tạo một text field?",2,<select>,0,<textarea>,0,<input>,1,<fieldset>,0,<input type='text'>
1,1,"Trong HTML, khi thiết kế biểu mẫu, thẻ nào được sử dụng để tạo một radio button?",2,<select>,0,<textarea>,0,<input>,1,<fieldset>,0,<input type='radio'>
1,1,"Trong HTML, thuộc tính nào của thẻ <option> được sử dụng để thiết lập giá trị mặc định cho Combobox và Listbox?",2,selected,1,checked,0,value,0,readonly,0,Thuộc tính selected của thẻ <option> được sử dụng để thiết lập giá trị mặc định cho Combobox và Listbox
1,1,"Trong HTML, thuộc tính nào của thẻ <input> được sử dụng để thiết lập giá trị mặc định cho Radio Button và Checkbox?",2,selected,0,checked,1,value,0,readonly,0,Thuộc tính checked của thẻ <input> được sử dụng để thiết lập giá trị mặc định cho Radio Button và Checkbox?
1,1,"Trong HTML, thẻ nào được sử dụng để tạo file control cho phép người dùng đính kèm tập tin vào biểu mẫu?",2,<select>,0,<textarea>,0,<input>,1,<fieldset>,0,<input type='file'>
1,1,"Trong HTML, khi thiết kế biểu mẫu, thẻ nào được sử dụng tạo Password Field?",2,<select>,0,<textarea>,0,<input>,1,<fieldset>,0,<input type='password'>
1,2,"Trong HTML, thẻ nào sau đây KHÔNG dùng để tạo tiêu đề?",2,<h1>,0,<h2>,0,<h3>,0,<hr>,1,"Thẻ <hr> dùng để tạo đường kẻ ngang, các thẻ từ <h1> đến <h6> được sử dụng để tạo tiêu đề."
1,2,"Để người dùng có thể tìm thấy trang web của bạn trên các công cụ tìm kiếm Internet, bạn cần khai báo các từ khóa (keyword) trong thuộc tính nào của thẻ <meta>?",2,name,0,content,1,http-equiv,0,charset,0,Sử dụng content trong thẻ <meta>
1,2,"Để xây dựng được một website sao cho phục vụ đúng đối tượng, việc cần làm trước tiên là:",2,Xây dựng kịch bản tìm kiếm,0,Lập sơ đồ cấu trúc website,0,Đánh giá nhu cầu của đối tượng,0,Xác định đối tượng phục vụ,1,Cần phải Xác định đối tượng phục vụ
1,2,"Trong HTML, để thêm cột cho bảng, ta sử dụng thẻ:",2,<title>,0,<caption>,0,<tr>,0,<td>,1,<td> </td> để tạo thêm cột trong <tr> </tr>
1,2,"Trong HTML, để xóa một dòng trong bảng, ta xóa bỏ cặp thẻ và nội dung nằm bên trong cặp thẻ nào?",2,<title> … </title>,0,<caption> … </caption>,0,<tr> … </tr>,1,<th> … </th>,0,<tr> </tr> dòng
1,2,"Trong HTML, để thay đổi độ rộng của một cột trong bảng, ta sử dụng thuộc tính width cho:",2,Thẻ <table>,0,Thẻ <tr>,0,Thẻ <td>,1,Thẻ <caption>,0,"Thay đổi độ rộng cột sử dụng width trong thẻ <td>, Ví dụ: <td width= ‘giá trị’>"
1,2,"Phát biểu nào sau đây là SAI, khi nói về thẻ <th> và <td>?",2,"Thẻ <th> định dạng nội dung trong ô in đậm, canh giữa",0,"Thẻ <td> định dạng nội dung trong ô in đậm, canh giữa",1,Thẻ <th> và <td> đều là thẻ để tạo một ô trong bảng,0,Thẻ <th> và <td> phải được chứa trong cặp thẻ <tr> </tr>,0,"Thẻ <td> định dạng nội dung trong ô in đậm, canh giữa là phát biểu sai về <td>. Vì <td> là thẻ chỉ sử dụng để tạo một ô trong bảng"
1,2,"Các thuộc tính colspan, rowspan nằm trong nhóm thẻ nào sau đây?",2,<table> và <td>,0,<table> và <tr>,0,<tr> và <th>,0,<th> và <td>,1,"Các thuộc tính colspan, rowspan nằm trong nhóm thẻ th và td"
1,2,"Để thu nhỏ ảnh so với kích thước thật của ảnh trên web, ta sử dụng các thuộc tính:",2,"width, height",1,"align, border",0,"width, align",0,"height, border",0,"Width: chiều rộng, height: chiều cao"
1,2,"Trong HTML, có mấy phương thức gửi dữ liệu từ biểu mẫu đến server?",2,01,0,02,1,03,0,04,0,Phương thức get và post
1,2,"Trong HTML, khi thiết kế biểu mẫu, thẻ nào được sử dụng để tạo ra ô nhập liệu trên nhiều dòng?",2,<select>,0,<textarea>,1,<input>,0,<fieldset>,0,<textarea></textarea>
1,2,"Để thay đổi kích thước khung Text Field, ta sử dụng thuộc tính:",2,size,1,maxlength,0,value,0,type,0,size
1,2,"Trong HTML, thuộc tính background KHÔNG được áp dụng cho thẻ nào sau đây?",2,<body>,0,<table>,0,<tr>,1,<td>,0,<tr> không có thuộc tính background
1,2,"Trong HTML, để thiết lập giá trị mặc định cho Password Field khi trang web được tải lên, ta sử dụng thuộc tính:",2,size,0,maxlength,0,value,1,type,0,Value để gán giá trị
1,2,"Trong HTML, dòng lệnh <input type= “text” …> dùng để làm gì?",2,Tạo một ô text để nhập dữ liệu,1,Tạo một ô password,0,Tạo ô nhập liệu nhiều cột nhiều dòng,0,Tạo một nút lệnh,0,Tạo một ô text để nhập dữ liệu dùng <input type= ‘text’>
1,2,Thẻ <input type=“Password”> dùng để làm gì?,2,Tạo một ô text để nhập dữ liệu,0,Tạo một ô password,1,Tạo ô nhập liệu nhiều cột nhiều dòng,0,Tạo một nút lệnh,0,Thẻ <input type= “Password” …> dùng để tạo ô nhập password
1,2,Thẻ <input type=“Submit”> dùng để làm gì?,2,Tạo một ô text để nhập dữ liệu,0,Tạo một ô password,0,Tạo ô nhập liệu nhiều cột nhiều dòng,0,Tạo một nút lệnh để gửi thông tin trong form,1,Thẻ <input type= “Submit” …> dùng để tạo nút lệnh gửi thông tin trong form
1,2,Thẻ <textarea rows=“..” cols=“…”></textarea> dùng để làm gì?,2,Tạo một ô text để nhập dữ liệu,0,Tạo một ô password,0,Tạo ô nhập liệu nhiều cột nhiều dòng,1,Tạo một nút lệnh,0,Tạo ô nhập liệu nhiều cột nhiều dòng dùng textarea
1,2,Thẻ <input type=“Radio”> dùng để làm gì?,2,Tạo một ô text để nhập dữ liệu,0,Tạo một nhóm đối tượng chọn nhưng chọn duy nhất,1,Tạo ô nhập liệu nhiều cột nhiều dòng,0,Tạo một nút lệnh,0,Tạo một nhóm đối tượng chọn nhưng chọn duy nhất dùng type=radio
1,2,Thẻ <input type=“checkbox”> dùng để làm gì?,2,Tạo một ô text để nhập dữ liệu,0,Tạo một nhóm đối tượng chọn được nhiều đối tượng,1,Tạo một cùng có nhiều cột nhiều dòng,0,Tạo một nút lệnh để gửi thông tin trong form,0,Tạo một nhóm đối tượng chọn được nhiều đối tượng dùng type=checkbox
1,3,Thẻ <input type= “button” …> dùng để làm gì?,2,Tạo một ô text để nhập dữ liệu,0,Tạo một nút lệnh lên trên form,1,Tạo một cùng có nhiều cột nhiều dòng,0,Tạo một nhóm đối tượng chọn được nhiều đối tượng,0,Tạo một nút lệnh lên trên form không gửi thông tin submit
1,3,Để định nghĩa một tập các frame đơn ta sử dụng thẻ:,2,<frame>,0,<noframe>,0,<ifframe>,1,<frameset>,0,Sử dụng <frameset> để định nghĩa một tập các frame đơn.
1,3,"Trong HTML, khi thiết kế biểu mẫu, để tạo file control, ta sử dụng lệnh nào sau đây:",2,<input type=“file”>,1,<input type=“button”>,0,<input type=“hidden”,0,<input type=“image”>,0,<input type= “file”> cho phép chọn file
1,3,"Trong HTML, để định dạng tiêu đề có kích thước lớn nhất, ta dùng:",2,<h2>,0,<h1>,1,<h4>,0,<h3>,0,<h1> là tiêu đề có size lớn nhất
1,3,"Trong HTML, đâu là thẻ để xuống dòng?",2,<b>,0,<br>,1,<hr>,0,<dl>,0,<br> dùng để xuống dòng (dùng <br> hoặc <br />)
1,3,"Trong HTML, đâu là thẻ tạo ra chữ in đậm?",2,<b>,1,<i>,0,<u>,0,<dl>,0,<b> dùng in đậm văn bản
1,3,"Trong HTML, đâu là thẻ tạo ra chữ in nghiêng?",2,<u>,0,<a>,0,<i>,1,<b>,0,<i> dùng in nghiêng chữ
1,3,Dòng lệnh nào dùng để khi click chuột vào link thì mở cùng 1 cửa sổ?,2,"<a href=""url"" target=""_top"">",0,"<a href=""url"" target=""_self"">",1,"<a href=""url"" target=""_blank"">",0,"<a href=""url"" target=""_parent"">",0,"<a href=""url"" target=""_self"">"
1,3,"Trong HTML, những thẻ nào được sử dụng khi thiết kế bảng biểu?",2,<table><tr><td>,1,<thead><body><tr>,0,<table><head><tfoot>,0,<table><tr><tt>,0,<table><tr><td>..</td>
1,3,"Trong HTML, khi thiết kế bảng biểu, dòng lệnh nào được sử dụng để tạo ra một ô với định dạng căn lề trái cho nội dung của ô.",2,<tdleft>,0,"<td valign=""left"">",0,"<td align=""left"">",1,<td leftalign>,0,"<td align=""left"">"
1,3,"Trong HTML, khi thiết kế biểu mẫu, thẻ nào tạo ra 1 drop-down list?",2,<select></select>,1,<list></list>,0,"<input type=""dropdown"">",0,"<input type=""list"">",0,<select>
1,3,"Trong HTML, thẻ nào dùng để chèn 1 hình ảnh vào trang web?",2,"<image src=""image.gif"">",0,<img>image.gif</img>,0,"<img src=""image.gif"">",1,"<img href=""image.gif>",0,"<img src=""image.gif""> đúng cú pháp"
1,3,"Trong HTML, ký hiệu nào dưới đây thể hiện một thẻ mở?",2,</>,0,<>,1,<\>,0,<!-->,0,<tên thẻ>; đáp án B đúng.
1,3,"Trong HTML, danh sách định nghĩa (definition lists) được đóng bởi thẻ nào?",2,</dd>,0,</dt>,0,</dc>,0,</dl>,1,</dl> đóng
1,1,"Để ảnh nền trang web không lặp lại, trong CSS ta sử dụng thuộc tính:",3,background-repeat,1,background-attachment,0,background-position,0,background-image,0,background-repeat: none
1,1,Hãy chọn thứ tự tác dụng đúng nhất của CSS lên trang web theo thứ tự ưu tiên giảm dần:,3,"Inline style sheet, Internal style sheet, External style sheet, Browser default",1,"Internal style sheet, External style sheet, Browser default, Inline style sheet",0,"External style sheet, Browser default, Inline style sheet, Internal style sheet",0,"Browser default, Inline style sheet, Internal style sheet, External style sheet",0,"Inline style sheet, Internal style sheet, External style sheet, Browser default"
1,1,"Khi viết CSS, trường hợp bộ chọn là một id, ta sử dụng dấu gì trước tên id?",3,Dấu chấm “.”,0,Dấu thăng “#”,1,Dấu đô-la “$”,0,Dấu chấm hỏi “?”,0,Dấu thăng “#” dùng cho id; dấu . dùng cho class
1,1,"Khi viết CSS, trường hợp bộ chọn là một lớp (class), ta sử dụng dấu gì trước tên lớp?",3,Dấu chấm “.”,1,Dấu thăng “#”,0,Dấu đô-la “$”,0,Dấu chấm hỏi “?”,0,Dấu thăng “#” dùng cho id; dấu . dùng cho class
1,1,Phần ghi chú nào sau đây thì đúng trong CSS?,3,# nội dung ghi chú,0,// nội dung ghi chú,0,/* nội dung ghi chú */,1,<!-- nội dung ghi chú -->,0,/* nội dung ghi chú */
1,1,Tập tin CSS có phần mở rộng là gì?,3,*.cs,0,*.css,1,*.style,0,*.js,0,.css
1,1,"Để ảnh nền trang web không cuộn theo thanh trượt, trong CSS ta sử dụng thuộc tính:",3,background-repeat,0,background-attachment,1,background-position,0,background-image,0,background-attachment
1,1,"Để đổi màu cho liên kết đã truy cập, ta viết CSS cho bộ chọn:",3,a:link,0,a:visited,1,a:active,0,a:hover,0,"a:visited: khi đã truy cập, a:active: khi đang hoạt động, a:hover: khi rê chuột vào"
1,1,"Để đổi màu cho liên kết đang hoạt động, ta viết CSS cho bộ chọn:",3,a:link,0,a:visited,0,a:active,1,a:hover,0,"a:visited: khi đã truy cập, a:active: khi đang hoạt động, a:hover: khi rê chuột vào"
1,1,"Để đổi màu cho liên kết đang được trỏ chuột vào, ta viết CSS cho bộ chọn:",3,a:link,0,a:visited,0,a:active,0,a:hover,1,"a:visited: khi đã truy cập, a:active: khi đang hoạt động, a:hover: khi rê chuột vào"
1,1,"Để bỏ đường gạch chân mặc định cho các siêu liên kết, trong CSS ta dùng thuộc tính:",3,text-decoration,1,font-weight,0,font-style,0,font-stretch,0,text-decoration: none
1,1,CSS không có công dụng nào sau đây?,3,Giúp làm gọn mã nguồn HTML,0,Tiết kiệm thời gian tải lại trang,0,Bổ sung nhiều thuộc tính định dạng,0,Giúp người dùng tìm kiếm nhanh,1,Css không thể giúp người dùng tìm kiếm nhanh
1,1,Có bao nhiêu loại CSS?,3,01,0,02,0,03,1,04,0,"3 loại (Inline style sheet, Internal style sheet, External style sheet)"
1,1,Có bao nhiêu kiểu/cách viết CSS?,3,01,0,02,1,03,0,04,0,Có 2 kiểu viết: Selector{propertiy1:value1;…} và <tagname … style=“property1:value1;…”>
1,1,CSS loại nào thì được viết trong cặp thẻ <style> </style>?,3,Inline style sheet,0,Internal style sheet,1,External style sheet,0,Không có loại nào,0,Internal style sheet
1,1,CSS loại nào thì được viết trong thuộc tính style của các thẻ HTML?,3,Inline style sheet,1,Internal style sheet,0,External style sheet,0,Không có loại nào,0,Inline style sheet
1,1,"Giả sử trong trang web có CSS loại Inline style sheet định dạng cho văn bản chữ màu xanh, in đậm. Cũng trong trang web này, có sử dụng thêm CSS loại External style sheet định dạng cho cùng văn bản đó chữ màu đỏ, in nghiêng. Kết quả trang web này sẽ hiển thị đoạn văn bản như thế nào?",3,"Chữ màu đỏ, in đậm, in nghiêng",0,"Chữ màu đỏ, in nghiêng",0,"Chữ màu xanh, in đậm, in nghiêng",1,"Chữ màu xanh, in nghiêng",0,"Chữ màu xanh, in đậm, in nghiêng"
1,1,"Giả sử trong trang web có CSS loại Inline style sheet định dạng cho văn bản chữ màu xanh, in đậm. Cũng trong trang web này, có sử dụng thêm CSS loại Internal style sheet định dạng cho cùng văn bản đó chữ màu đỏ, gạch chân. Kết quả trang web này sẽ hiển thị đoạn văn bản như thế nào?",3,"Chữ màu đỏ, in đậm, gạch chân",0,"Chữ màu đỏ, gạch chân",0,"Chữ màu xanh, in đậm, gạch chân",1,"Chữ màu xanh, gạch chân",0,"Chữ màu xanh, in đậm, gạch chân (Kết hợp các thuộc tính; ưu tiên trong inline)"
1,1,"Nếu chỉ sử dụng CSS, làm cách nào để định dạng cho một đoạn văn thụt vào đầu đoạn?",3,Dùng thuộc tính text-align,0,Dùng thuộc tính text-transform,0,Dùng thuộc tính text-decoration,0,Dùng thuộc tính text-indent,1,Dùng thuộc tính text-indent để thụt vào đầu đoạn
1,1,Đoạn mã CSS sau có ý nghĩa gì? p strong {color: green;},3,"Tất cả những phần văn bản nào được đặt trong các thẻ <p>, <strong> thì có màu xanh lá",0,Bất kỳ phần văn bản nào nằm trong thẻ <p> hoặc thẻ <strong> thì đều có màu xanh lá,0,Chỉ khi đúng thứ tự thẻ <strong> chứa thẻ <p> thì phần văn bản bên trong mới có màu xanh lá,0,Chỉ khi đúng thứ tự thẻ <p> chứa thẻ <strong> thì phần văn bản bên trong mới có màu xanh lá,1,Chỉ khi đúng thứ tự thẻ <p> chứa thẻ <strong> thì phần văn bản bên trong mới có màu xanh lá
1,2,"Trong CSS, để xác định phạm vi khai báo của một bộ chọn, ta sử dụng cặp dấu nào sau đây?",3,Ngoặc tròn “()”,0,Ngoặc nhọn “{}”,1,Ngoặc vuông “[]”,0,Bé và lớn “<>”,0,Ngoặc nhọn “{}” cấu trúc viết css
1,2,"Trong CSS, để bỏ ký hiệu đầu danh sách, ta dùng thuộc tính nào sau đây?",3,list-style: none;,1,list-style: circle;,0,list-style: disc;,0,list-style: square;,0,list-style: none;
1,2,"Để gọi CSS từ file bên ngoài, ta sử dụng thẻ:",3,<link>,1,<a>,0,<input>,0,<meta>,0,<link href= ‘đường dẫn’>
1,2,"Với CSS loại Internal style sheet, ta chèn mã nguồn CSS ở vị trí nào của trang HTML?",3,Trong cặp thẻ <title> </title>,0,Trong cặp thẻ <head> </head>,1,Trong cặp thẻ <body> </body>,0,Giữa thẻ đóng </head> và thẻ mở <body>,0,Trong cặp thẻ <head> </head>
1,2,Vì sao gọi CSS là bảng mẫu nạp chồng?,3,Vì CSS có thể chèn vào trang HTML,0,Vì CSS quy định cách hiển thị trang web,0,Vì nhờ CSS mà ít tốn thời gian tải trang web hơn,0,Vì CSS có thể được gọi nhiều lần với giá trị khác nhau cho cùng một thuộc tính,1,Vì CSS có thể được gọi nhiều lần với giá trị khác nhau cho cùng một thuộc tính
1,2,Thuộc tính text-transform trong CSS có tác dụng gì?,3,Canh lề văn bản,0,Chuyển đổi ký tự HOA/thường,1,Gạch chân văn bản,0,Thụt vào đầu đoạn,0,Chuyển đổi ký tự HOA/thường
1,2,CSS là viết tắt của cụm từ nào?,3,Creative style sheets,0,Computer style sheets,0,Cascading style sheets,1,Colorful style sheets,0,Cascading style sheets (CSS)
1,2,"Trong HTML, thẻ nào định nghĩa CSS ở ngay trong tập tin HTML?",3,<css>,0,<script>,0,<style>,1,<html>,0,<style>
1,2,"Trong HTML, thuộc tính nào định nghĩa CSS ngay trong thẻ mở của phần tử HTML?",3,Font,0,Class,0,Style,1,Styles,0,<tên thẻ style=”…”>
1,2,"Trong CSS, dòng lệnh nào tuân theo cú pháp của CSS?",3,Body {color: black},1,{body;color:black},0,Body:color=black,0,{body:color=black(body},0,Body {color: black} Sử dụng tên thẻ để thiết lập thuộc tính
1,2,"Trong CSS, dòng lệnh nào dùng để ghi chú trong CSS?",3,/* this is a comment */,1,// this is a comment //,0,‘ this is a comment,0,// this is a comment,0,/* this is a comment */
1,2,"Trong CSS, làm thế nào thêm màu nền cho tất cả các phần tử <h1> ?",3,H1.all {background-color:#ffffff},0,H1 {background-color:#ffffff},1,All.h1 {background-color:#ffffff},0,h1:{ background-color:#ffffff},0,H1 {background-color:#ffffff}
1,2,"Trong CSS, thuộc tính nào thay đổi kích cỡ chữ?",3,Font-style,0,Font-size,1,Text-style,0,Text-size,0,Font-size
1,2,Thuộc tính nào làm chữ trong thẻ p trở thành chữ đậm?,3,{text-size:bold},0,<p style=“font-size:bold”>,0,<p style= “text-size:bold”>,0,p{font-weight:bold},1,p{font-weight:bold}
1,2,"Trong CSS, làm sao để hiển thị liên kết mà ko có gạch chân bên dưới?",3,a{decoration:no underline},0,a{text-decoration:no underline},0,a{underline:none},0,a{text-decoration:none},1,A {text-decoration:none}
1,2,"Trong CSS, làm sao để thay đổi lề trái của một phần tử?",3,margin-left:20px,1,text-indent:20px,0,margin:20px,0,indent:20px,0,Margin-left: giá trị;
1,2,Những phát biểu nào là ĐÚNG khi nói về style sheet?,3,Có thể đặt trong thẻ meta,0,Có thể đặt bên trong một trang html,1,"Có thể đặt ở một file bên ngoài, và không được liên kết với trang html",0,Không thể chứa nhiều hơn một luật (rule),0,Có thể đặt bên trong một trang html
1,2,"Khi sử dụng thẻ <link> để liên kết với file CSS, chúng ta có thể đặt thẻ này ở vùng nào trong file HTML?",3,Trong thẻ <body>,0,Trong thẻ <head>,1,Ở cuối file HTML,0,Ở đầu file HTML,0,Trong thẻ <head>
1,2,"Trong CSS, thuộc tính text-indent dùng để:",3,Thiết lập khoảng cách thụt đầu dòng,1,Thiết lập chế độ canh văn bản,0,Thêm hiệu ứng đặc biệt cho văn bản,0,Thêm vào khoảng cách đoạn,0,Thiết lập khoảng cách thụt đầu dòng
1,2,"Trong mã màu RGB dạng hệ thập lục, #FFFFFF là màu gì?",3,Đen,0,Trắng,1,Đỏ,0,Xanh,0,"#000 là màu đen, (Dãy màu) #FFF là màu trắng"
1,3,"Trong CSS, thuộc tính z-index dùng để làm gì?",3,Định vị tương đối cho một thành phần,0,Quy định kiểu viền của một đối tượng web,0,Tạo hiệu ứng màu sắc cho liên lết,0,Đặt các thành phần web ở các lớp khác nhau,1,Đặt các thành phần web ở các lớp khác nhau
1,3,"Trong CSS, khai báo “text-transform: uppercase” dùng để định dạng:",3,In hoa văn bản,1,In thường văn bản,0,In hoa ký tự đầu mỗi từ,0,Không áp dụng,0,In hoa (uppercase)
1,3,"Trong HTML, để liên kết tới External Style sheet ta sử dụng thẻ:",3,<STYLE>,0,<A>,0,<LINK>,1,<CSS>,0,<LINK>
1,3,"Trong CSS, hãy cho biết 4 giá trị của border-radius lần lượt là gì?",3,"top, bottom, left, right",0,"up, down, front, behind",0,"top-left, top-right, bottom-right, bottom-left",1,"bottom-left, bottom-right, top-right, top-left",0,"top-left, top-right, bottom-right, bottom-left"
1,3,"Trong CSS, để khai báo bộ chọn là một lớp của thẻ DIV, ta sử dụng ký hiệu nào?",3,Dấu .,1,Dấu $,0,Dấu #,0,Dấu ?,0,Dấu chấm được sử dụng để tạo class trong CSS
1,3,CSS dùng để làm gì?,3,Các kịch bản máy khách.,0,Viết các ứng dụng sự kiện,0,Dùng để lập trình web từ phía máy chủ,0,Định dạng giao diện trang web,1,Định dạng giao diện trang web
1,3,"Trong CSS, hãy cho biết ph là giá trị trong thuộc tính nào của một phần tử HTML? #ph {color:red;text-align:center;}",3,style,0,name,0,id,1,class,0,Là giá trị trong thuộc tính id của một phần tử HTML
1,3,"Trong CSS, để khai báo bộ chọn là id của thẻ DIV, ta sử dụng ký hiệu nào?",3,Dấu .,0,Dấu $,0,Dấu #,1,Dấu ?,0,Dấu # được sử dụng để tạo id trong CSS
1,3,"Trong CSS, muốn định dạng nền màu đỏ cho một phần tử HTML thì khai báo nào sau đây là đúng?",3,backcolor: red;,0,Color: red;,0,Backgroundcolor: red;,0,background: red;,1,background: red;
1,3,"Trong HTML, lệnh để liên kết đến tập tin “mystyle.css” là gì?",3,<link rel=''stylesheet'' type=''text/css'' href=''mystyle.css'' />,1,<a rel=''stylesheet'' type=''text/css'' href=''mystyle.css'' />,0,<link href=''stylesheet'' type=''text/css'' ref=''mystyle.css'' />,0,<a href=''stylesheet'' type=''text/css'' src=''mystyle.css'' />,0,<link rel=''stylesheet'' type=''text/css'' href=''mystyle.css'' />
1,3,"Trong CSS, khai báo “border: solid” dùng để mô tả gì?",3,Mô tả đường viền là 2 nét,0,Mô tả đường viền có độ bóng,0,Mô tả đường viền liền nét,1,Mô tả đường viền nét đứt,0,Mô tả đường viền liền nét (solid)
1,3,"Trong CSS, thuộc tính nào thay đổi phông chữ?",3,font-style,0,font-family,1,text-style,0,text-size,0,Thuộc tính font-family để thay đổi phông chữ
1,3,"Trong CSS, khai báo “border: dashed” dùng để mô tả gì?",3,Mô tả đường viền là 2 nét,0,Mô tả đường viền có độ bóng,0,Mô tả đường viền liền nét,0,Mô tả đường viền nét đứt,1,Mô tả đường viền nét đứt (dashed)
1,3,"Trong CSS, khai báo “border: double” dùng để mô tả gì?",3,Mô tả đường viền là 2 nét,1,Mô tả đường viền có độ bóng,0,Mô tả đường viền liền nét,0,Mô tả đường viền nét đứt,0,Mô tả đường viền là 2 nét
1,1,JavaScript là ngôn ngữ xử lý ở phía ...,4,Client,1,Server,0,Server/client,0,Không có dạng nào.,0,JavaScript xử lý ở Client (máy khách)
1,1,Mã nguồn JavaScript được đặt bên trong cặp thẻ nào?,4,<script> …</script>,1,D. <Javascript><Javascript>,0,<java>   </java>,0,<html> </html>,0,<script language=’JavaScript’> …</script>
1,1,"Trong Javascript, hàm alert() dùng để làm gì?",4,Dùng để hiện một thông báo.,1,Dùng để hiện một thông báo nhập,0,Dùng để chuyển đổi số sang chữ,0,Dùng để chuyển đổi chuỗi sang số.,0,Alert() dùng để hiện một thông báo.
1,1,"Trong Javascript, hàm parseInt() dùng để làm gì?",4,Chuyển một chuỗi số thành số,0,Chuyển một chuỗi số thành số nguyên,1,Chuyển một chuỗi số thành số thực,0,Chuyển một số nguyên thành một chuỗi,0,Chuyển một chuỗi số thành số nguyên sử dụng hàm parseInt()
1,1,"Trong Javascript, hàm parseFloat() dùng để làm gì?",4,Chuyển một chuỗi số thành số,0,Chuyển một chuỗi số thành số thực,1,Chuyển một chuỗi số thành số nguyên,0,Chuyển một số nguyên thành một chuỗi,0,Chuyển một chuỗi số thành số thực sử dụng hàm parseFloat()
1,1,Lệnh prompt() trong JavaScript để làm gì?,4,Hiện một thông báo nhập thông tin,1,"Hiện một thông báo dạng Yes, No",0,Hiển thị bảng thông báo OK,0,"Hiển thị bảng thông báo Ok, Cancel",0,prompt() dùng hiện một thông báo nhập thông tin
1,1,"Trong Javascript, sự kiện Onload thực hiện khi...",4,Bắt đầu chương trình chạy,1,Click chuột,0,Kết thúc một chương trình,0,Di chuyển chuột qua,0,Khi bắt đầu chương trình chạy hàm onload()
1,1,"Trong Javascript, sự kiện OnUnload thực hiện khi nào?",4,Khi bắt đầu chương trình chạy,0,Khi click chuột,0,Khi kết thúc một chương trình,1,Khi di chuyển chuột qua.,0,Khi kết thúc chương trình sẽ gọi hàm OnUnload()
1,1,"Trong Javascript, sự kiện OnMouseOver thực hiện khi nào?",4,Khi một đối tượng trong form mất focus.,0,Khi một đối tượng trong form có focus,0,Khi di chuyển con chuột trên một phần tử HTML.,1,Khi click chuột vào nút lệnh,0,OnMouseOver() được gọi khi di chuyển con chuột qua một đối tượng trong form
1,1,"Trong Javascript, sự kiện Onclick thực hiện khi nào?",4,Khi một đối tượng trong form mất focus.,0,Khi một đối tượng trong form có focus,0,Khi double click chuột vào một phần tử HTML.,0,Khi click chuột vào một phần tử HTML,1,Onclick() được gọi khi click chuột vào nút lệnh
1,1,"Trong Javascript, sự kiện Onchange thực hiện khi nào?",4,Khi một đối tượng trong form mất focus.,0,Khi một đối tượng trong form có focus,0,Xảy ra khi giá trị của một trường trong form được người dùng thay đổi,1,Khi click chuột vào nút lệnh,0,Onchange() xảy ra khi giá trị của một trường trong form được người dùng thay đổi
1,1,"Trong Javascript, lệnh break kết hợp với vòng lặp for dùng để?",4,Thoát khỏi vòng lặp for,1,Không có ý nghĩa trong vòng lặp,0,Chuyển sang bước lặp tiếp theo,0,Không thể kết hợp được.,0,Thoát khỏi vòng lặp for nếu gặp lệnh break
1,1,Javascript có các dạng kiểu dữ liệu nào?,4,"Number, String, Boolean",1,"Number, Integer, char",0,"Number, String, Boolean, Null",0,"Interger, char, Boolean",0,"Number (số), String (chuỗi), Boolean (true/false)"
1,1,"Trong Javascript, vòng lặp for có dạng như thế nào?",4,for (biến = Giá trị đầu; Điều kiện; Giá trị tăng/giảm),1,for (biến = Giá trị đầu; Giá trị tăng; điều kiện),0,for (biến = Điều kiện; Giá trị tăng; Giá trị cuối),0,for (biến = Giá trị đầu; Điều kiện; Giá trị tăng),0,for ( biến = Giá trị đầu; Điều kiện; Giá trị tăng/giảm)
1,2,"Trong Javascript, vòng lặp while là dạng vòng lặp gì?",4,Không xác định và xét điều kiện rồi mới lặp,1,Không xác định và  lặp rồi mới xét điều kiện,0,Xác định và xét điều kiện rồi mới lặp,0,Xác định và xét điều kiện rồi mới lặp,0,Vòng lặp while() => Không xác định và xét điều kiện rồi mới lặp
1,2,"Trong Javascript, vòng lặp do...while là dạng vòng lặp gì?",4,Không xác định và xét điều kiện rồi mới lặp,0,Không xác định và  lặp rồi mới xét điều kiện,1,Xác định và xét điều kiện rồi mới lặp,0,Xác định và xét điều kiện rồi mới lặp,0,Thực hiện các lệnh bên trong sau đó kiểm tra điều kiện.
1,2,"Trong Javascript, đoạn mã nguồn sau hiển thị kết quả gì?
<script>
var text = """";
var i = 0;
while (i < 10) {
  text += "" "" + i;
  i++;
}
document.write(text);
</script>",4,0 1 2 3 4 5 6 7 8 9,1,0 1 2 3 4 5 6 7 8 9 10,0,1 2 3 4 5 6 7 8 9,0,1 2 3 4 5 6 7 8 9 10,0,Xuất từ 0 đến 9
1,2,"Trong Javascript, đoạn mã nguồn sau hiển thị kết quả gì?
<script>
var text = """";
var i = 0;
while (i <= 10) {
  text += "" "" + i;
  i++;
}
document.write(text);
</script>",4,0 1 2 3 4 5 6 7 8 9,0,0 1 2 3 4 5 6 7 8 9 10,1,1 2 3 4 5 6 7 8 9,0,1 2 3 4 5 6 7 8 9 10,0,Xuất từ 0 đến 10
1,2,"Trong Javascript, đoạn mã nguồn sau hiển thị kết quả gì?
<script>
var text = """";
var i = 0;
do {
  text += "" "" + i;
  i++;
}while(i<0)
document.write(text);
</script>",4,0,1,Lỗi,0,1,0,0 1,0,Hiển thị 0 ra sau đó dừng vì điều kiện không hợp lệ
1,2,"Trong HTML, mã nguồn JavaScript có thể đặt ở trong cặp thẻ nào của trang html?",4,<head> hoặc <body>,1,Chỉ trong phần <head>,0,Chỉ trong phần <body>,0,Phải tạo trang js,0,Có thể đặt mã nguồn Javascript trong cặp thẻ <head> hoặc <body>
1,2,Cách khai báo mảng nào trong JavaScript là đúng?,4,"var colors = 1 = (""violet""), 2 = (""black"");",0,"var colors = [""violet"", ""black""];",1,"var colors = (1:""violet"", 2:""black"");",0,"var colors = ""violet"", ""black"";",0,"var colors = [""violet"", ""black""];"
1,2,"Trong HTML, câu lệnh nào đúng khi thực hiện việc gọi một script từ bên ngoài có tên là a.js?",4,"<script src=""a.js""></script>",1,"<script name=""a.js""></script>",0,"<script href=""a.js""></script>",0,"<script link=""a.js""></script>",0,"<script src=""a.js""></script>"
1,2,Sự kiện nào xảy ra khi người dùng click vào một phần tử HTML trên trang web?,4,onclick,1,onmouseover,0,onchange,0,onmouseclick,0,Onclick()
1,2,"Cách viết câu lệnh rẽ nhánh if nào sau đây là đúng (Với a, b là hai biến đã khai báo trước đó)?",4,if a=b then,0,if (a == b),1,if  a== b then,0,if a= b,0,if (a == b)
1,2,"Trong Javascript, câu lệnh rẽ nhánh if nào sau đây là đúng?",4,if (i != 10),1,if i =! 10 then,0,if i <> 10,0,if (i <> 10),0,if (i != 10)
1,2,Biến trong Javascript được khai báo thế nào?,4,dim x=5,0,var x;,1,dime x=5,0,var 1x=5,0,var x;
1,2,Để ghép chuỗi trong JavaScript ta sử dụng ký hiệu nào?,4,+,1,&&,0,++,0,and,0,Sử dụng dấu + để ghép chuỗi.
1,2,"Trong Javascript, sự kiện nào sau không được áp dụng với form?",4,Onclick,0,OnBlur,0,Onsubmit,0,Oncharge,1,Oncharge không có trong javascript
1,3,"Trong Javascript, ý nghĩa việc đặt sự kiện focus() cho text field là gì?",4,Mô tả việc con trỏ rời trường text (cách thức),0,Mô tả việc lựa chọn dòng text trong trường text (cách thức),0,Đặt con trỏ vào ô nhập liệu,1,Tên của đối tượng được chỉ ra trong thẻ INPUT (thuộc tính),0,Mô tả việc con trỏ tới trường text (cách thức)
1,3,Để khai báo mảng trong JavaScript ta dùng ký hiệu?,4,( ),0,[( )],0,{ },0,[ ],1,Sử dụng dấu []
1,3,"Để chèn thêm 1 ký tự "" trước và sau một chuỗi trong JavaScript ta viết theo cách nào?",4,"document.write("" \""This text inside quotes.\"" "");",1,"document.write("" \""This text inside quotes.""\ "");",0,"document.write("" \This text inside quotes.\ "");",0,"document.write(""This text inside quotes"");",0,"document.write("" \""This text inside quotes.\"" "");"
1,3,"Trong Javascript, hãy cho biết đoạn mã nguồn sau xuất ra kết quả bao nhiêu nếu nhập lần lượt 20 và 18?
var xx = prompt(""Nhập giá trị thứ nhất?"");
 var xy = prompt(""Nhập giá trị thứ hai?"");
 var tong = xx + yy;
 alert(tong);",4,38,0,2018,1,Lỗi,0,1820,0,Ghép chuỗi
1,3,Hãy chọn dòng lệnh đúng với yêu cầu thêm ghi chú trên nhiều dòng trong JavaScript?,4,/*Đây là một comment trên nhiều dòng*/,1,<!--Đây là một comment trên nhiều dòng-->,0,//Đây là một comment trên nhiều dòng//,0,<? Đây là một comment trên nhiều dòng ?>,0,/*Đây là một comment trên nhiều dòng*/
1,3,Làm cách nào để gọi một hàm “myFunction” trong JavaScript?,4,call function myFunction(),0,call myFunction(),0,myFunction(),1,function myFunction(),0,Ghi đúng tên hàm để gọi
1,3,Hãy chọn dòng lệnh đúng với yêu cầu làm tròn 7.25 tới số nguyên gần nhất trong JavaScript?,4,rnd(7.25),0,round(7.25),0,Math.round(7.25),1,Math.rnd(7.25),0,Math.round(7.25)
1,3,Thẻ nào của HTML cho phép đặt mã nguồn JavaScript vào trang web?,4,<javascript>,0,<scripting>,0,<js>,0,<script>,1,<script language=‘javascript’>
1,1,Kiểu nút bootstrap nào sau đây cho biết nguy hiểm với hành động này?,5,btn-warning,1,btn-danger,0,btn-link,0,btn-info,0,btn-warning
1,1,"Trong thư viện Bootstrap, lớp nào sau đây tạo nên hình ảnh có 4 góc được bo tròn?",5,.img-round,1,.img-round-angle,0,.img-rnd,0,.img-circle,0,.img-round
1,1,"Trong thư viện Bootstrap, lớp .container cung cấp gì?",5,Vùng chứa có chiều rộng đầy đủ,0,Vùng chứa có chiều rộng cố định,1,Định dạng bảng,0,Để tạo một Biểu mẫu,0,Vùng chứa có chiều rộng cố định
1,1,"Trong thư viện Bootstrap, kích thước mặc định của tiêu đề H2 là bao nhiêu?",5,20px,0,24px,0,30px,1,36px,0,"H1: 36px, H2: 30 px"
1,1,"Trong thư viện Bootstrap, thiết bị lớn nhất được định nghĩa là có chiều rộng màn hình từ bao nhiêu pixel?",5,1000 pixel trở lên,0,1100 pixel trở lên,0,1200 pixel trở lên,1,1024 pixel trở lên,0,1200 pixel trở lên
1,1,"Trong thư viện Bootstrap, kích thước mặc định của tiêu đề lớn nhất H1?",5,20px,0,24px,0,30px,0,36px,1,"H1: 36px, H2: 30 px"
1,2,"Trong thư viện Bootstrap, từ khóa xs xác định loại thiết bị nào?",5,điện thoại có kích thước nhỏ hơn 768px,1,máy tính bảng có kích thước lớn hơn 768px đến dưới 992px,0,máy tính để bàn có kích thước từ 992px đến dưới 1200px,0,máy tính để bàn có kích thước từ 1200px trở lên,0,Điện thoại có kích thước nhỏ hơn 768px
1,2,"Trong thư viện Bootstrap, lớp .input-lg dùng để làm gì?",5,Để tạo kích thước chiều rộng ô nhập liệu,0,Để tạo kích thước chiều cao ô nhập liệu,1,Để làm cho cả chiều rộng và chiều cao  ô nhập liệu,0,Để tạo kích thước ký tự trong ô nhập liệu,0,Để tạo kích thước chiều cao ô nhập liệu
1,1,Hệ thống lưới của Bootstrap cho phép tối đa bao nhiêu cột?,5,6 cột trên trang,0,12 cột trên trang,0,8 cột trên trang,0,10 cột trên trang,0,
1,2,"Trong thư viện Bootstrap, kích thước mặc định của tiêu đề H4 là bao nhiêu?",5,18px,1,24px,0,30px,0,36px,0,H4: 18 px
1,2,"Trong thư viện Bootstrap, lớp nào sau đây tạo hình ảnh thu nhỏ?",5,.img-tmbnail,0,.img-thumbnail-image,0,.img-thumb,0,.img-thumbnail,1,Lớp .img-thumbnail tạo hình ảnh thu nhỏ
1,2,"Trong thư viện Bootstrap, lớp nào nên được sử dụng để chỉ ra một nhóm nút?",5,btn-group-button,0,btn-group,1,btn-grp,0,btn,0,Lớp btn-group được sử dụng để chỉ ra một nhóm nút
1,3,Kiểu nút bootstrap nào sau đây cho biết nguy hiểm với hành động này?,5,btn-warning,0,btn-danger,1,btn-link,0,btn-info,0,Kiểu nút bootstrap btn-danger cho biết nguy hiểm
1,3,Kiểu bootstrap nào sau đây được sử dụng để tạo điều hướng tab hợp lý?,5,".nav, .nav-tabs",0,".nav, .nav- Drugs",0,".nav, .nav-Drugs, .nav-stacked",0,".nav, .nav-tabs, .nav-justified",1,".nav, .nav-tabs, .nav-justified"
1,3,Kiểu hình ảnh bootstrap nào sau đây làm cho toàn bộ hình ảnh tròn bằng cách thêm bán kính đường viền: 500px?,5,img-round,0,img-circle,1,img-thumbnail,0,Không có điều nào ở trên.,0,.img-circle
1,1,Kiểu bootstrap nào sau đây được sử dụng để thêm liên kết chuẩn vào .navbar?,5,btn-link,0,btn- danger,0,btn-success,0,btn-info,0,
1,3,Kiểu nút bootstrap nào sau đây tạo nút mặc định / tiêu chuẩn?,5,btn-default,1,btn- primary,0,btn-success,0,btn-info,0,btn-default
