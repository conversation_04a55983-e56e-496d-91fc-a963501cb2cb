'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
    class UserQuestionHistory extends Model {
        
        static associate(models) {
            UserQuestionHistory.belongsTo(models.User, {
                foreignKey: 'user_id',
                as: 'User'
            });
            UserQuestionHistory.belongsTo(models.Question, {
                foreignKey: 'question_id',
                as: 'Question'
            });
            UserQuestionHistory.belongsTo(models.Quiz, {
                foreignKey: 'quiz_id',
                as: 'Quiz'
            });
        }
        
    }

    UserQuestionHistory.init(
        {
            
            history_id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true,
                autoIncrementIdentity: true
            },
            user_id: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: 'Users',
                    key: 'user_id',
                },
            },
            question_id: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: 'Questions',
                    key: 'question_id',
                },
            },
            quiz_id: {
                type: DataTypes.INTEGER,
                allowNull: true,
                references: {
                    model: 'Quizzes',
                    key: 'quiz_id',
                },
            },
            selected_answer: {
                type: DataTypes.INTEGER,
                allowNull: true,
            },
            is_correct: {
                type: DataTypes.BOOLEAN,
                allowNull: false,
            },
            time_spent: {
                type: DataTypes.INTEGER,
                allowNull: true,
                comment: 'Time spent in seconds'
            },
            attempt_date: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW
            },
            difficulty_level: {
                type: DataTypes.ENUM('easy', 'medium', 'hard'),
                allowNull: true
            }
            
        },
        {
            sequelize,
            modelName: 'UserQuestionHistory',
            tableName: 'UserQuestionHistories',
            timestamps: false
        }
    );

    return UserQuestionHistory;
}; 



