const { StudentCourse, User, Course } = require('../models');

exports.getAllStudentCourses = async (req, res) => {
    try {
        const studentCourses = await StudentCourse.findAll({
            include: [
                { model: User, attributes: ['user_id', 'name'] },
                { model: Course, attributes: ['course_id', 'name'] },
            ],
        });

        res.status(200).json(studentCourses);
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi lấy danh sách StudentCourse', error: error.message });
    }
};

exports.getStudentCourseById = async (req, res) => {
    try {
        const { user_id, course_id } = req.params;

        const studentCourse = await StudentCourse.findOne({
            where: { user_id, course_id },
            include: [
                { model: User, attributes: ['user_id', 'name'] },
                { model: Course, attributes: ['course_id', 'name'] },
            ],
        });

        if (!studentCourse) return res.status(404).json({ message: 'StudentCourse không tồn tại' });
        res.status(200).json(studentCourse);
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi lấy thông tin StudentCourse', error: error.message });
    }
};

exports.createStudentCourse = async (req, res) => {
    try {
        const { user_id, course_id } = req.body;

        if (!user_id || !course_id) {
            return res.status(400).json({ message: 'Thiếu các trường bắt buộc' });
        }

        const user = await User.findByPk(user_id);
        const course = await Course.findByPk(course_id);

        if (!user) return res.status(400).json({ message: 'Người dùng không tồn tại' });
        if (!course) return res.status(400).json({ message: 'Khóa học không tồn tại' });

        const newStudentCourse = await StudentCourse.create({ user_id, course_id });
        res.status(201).json(newStudentCourse);
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi tạo StudentCourse', error: error.message });
    }
};

exports.deleteStudentCourse = async (req, res) => {
    try {
        const { user_id, course_id } = req.params;

        const studentCourse = await StudentCourse.findOne({ where: { user_id, course_id } });
        if (!studentCourse) return res.status(404).json({ message: 'StudentCourse không tồn tại' });

        await studentCourse.destroy();
        res.status(200).json({ message: 'Xóa StudentCourse thành công' });
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi xóa StudentCourse', error: error.message });
    }
};