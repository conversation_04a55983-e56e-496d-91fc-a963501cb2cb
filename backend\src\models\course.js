'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
    class Course extends Model {
        static associate(models) {
            Course.belongsTo(models.User, { foreignKey: 'user_id' });
            Course.belongsTo(models.Program, { foreignKey: 'program_id' });
            Course.belongsToMany(models.User, { through: models.StudentCourse, foreignKey: 'course_id' });
            Course.hasMany(models.Subject, { foreignKey: 'course_id' });
            Course.hasMany(models.CourseResult, { foreignKey: 'course_id' });
        }
    }

    Course.init(
        {
            course_id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true,
                autoIncrementIdentity: true
            },
            user_id: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: 'Users',
                    key: 'user_id',
                },
            },
            name: {
                type: DataTypes.STRING,
                allowNull: false,
            },
            description: {
                type: DataTypes.TEXT,
            },
            start_date: {
                type: DataTypes.DATE,
            },
            end_date: {
                type: DataTypes.DATE,
            },
            program_id: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: 'Programs',
                    key: 'program_id',
                },
            },
        },
        {
            sequelize,
            modelName: 'Course',
            tableName: 'Courses',
        }
    );

    return Course;
};