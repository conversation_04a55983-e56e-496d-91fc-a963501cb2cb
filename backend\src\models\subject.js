'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
    class Subject extends Model {
        static associate(models) {
            // Quan hệ với Course, TypeSubject, TypeOfKnowledge, PLO
            Subject.belongsTo(models.Course, { foreignKey: 'course_id' });
            Subject.belongsTo(models.TypeSubject, { foreignKey: 'type_id' });
            Subject.belongsTo(models.TypeOfKnowledge, { foreignKey: 'noidung_id' });
            Subject.belongsTo(models.PLO, { foreignKey: 'plo_id' });

            // Quan hệ với Quiz
            Subject.hasMany(models.Quiz, { foreignKey: 'subject_id' });

            // Quan hệ với Chapter (mới)
            Subject.hasMany(models.Chapter, { foreignKey: 'subject_id', as: 'Chapters' });

            // Quan hệ nhiều-nhiều với chính nó thông qua TienQuyet
            Subject.belongsToMany(models.Subject, {
                as: 'PrerequisiteSubjects',
                through: models.TienQuyet,
                foreignKey: 'subject_id',
                otherKey: 'prerequisite_subject_id',
            });
        }
    }

    Subject.init(
        {
            subject_id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true,
                autoIncrementIdentity: true
            },
            course_id: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: 'Courses',
                    key: 'course_id',
                },
            },
            type_id: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: 'TypeSubjects',
                    key: 'type_id',
                },
            },
            noidung_id: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: 'TypeOfKnowledges',
                    key: 'noidung_id',
                },
            },
            name: {
                type: DataTypes.STRING,
                allowNull: false,
            },
            description: {
                type: DataTypes.TEXT,
            },
            created_at: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW,
            },
            plo_id: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: 'PLOs',
                    key: 'plo_id',
                },
            },
        },
        {
            sequelize,
            modelName: 'Subject',
            tableName: 'Subjects',
            timestamps: false, // Không sử dụng timestamps mặc định (createdAt, updatedAt)
        }
    );

    return Subject;
};