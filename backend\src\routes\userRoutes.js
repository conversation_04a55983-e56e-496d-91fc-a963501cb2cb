const express = require('express');
const router = express.Router();
const userController = require('../controllers/userController');
const { authenticateToken, authorize } = require('../middleware/authMiddleware');
const multer = require('multer');

// <PERSON><PERSON><PERSON> hình multer cho upload file
const upload = multer({ dest: 'uploads/' });

// Đăng nhập (không cần phân quyền)
router.post('/login', userController.login);

// Routes
router.get('/', authenticateToken, authorize(['admin']), userController.getAllUsers);
router.get('/:id', authenticateToken, authorize(['admin', 'teacher', 'student']), userController.getUserById);

// Tạo user
router.post('/createAdmin', authenticateToken, authorize(['admin']), userController.createAdmin);
router.post('/createTeacher', authenticateToken, authorize(['admin']), userController.createTeacher);
//router.post('/importTeachers', authenticateToken, authorize(['admin']), upload.single('file'), userController.importTeachers);

// Tạo student
router.post('/createStudent', authenticateToken, authorize(['admin', 'teacher']), userController.createStudent);
router.post('/importStudents', authenticateToken, authorize(['admin', 'teacher']), upload.single('file'), userController.importStudents);

// Cập nhật và xóa user
router.put('/:id', authenticateToken, authorize(['admin', 'teacher', 'student']), userController.updateUser);
router.delete('/:id', authenticateToken, authorize(['admin']), userController.deleteUser);

module.exports = router;