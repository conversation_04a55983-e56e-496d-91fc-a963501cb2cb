const express = require('express');
const router = express.Router();
const loController = require('../controllers/loController');
const { authenticateToken, authorize } = require('../middleware/authMiddleware');

// Public routes - có thể xem nhưng không chỉnh sửa
router.get('/subject/:subjectId', loController.getLOsBySubject);
router.get('/', loController.getAllLOs);
router.get('/:id', loController.getLOById);

// Admin only routes - CRUD operations
router.post('/', authenticateToken, authorize(['admin']), loController.createLO);
router.put('/:id', authenticateToken, authorize(['admin']), loController.updateLO);
router.delete('/:id', authenticateToken, authorize(['admin']), loController.deleteLO);

// Statistics routes - Admin and Teacher
router.get('/statistics/overview', authenticateToken, authorize(['admin', 'teacher']), loController.getLOStatistics);
router.get('/statistics/performance', authenticateToken, authorize(['admin', 'teacher']), loController.getLOPerformanceAnalysis);
router.get('/:id/questions/statistics', authenticateToken, authorize(['admin', 'teacher']), loController.getLOQuestionStatistics);

// Bulk operations - Admin only
router.post('/bulk/create', authenticateToken, authorize(['admin']), loController.bulkCreateLOs);
router.put('/bulk/update', authenticateToken, authorize(['admin']), loController.bulkUpdateLOs);
router.delete('/bulk/delete', authenticateToken, authorize(['admin']), loController.bulkDeleteLOs);

module.exports = router;
