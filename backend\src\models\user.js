'use strict';
const { Model } = require('sequelize');
const bcrypt = require('bcrypt');

module.exports = (sequelize, DataTypes) => {
    class User extends Model {
        static associate(models) {
            User.belongsTo(models.Role, { foreignKey: 'role_id' });
            User.hasMany(models.Course, { foreignKey: 'user_id' });
            User.belongsToMany(models.Course, { through: models.StudentCourse, foreignKey: 'user_id' });
            User.hasMany(models.QuizResult, { foreignKey: 'user_id' });
            User.hasMany(models.CourseResult, { foreignKey: 'user_id' });

            // New associations for learning analytics
            User.hasMany(models.StudentProgramProgress, { foreignKey: 'user_id', as: 'StudentProgramProgress' });
            User.hasMany(models.ProgramOutcomeTracking, { foreignKey: 'user_id', as: 'ProgramOutcomeTracking' });
            User.hasMany(models.LearningAnalytics, { foreignKey: 'created_by', as: 'CreatedAnalytics' });
        }

        // Phương thức để so s<PERSON>h mật khẩu
        async comparePassword(password) {
            return await bcrypt.compare(password, this.password);
        }

        // Gamification methods
        calculateLevel(totalPoints) {
            // Mỗi level cần 100 điểm, tăng dần
            return Math.floor(totalPoints / 100) + 1;
        }

        calculateExperienceInLevel(totalPoints) {
            return totalPoints % 100;
        }

        async addPoints(points, reason = 'quiz_completion') {
            const oldLevel = this.current_level;
            this.total_points += points;
            this.current_level = this.calculateLevel(this.total_points);
            this.experience_points = this.calculateExperienceInLevel(this.total_points);

            await this.save();

            // Trả về thông tin level up nếu có
            return {
                points_added: points,
                total_points: this.total_points,
                old_level: oldLevel,
                new_level: this.current_level,
                level_up: this.current_level > oldLevel,
                experience_points: this.experience_points,
                reason
            };
        }

        async updateGamificationStats(stats) {
            const currentStats = this.gamification_stats || {};
            this.gamification_stats = {
                ...currentStats,
                ...stats
            };
            await this.save();
            return this.gamification_stats;
        }
    }

    User.init(
        {
            user_id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true,
                autoIncrementIdentity: true
            },
            name: {
                type: DataTypes.STRING,
                allowNull: false,
            },
            email: {
                type: DataTypes.STRING,
                allowNull: false,
                unique: true,
            },
            password: {
                type: DataTypes.STRING,
                allowNull: false,
            },
            role_id: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: 'Roles',
                    key: 'role_id',
                },
            },
            // Gamification fields
            total_points: {
                type: DataTypes.INTEGER,
                allowNull: false,
                defaultValue: 0,
                comment: 'Tổng điểm tích lũy của người dùng'
            },
            current_level: {
                type: DataTypes.INTEGER,
                allowNull: false,
                defaultValue: 1,
                comment: 'Cấp độ hiện tại của người dùng'
            },
            experience_points: {
                type: DataTypes.INTEGER,
                allowNull: false,
                defaultValue: 0,
                comment: 'Điểm kinh nghiệm trong cấp độ hiện tại'
            },
            gamification_stats: {
                type: DataTypes.JSON,
                allowNull: false,
                defaultValue: {
                    total_quizzes_completed: 0,
                    total_correct_answers: 0,
                    total_questions_answered: 0,
                    average_response_time: 0,
                    best_streak: 0,
                    current_streak: 0,
                    speed_bonus_earned: 0,
                    perfect_scores: 0
                },
                comment: 'Thống kê gamification của người dùng'
            },
        },
        {
            sequelize,
            modelName: 'User',
            tableName: 'Users',
            timestamps: false, // Nếu không cần createdAt và updatedAt
            hooks: {
                // Hook mã hóa mật khẩu trước khi tạo người dùng
                beforeCreate: async (user) => {
                    if (user.password) {
                        const salt = await bcrypt.genSalt(10); // Tạo salt với độ phức tạp 10
                        user.password = await bcrypt.hash(user.password, salt); // Mã hóa mật khẩu
                    }
                },
                // Hook mã hóa mật khẩu trước khi cập nhật người dùng (nếu mật khẩu thay đổi)
                beforeUpdate: async (user) => {
                    if (user.password && user.changed('password')) {
                        const salt = await bcrypt.genSalt(10);
                        user.password = await bcrypt.hash(user.password, salt);
                    }
                },
            },
        }
    );

    return User;
};