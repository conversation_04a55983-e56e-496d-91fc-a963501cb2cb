const { LO, Chapter, Question, ChapterLO, Subject, UserQuestionHistory, Level, Quiz, Course, sequelize } = require('../models');
const { Op } = require('sequelize');
//const { sequelize } = require('../config/database');

exports.getAllLOs = async (req, res) => {
    try {
        const { page = 1, limit = 10 } = req.query;
        const offset = (page - 1) * limit;

        const los = await LO.findAndCountAll({
            limit: parseInt(limit),
            offset: parseInt(offset),
            include: [
                {
                    model: Chapter,
                    as: 'Chapters',
                    attributes: ['chapter_id', 'name'],
                    through: { attributes: [] },
                },
            ],
        });

        res.status(200).json({
            totalItems: los.count,
            totalPages: Math.ceil(los.count / limit),
            currentPage: parseInt(page),
            los: los.rows,
        });
    } catch (error) {
        console.error('Lỗi khi lấy danh sách LO:', error);
        res.status(500).json({ message: 'Lỗi khi lấy danh sách LO', error: error.message });
    }
};

exports.getLOById = async (req, res) => {
    try {
        const { id } = req.params;
        if (!id) {
            return res.status(400).json({ message: 'Thiếu ID của LO' });
        }

        const lo = await LO.findByPk(id, {
            include: [
                {
                    model: Chapter,
                    as: 'Chapters',
                    attributes: ['chapter_id', 'name'],
                    through: { attributes: [] }
                },
                {
                    model: Question,
                    attributes: ['question_id', 'question_text']
                }
            ],
        });

        if (!lo) {
            return res.status(404).json({ message: 'Không tìm thấy LO' });
        }

        res.status(200).json(lo);
    } catch (error) {
        console.error('Lỗi khi lấy thông tin LO:', error);
        res.status(500).json({ message: 'Lỗi server', error: error.message });
    }
};

exports.createLO = async (req, res) => {
    const transaction = await sequelize.transaction();
    try {
        const { name, description } = req.body;

        if (!name) {
            await transaction.rollback();
            return res.status(400).json({ message: 'Tên LO là bắt buộc' });
        }

        // Kiểm tra trùng lặp tên LO
        const existingLO = await LO.findOne({
            where: { name },
            transaction
        });

        if (existingLO) {
            await transaction.rollback();
            return res.status(400).json({ message: 'Tên LO đã tồn tại' });
        }

        const newLO = await LO.create({
            name,
            description: description || null
        }, { transaction });

        await transaction.commit();
        res.status(201).json(newLO);
    } catch (error) {
        await transaction.rollback();
        console.error('Lỗi khi tạo LO:', error);
        res.status(500).json({ message: 'Lỗi server', error: error.message });
    }
};

exports.updateLO = async (req, res) => {
    const transaction = await sequelize.transaction();
    try {
        const { id } = req.params;
        const { name, description } = req.body;

        if (!id) {
            await transaction.rollback();
            return res.status(400).json({ message: 'Thiếu ID của LO' });
        }

        const lo = await LO.findByPk(id, { transaction });
        if (!lo) {
            await transaction.rollback();
            return res.status(404).json({ message: 'Không tìm thấy LO' });
        }

        if (name) {
            // Kiểm tra trùng lặp tên LO
            const existingLO = await LO.findOne({
                where: {
                    name,
                    lo_id: { [Op.ne]: id }
                },
                transaction
            });

            if (existingLO) {
                await transaction.rollback();
                return res.status(400).json({ message: 'Tên LO đã tồn tại' });
            }
        }

        await lo.update({
            name: name || lo.name,
            description: description || lo.description
        }, { transaction });

        await transaction.commit();
        res.status(200).json(lo);
    } catch (error) {
        await transaction.rollback();
        console.error('Lỗi khi cập nhật LO:', error);
        res.status(500).json({ message: 'Lỗi server', error: error.message });
    }
};

exports.deleteLO = async (req, res) => {
    const transaction = await sequelize.transaction();
    try {
        const { id } = req.params;

        if (!id) {
            await transaction.rollback();
            return res.status(400).json({ message: 'Thiếu ID của LO' });
        }

        const lo = await LO.findByPk(id, { transaction });
        if (!lo) {
            await transaction.rollback();
            return res.status(404).json({ message: 'Không tìm thấy LO' });
        }

        // Kiểm tra xem LO có đang được sử dụng trong các mối quan hệ không
        const hasQuestions = await Question.findOne({
            where: { lo_id: id },
            transaction
        });

        const hasChapters = await ChapterLO.findOne({
            where: { lo_id: id },
            transaction
        });

        if (hasQuestions || hasChapters) {
            await transaction.rollback();
            return res.status(400).json({
                message: 'Không thể xóa LO vì đang được sử dụng trong câu hỏi hoặc chương'
            });
        }

        await lo.destroy({ transaction });
        await transaction.commit();
        res.status(200).json({ message: 'Xóa LO thành công' });
    } catch (error) {
        await transaction.rollback();
        console.error('Lỗi khi xóa LO:', error);
        res.status(500).json({ message: 'Lỗi server', error: error.message });
    }
};

exports.getLOsBySubject = async (req, res) => {
    try {
        const { subjectId } = req.params;
        const { page = 1, limit = 10 } = req.query;
        const offset = (page - 1) * limit;

        if (!subjectId) {
            return res.status(400).json({ message: 'Thiếu ID của môn học' });
        }

        // Kiểm tra subject có tồn tại không
        const subject = await Subject.findByPk(subjectId);
        if (!subject) {
            return res.status(404).json({ message: 'Không tìm thấy môn học' });
        }

        // 1. Lấy danh sách chapter theo subject
        const chapters = await Chapter.findAll({
            where: { subject_id: subjectId },
            attributes: ['chapter_id'],
        });

        if (!chapters.length) {
            return res.status(404).json({ message: 'Không tìm thấy chương nào thuộc môn học này' });
        }

        const chapterIds = chapters.map(ch => ch.chapter_id);

        // 2. Lấy danh sách ChapterLO theo các chapter_id
        const chapterLOs = await ChapterLO.findAll({
            where: { chapter_id: { [Op.in]: chapterIds } },
            attributes: ['lo_id'],
        });

        if (!chapterLOs.length) {
            return res.status(404).json({ message: 'Không tìm thấy LO nào liên kết với môn học này' });
        }

        const loIds = [...new Set(chapterLOs.map(clo => clo.lo_id))];

        // 3. Lấy danh sách LO với pagination
        const { count, rows: los } = await LO.findAndCountAll({
            where: { lo_id: { [Op.in]: loIds } },
            include: [
                {
                    model: Chapter,
                    as: 'Chapters',
                    attributes: ['chapter_id', 'name'],
                    through: { attributes: [] },
                },
            ],
            limit: parseInt(limit),
            offset: parseInt(offset),
        });

        return res.status(200).json({
            totalItems: count,
            totalPages: Math.ceil(count / limit),
            currentPage: parseInt(page),
            los,
        });
    } catch (error) {
        console.error('Lỗi khi lấy LO theo Subject:', error);
        return res.status(500).json({ message: 'Lỗi server', error: error.message });
    }
};

// =====================================================
// NEW ADMIN FUNCTIONS FOR STATISTICS AND ANALYTICS
// =====================================================

// Lấy thống kê tổng quan về LO
exports.getLOStatistics = async (req, res) => {
    try {
        const { program_id, subject_id, time_period } = req.query;

        // Build where clause
        let whereClause = {};
        if (program_id) {
            // Get subjects in program first
            const subjects = await Subject.findAll({
                include: [{
                    model: Course,
                    where: { program_id }
                }],
                attributes: ['subject_id']
            });
            const subjectIds = subjects.map(s => s.subject_id);

            // Get chapters in those subjects
            const chapters = await Chapter.findAll({
                where: { subject_id: { [Op.in]: subjectIds } },
                attributes: ['chapter_id']
            });
            const chapterIds = chapters.map(c => c.chapter_id);

            // Filter LOs by chapters
            if (chapterIds.length > 0) {
                const chapterLOs = await ChapterLO.findAll({
                    where: { chapter_id: { [Op.in]: chapterIds } },
                    attributes: ['lo_id']
                });
                const loIds = chapterLOs.map(cl => cl.lo_id);
                whereClause.lo_id = { [Op.in]: loIds };
            }
        }

        // Get total LOs
        const totalLOs = await LO.count({ where: whereClause });

        // Get LOs with questions
        const losWithQuestions = await LO.count({
            where: whereClause,
            include: [{
                model: Question,
                required: true
            }]
        });

        // Get question statistics
        const questionStats = await Question.findAll({
            include: [{
                model: LO,
                where: whereClause,
                attributes: ['lo_id', 'name']
            }, {
                model: Level,
                as: 'Level',
                attributes: ['name']
            }],
            attributes: ['question_id', 'lo_id']
        });

        // Process statistics
        const loQuestionCount = {};
        const difficultyDistribution = { easy: 0, medium: 0, hard: 0 };

        questionStats.forEach(question => {
            const lo_id = question.lo_id;
            loQuestionCount[lo_id] = (loQuestionCount[lo_id] || 0) + 1;

            const difficulty = question.Level?.name?.toLowerCase() || 'medium';
            if (difficultyDistribution[difficulty] !== undefined) {
                difficultyDistribution[difficulty]++;
            }
        });

        res.json({
            overview: {
                total_los: totalLOs,
                los_with_questions: losWithQuestions,
                los_without_questions: totalLOs - losWithQuestions,
                total_questions: questionStats.length,
                average_questions_per_lo: totalLOs > 0 ? (questionStats.length / totalLOs).toFixed(2) : 0
            },
            difficulty_distribution: difficultyDistribution,
            lo_question_distribution: loQuestionCount,
            generated_at: new Date()
        });

    } catch (error) {
        console.error('Error getting LO statistics:', error);
        res.status(500).json({ message: 'Lỗi khi lấy thống kê LO', error: error.message });
    }
};

// Phân tích hiệu suất LO
exports.getLOPerformanceAnalysis = async (req, res) => {
    try {
        const { program_id, subject_id, time_period } = req.query;
        const { start_date, end_date } = time_period || {};

        // Build where clause for UserQuestionHistory
        let historyWhereClause = {};
        if (start_date && end_date) {
            historyWhereClause.attempt_date = {
                [Op.between]: [start_date, end_date]
            };
        }

        // Get performance data
        const performanceData = await UserQuestionHistory.findAll({
            where: historyWhereClause,
            include: [{
                model: Question,
                as: 'Question',
                include: [{
                    model: LO,
                    as: 'LO',
                    attributes: ['lo_id', 'name', 'description']
                }, {
                    model: Level,
                    as: 'Level',
                    attributes: ['name']
                }]
            }],
            attributes: ['user_id', 'question_id', 'is_correct', 'time_spent', 'attempt_date']
        });

        // Process performance by LO
        const loPerformance = {};

        performanceData.forEach(history => {
            const lo = history.Question?.LO;
            if (!lo) return;

            const lo_id = lo.lo_id;
            if (!loPerformance[lo_id]) {
                loPerformance[lo_id] = {
                    lo_info: {
                        lo_id: lo.lo_id,
                        name: lo.name,
                        description: lo.description
                    },
                    total_attempts: 0,
                    correct_attempts: 0,
                    unique_students: new Set(),
                    total_time: 0,
                    difficulty_breakdown: { easy: 0, medium: 0, hard: 0 }
                };
            }

            const data = loPerformance[lo_id];
            data.total_attempts++;
            data.unique_students.add(history.user_id);

            if (history.is_correct) {
                data.correct_attempts++;
            }

            if (history.time_spent) {
                data.total_time += history.time_spent;
            }

            // Track difficulty
            const difficulty = history.Question.Level?.name?.toLowerCase() || 'medium';
            if (data.difficulty_breakdown[difficulty] !== undefined) {
                data.difficulty_breakdown[difficulty]++;
            }
        });

        // Calculate final metrics
        const results = Object.keys(loPerformance).map(lo_id => {
            const data = loPerformance[lo_id];
            return {
                ...data.lo_info,
                performance_metrics: {
                    total_attempts: data.total_attempts,
                    unique_students: data.unique_students.size,
                    accuracy_rate: data.total_attempts > 0 ?
                        ((data.correct_attempts / data.total_attempts) * 100).toFixed(2) : 0,
                    average_time: data.total_attempts > 0 ?
                        Math.round(data.total_time / data.total_attempts) : 0,
                    difficulty_breakdown: data.difficulty_breakdown
                }
            };
        });

        // Sort by accuracy rate
        results.sort((a, b) => b.performance_metrics.accuracy_rate - a.performance_metrics.accuracy_rate);

        res.json({
            total_los_analyzed: results.length,
            performance_analysis: results,
            summary: {
                best_performing_lo: results[0] || null,
                worst_performing_lo: results[results.length - 1] || null,
                average_accuracy: results.length > 0 ?
                    (results.reduce((sum, lo) => sum + parseFloat(lo.performance_metrics.accuracy_rate), 0) / results.length).toFixed(2) : 0
            },
            generated_at: new Date()
        });

    } catch (error) {
        console.error('Error getting LO performance analysis:', error);
        res.status(500).json({ message: 'Lỗi khi phân tích hiệu suất LO', error: error.message });
    }
};

// Thống kê câu hỏi theo LO
exports.getLOQuestionStatistics = async (req, res) => {
    try {
        const { id } = req.params;

        const lo = await LO.findByPk(id, {
            include: [{
                model: Question,
                include: [{
                    model: Level,
                    as: 'Level',
                    attributes: ['name']
                }, {
                    model: UserQuestionHistory,
                    attributes: ['user_id', 'is_correct', 'time_spent']
                }]
            }]
        });

        if (!lo) {
            return res.status(404).json({ message: 'LO không tồn tại' });
        }

        const questionStats = lo.Questions.map(question => {
            const histories = question.UserQuestionHistories || [];
            const totalAttempts = histories.length;
            const correctAttempts = histories.filter(h => h.is_correct).length;
            const uniqueStudents = new Set(histories.map(h => h.user_id)).size;
            const avgTime = histories.length > 0 ?
                histories.reduce((sum, h) => sum + (h.time_spent || 0), 0) / histories.length : 0;

            return {
                question_id: question.question_id,
                question_text: question.question_text,
                difficulty: question.Level?.name || 'Unknown',
                statistics: {
                    total_attempts: totalAttempts,
                    correct_attempts: correctAttempts,
                    accuracy_rate: totalAttempts > 0 ? ((correctAttempts / totalAttempts) * 100).toFixed(2) : 0,
                    unique_students: uniqueStudents,
                    average_time: Math.round(avgTime)
                }
            };
        });

        res.json({
            lo_info: {
                lo_id: lo.lo_id,
                name: lo.name,
                description: lo.description
            },
            total_questions: questionStats.length,
            question_statistics: questionStats,
            summary: {
                total_attempts: questionStats.reduce((sum, q) => sum + q.statistics.total_attempts, 0),
                average_accuracy: questionStats.length > 0 ?
                    (questionStats.reduce((sum, q) => sum + parseFloat(q.statistics.accuracy_rate), 0) / questionStats.length).toFixed(2) : 0,
                most_difficult_question: questionStats.sort((a, b) =>
                    parseFloat(a.statistics.accuracy_rate) - parseFloat(b.statistics.accuracy_rate))[0] || null
            },
            generated_at: new Date()
        });

    } catch (error) {
        console.error('Error getting LO question statistics:', error);
        res.status(500).json({ message: 'Lỗi khi lấy thống kê câu hỏi LO', error: error.message });
    }
};

// Bulk create LOs
exports.bulkCreateLOs = async (req, res) => {
    const transaction = await sequelize.transaction();
    try {
        const { los } = req.body; // Array of LO objects

        if (!Array.isArray(los) || los.length === 0) {
            await transaction.rollback();
            return res.status(400).json({ message: 'Danh sách LO không hợp lệ' });
        }

        // Validate each LO
        for (const lo of los) {
            if (!lo.name) {
                await transaction.rollback();
                return res.status(400).json({ message: 'Tên LO là bắt buộc cho tất cả LO' });
            }
        }

        // Check for duplicate names
        const names = los.map(lo => lo.name);
        const existingLOs = await LO.findAll({
            where: { name: { [Op.in]: names } },
            transaction
        });

        if (existingLOs.length > 0) {
            await transaction.rollback();
            return res.status(400).json({
                message: 'Một số tên LO đã tồn tại',
                duplicates: existingLOs.map(lo => lo.name)
            });
        }

        // Create LOs
        const createdLOs = await LO.bulkCreate(los, { transaction });
        await transaction.commit();

        res.status(201).json({
            message: `Đã tạo thành công ${createdLOs.length} LO`,
            created_los: createdLOs
        });

    } catch (error) {
        await transaction.rollback();
        console.error('Error bulk creating LOs:', error);
        res.status(500).json({ message: 'Lỗi khi tạo hàng loạt LO', error: error.message });
    }
};

// Bulk update LOs
exports.bulkUpdateLOs = async (req, res) => {
    const transaction = await sequelize.transaction();
    try {
        const { updates } = req.body; // Array of {lo_id, name, description}

        if (!Array.isArray(updates) || updates.length === 0) {
            await transaction.rollback();
            return res.status(400).json({ message: 'Danh sách cập nhật không hợp lệ' });
        }

        const results = [];
        for (const update of updates) {
            const { lo_id, name, description } = update;

            if (!lo_id) {
                continue; // Skip invalid entries
            }

            const lo = await LO.findByPk(lo_id, { transaction });
            if (lo) {
                await lo.update({
                    name: name || lo.name,
                    description: description !== undefined ? description : lo.description
                }, { transaction });
                results.push(lo);
            }
        }

        await transaction.commit();

        res.json({
            message: `Đã cập nhật thành công ${results.length} LO`,
            updated_los: results
        });

    } catch (error) {
        await transaction.rollback();
        console.error('Error bulk updating LOs:', error);
        res.status(500).json({ message: 'Lỗi khi cập nhật hàng loạt LO', error: error.message });
    }
};

// Bulk delete LOs
exports.bulkDeleteLOs = async (req, res) => {
    const transaction = await sequelize.transaction();
    try {
        const { lo_ids } = req.body; // Array of LO IDs

        if (!Array.isArray(lo_ids) || lo_ids.length === 0) {
            await transaction.rollback();
            return res.status(400).json({ message: 'Danh sách ID LO không hợp lệ' });
        }

        // Check if any LO has associated questions
        const losWithQuestions = await LO.findAll({
            where: { lo_id: { [Op.in]: lo_ids } },
            include: [{
                model: Question,
                required: true,
                attributes: ['question_id']
            }],
            transaction
        });

        if (losWithQuestions.length > 0) {
            await transaction.rollback();
            return res.status(400).json({
                message: 'Không thể xóa LO có câu hỏi liên quan',
                los_with_questions: losWithQuestions.map(lo => ({
                    lo_id: lo.lo_id,
                    name: lo.name,
                    question_count: lo.Questions.length
                }))
            });
        }

        // Delete LOs
        const deletedCount = await LO.destroy({
            where: { lo_id: { [Op.in]: lo_ids } },
            transaction
        });

        await transaction.commit();

        res.json({
            message: `Đã xóa thành công ${deletedCount} LO`,
            deleted_count: deletedCount
        });

    } catch (error) {
        await transaction.rollback();
        console.error('Error bulk deleting LOs:', error);
        res.status(500).json({ message: 'Lỗi khi xóa hàng loạt LO', error: error.message });
    }
};