/**
 * Quick test for Advanced Analytics APIs
 * Tests basic endpoints to verify the column name fix
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function quickTest() {
    console.log('🚀 Quick Test for Analytics APIs\n');

    try {
        // Test 1: Sample Data (no authentication needed for this endpoint)
        console.log('📡 Testing Sample Data endpoint...');
        const sampleResponse = await axios.get(`${BASE_URL}/api/advanced-analytics/test/sample-data`);

        if (sampleResponse.data.success) {
            console.log('✅ Sample Data endpoint works!');
            console.log('   Response keys:', Object.keys(sampleResponse.data.data));
        } else {
            console.log('❌ Sample Data endpoint failed');
        }

    } catch (error) {
        console.log('❌ Error testing Sample Data:', error.response?.data?.message || error.message);
    }

    try {
        // Test 2: Login
        console.log('\n📡 Testing Login...');
        const loginResponse = await axios.post(`${BASE_URL}/api/auth/login`, {
            email: '<EMAIL>',  // Update with your admin credentials
            password: 'admin123'
        });

        if (loginResponse.data.success && loginResponse.data.token) {
            const token = loginResponse.data.token;
            console.log('✅ Login successful!');

            // Test 3: Test Endpoints with authentication
            console.log('\n📡 Testing Endpoints Validation...');
            const testResponse = await axios.get(`${BASE_URL}/api/advanced-analytics/test/endpoints?user_id=1`, {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (testResponse.data.success) {
                console.log('✅ Test Endpoints works!');
                console.log('   Test results:', testResponse.data.data.overall_status);
            } else {
                console.log('❌ Test Endpoints failed');
            }

            // Test 4: LO-Chapter Relationship (this will test the new approach)
            console.log('\n📡 Testing LO-Chapter Relationship...');
            const relationshipResponse = await axios.get(`${BASE_URL}/api/advanced-analytics/test/lo-chapter-relationship`, {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (relationshipResponse.data.success) {
                console.log('✅ LO-Chapter Relationship works!');
                const data = relationshipResponse.data.data;
                console.log(`   Found ${data.total_los} LOs, ${data.total_chapters_mapped} chapters mapped`);
                console.log(`   Question history records: ${data.total_question_history}`);

                if (data.sample_processed_data && data.sample_processed_data.length > 0) {
                    console.log('   Sample data:', data.sample_processed_data[0]);
                }
            } else {
                console.log('❌ LO-Chapter Relationship failed');
            }

            // Test 5: Student Score Analysis (this will test the column name fix)
            console.log('\n📡 Testing Student Score Analysis...');
            const scoreResponse = await axios.get(`${BASE_URL}/api/advanced-analytics/student/score-analysis?user_id=1&time_period=3m`, {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (scoreResponse.data.success) {
                console.log('✅ Student Score Analysis works!');
                if (scoreResponse.data.data.message) {
                    console.log('   Message:', scoreResponse.data.data.message);
                } else {
                    console.log('   Analysis completed successfully');
                }
            } else {
                console.log('❌ Student Score Analysis failed');
            }

        } else {
            console.log('❌ Login failed - cannot test authenticated endpoints');
        }

    } catch (error) {
        const errorMsg = error.response?.data?.message || error.message;
        console.log('❌ Error:', errorMsg);

        // Check if it's the column error we're trying to fix
        if (errorMsg.includes('response_time') || errorMsg.includes('column') || errorMsg.includes('does not exist')) {
            console.log('🔍 This looks like a database column issue. Check:');
            console.log('   1. Database connection is working');
            console.log('   2. UserQuestionHistory table exists');
            console.log('   3. Column names match the model definition');
        }
    }

    console.log('\n✨ Quick test completed!');
    console.log('\n💡 Next steps:');
    console.log('1. If LO-Chapter relationship works, the new approach is correct');
    console.log('2. If you see data, the analytics functions should work');
    console.log('3. Test with Postman: GET /api/advanced-analytics/test/lo-chapter-relationship');
    console.log('4. Then test other analytics endpoints');

    console.log('\n🔧 If you see errors:');
    console.log('1. Database schema mismatch');
    console.log('2. Missing data in UserQuestionHistory table');
    console.log('3. ChapterLO table not populated with data');
}

// Run the quick test
if (require.main === module) {
    quickTest().catch(error => {
        console.error('💥 Quick test error:', error.message);
        process.exit(1);
    });
}

module.exports = { quickTest };
