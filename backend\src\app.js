// src/app.js
const express = require('express');
const http = require('http');
const cors = require('cors');
const dotenv = require('dotenv');
const socket = require('./socket');
const { setGlobalIO } = require('./controllers/quizController');

dotenv.config();

const app = express();
const server = http.createServer(app);

// Cấu hình CORS
const corsOptions = {
    origin: [
        'https://**************',
        'https://stardust.id.vn',
        'http://localhost:3000',
        'http://127.0.0.1:3000',
        'http://frontend:3000',
        'http://localhost:3001',
        'http://localhost:3002',
        'http://localhost:3003',
        'http://localhost:3004',
        'http://localhost:3005',
        'http://localhost:3006',
        'http://localhost:3007',
        'http://localhost:3008',
        'http://localhost:3009',
        'http://localhost:3010',
        'http://127.0.0.1:3001',
        'http://127.0.0.1:3002',
        'http://127.0.0.1:3003',
        'http://127.0.0.1:3004',
        'http://127.0.0.1:3005',
        'http://127.0.0.1:3006',
        'http://127.0.0.1:3007',
        'http://127.0.0.1:3008',
        'http://127.0.0.1:3009',
        'http://127.0.0.1:3010'
    ],
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization'],
    credentials: true
};

// Middleware
app.use(cors(corsOptions));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Khởi tạo socket.io với server
const io = socket.init(server);

// Đảm bảo quizRealtimeService được khởi tạo đúng
setGlobalIO(io);

// Thêm socket.io vào app để có thể truy cập từ các controller
app.set('io', io);

// Import các routes
const questionRoutes = require('./routes/questionRoutes');
const programRoutes = require('./routes/programRoutes');
const roleRoutes = require('./routes/roleRoutes');
const userRoutes = require('./routes/userRoutes');
const poRoutes = require('./routes/poRoutes');
const ploRoutes = require('./routes/ploRoutes');
const posPlosRoutes = require('./routes/posPlosRoutes');
const courseRoutes = require('./routes/courseRoutes');
const studentCourseRoutes = require('./routes/studentCourseRoutes');
const typeSubjectRoutes = require('./routes/typeSubjectRoutes');
const groupRoutes = require('./routes/groupRoutes');
const typeOfKnowledgeRoutes = require('./routes/typeOfKnowledgeRoutes');
const subjectRoutes = require('./routes/subjectRoutes');
const loRoutes = require('./routes/loRoutes');
const questionTypeRoutes = require('./routes/questionTypeRoutes');
const levelRoutes = require('./routes/levelRoutes');
const quizRoutes = require('./routes/quizRoutes');
const quizQuestionRoutes = require('./routes/quizQuestionRoutes');
const answerRoutes = require('./routes/answerRoutes');
const quizResultRoutes = require('./routes/quizResultRoutes');
const courseResultRoutes = require('./routes/courseResultRoutes');
const tienQuyetRoutes = require('./routes/tienQuyetRoutes');
const learningAnalyticsRoutes = require('./routes/learningAnalyticsRoutes');
const reportRoutes = require('./routes/reportRoutes');
const chapterRoutes = require('./routes/chapterRoutes');
const statisticsRoutes = require('./routes/statisticsRoutes');
const gamificationRoutes = require('./routes/gamificationRoutes');
const progressRoutes = require('./routes/progressRoutes');
const adaptiveQuizRoutes = require('./routes/adaptiveQuizRoutes');

// Setup routes
app.use('/api/questions', questionRoutes);
app.use('/api/programs', programRoutes);
app.use('/api/roles', roleRoutes);
app.use('/api/users', userRoutes);
app.use('/api/pos', poRoutes);
app.use('/api/plos', ploRoutes);
app.use('/api/pos-plos', posPlosRoutes);
app.use('/api/courses', courseRoutes);
app.use('/api/student-courses', studentCourseRoutes);
app.use('/api/type-subjects', typeSubjectRoutes);
app.use('/api/groups', groupRoutes);
app.use('/api/type-of-knowledges', typeOfKnowledgeRoutes);
app.use('/api/subjects', subjectRoutes);
app.use('/api/los', loRoutes);
app.use('/api/question-types', questionTypeRoutes);
app.use('/api/levels', levelRoutes);
app.use('/api/quizzes', quizRoutes);
app.use('/api/quiz-questions', quizQuestionRoutes);
app.use('/api/answers', answerRoutes);
app.use('/api/quiz-results', quizResultRoutes);
app.use('/api/course-results', courseResultRoutes);
app.use('/api/tienquyets', tienQuyetRoutes);
app.use('/api/learning-analytics', learningAnalyticsRoutes);
app.use('/api/reports', reportRoutes);
app.use('/api/chapters', chapterRoutes);
app.use('/api/statistics', statisticsRoutes);
app.use('/api/gamification', gamificationRoutes);
app.use('/api/progress', progressRoutes);
app.use('/api/quizzes/adaptive', adaptiveQuizRoutes);

// Test Socket.IO route
app.get('/api/test-socket', (req, res) => {
    const io = req.app.get('io');
    io.emit('testEvent', { message: 'Test message from server' });
    res.json({ message: 'Test event sent' });
});

// Xử lý lỗi 404
app.use((req, res, next) => {
    res.status(404).json({ error: 'Route không tồn tại' });
});

// Xử lý lỗi server
app.use((err, req, res, next) => {
    console.error('Server Error:', err);
    res.status(500).json({ error: 'Lỗi server', details: err.message });
});

module.exports = server;
