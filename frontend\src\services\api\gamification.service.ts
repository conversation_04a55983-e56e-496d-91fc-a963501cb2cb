/* eslint-disable @typescript-eslint/no-explicit-any */
import api from "./client";

export interface UserGamificationInfo {
  user_id: number;
  name: string;
  total_points: number;
  current_level: number;
  experience_points: number;
  experience_to_next_level: number;
  stats: {
    total_quizzes_completed: number;
    total_correct_answers: number;
    total_questions_answered: number;
    best_streak: number;
    current_streak: number;
    speed_bonus_earned: number;
    perfect_scores: number;
    average_response_time: number;
  };
}

export interface LeaderboardEntry {
  position: number;
  user_id: number;
  name: string;
  total_points: number;
  current_level: number;
  stats: UserGamificationInfo["stats"];
}

export interface GamificationStats {
  overview: {
    total_users: number;
    active_users: number;
    engagement_rate: string;
  };
  top_performers: LeaderboardEntry[];
  level_distribution: Array<{
    current_level: number;
    count: number;
  }>;
}

export interface AddPointsRequest {
  user_id: number;
  points: number;
  reason?: string;
}

class GamificationService {
  /**
   * Lấy thông tin gamification của user hiện tại
   */
  async getCurrentUserGamification(): Promise<UserGamificationInfo> {
    const response = await api.get("/gamification/me");
    return response.data.data;
  }

  /**
   * Lấy bảng xếp hạng
   */
  async getLeaderboard(
    limit: number = 10,
    timeframe: string = "all"
  ): Promise<LeaderboardEntry[]> {
    const response = await api.get("/gamification/leaderboard", {
      params: { limit, timeframe },
    });
    return response.data.data.leaderboard;
  }

  /**
   * Lấy thông tin gamification của user khác (Admin/Teacher only)
   */
  async getUserGamificationById(userId: number): Promise<UserGamificationInfo> {
    const response = await api.get(`/gamification/user/${userId}`);
    return response.data.data;
  }

  /**
   * Thêm điểm thủ công (Admin only)
   */
  async addPointsManually(data: AddPointsRequest): Promise<any> {
    const response = await api.post("/gamification/add-points", data);
    return response.data.data;
  }

  /**
   * Lấy thống kê tổng quan gamification (Admin only)
   */
  async getGamificationStats(): Promise<GamificationStats> {
    const response = await api.get("/gamification/stats");
    return response.data.data;
  }

  /**
   * Tính toán level từ total points
   */
  calculateLevel(totalPoints: number): number {
    return Math.floor(totalPoints / 100) + 1;
  }

  /**
   * Tính toán experience points trong level hiện tại
   */
  calculateExperienceInLevel(totalPoints: number): number {
    return totalPoints % 100;
  }

  /**
   * Tính toán phần trăm progress trong level
   */
  calculateLevelProgress(experiencePoints: number): number {
    return (experiencePoints / 100) * 100;
  }

  /**
   * Lấy tên level dựa trên số level
   */
  getLevelName(level: number): string {
    const levelNames = [
      "Người mới bắt đầu",
      "Học viên tập sự",
      "Học viên",
      "Học viên giỏi",
      "Chuyên gia nhỏ",
      "Chuyên gia",
      "Thạc sĩ",
      "Tiến sĩ",
      "Giáo sư",
      "Huyền thoại",
    ];

    if (level <= 10) {
      return levelNames[level - 1] || "Huyền thoại";
    }

    return `Huyền thoại cấp ${level - 10}`;
  }

  /**
   * Lấy màu sắc cho level
   */
  getLevelColor(level: number): string {
    const colors = [
      "#9CA3AF", // Gray - Người mới
      "#10B981", // Green - Học viên tập sự
      "#3B82F6", // Blue - Học viên
      "#8B5CF6", // Purple - Học viên giỏi
      "#F59E0B", // Yellow - Chuyên gia nhỏ
      "#EF4444", // Red - Chuyên gia
      "#EC4899", // Pink - Thạc sĩ
      "#6366F1", // Indigo - Tiến sĩ
      "#F97316", // Orange - Giáo sư
      "#FBBF24", // Gold - Huyền thoại
    ];

    if (level <= 10) {
      return colors[level - 1] || "#FBBF24";
    }

    return "#FBBF24"; // Gold cho tất cả huyền thoại
  }

  /**
   * Format điểm số với dấu phẩy
   */
  formatPoints(points: number): string {
    return points.toLocaleString("vi-VN");
  }

  /**
   * Format thời gian response
   */
  formatResponseTime(milliseconds: number): string {
    if (milliseconds < 1000) {
      return `${milliseconds}ms`;
    }
    return `${(milliseconds / 1000).toFixed(1)}s`;
  }

  /**
   * Tính accuracy rate
   */
  calculateAccuracyRate(correct: number, total: number): number {
    if (total === 0) return 0;
    return Math.round((correct / total) * 100);
  }
}

export const gamificationService = new GamificationService();
export default gamificationService;
