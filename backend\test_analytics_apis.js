/**
 * Simple test script for Advanced Analytics APIs
 * Run with: node test_analytics_apis.js
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000';
let authToken = '';

// Test configuration
const TEST_CONFIG = {
    admin_email: '<EMAIL>',
    admin_password: 'admin123',
    test_user_id: 1,
    test_subject_id: 1,
    test_program_id: 1
};

/**
 * <PERSON>gin and get authentication token
 */
async function login() {
    try {
        console.log('🔐 Logging in...');
        const response = await axios.post(`${BASE_URL}/api/auth/login`, {
            email: TEST_CONFIG.admin_email,
            password: TEST_CONFIG.admin_password
        });

        if (response.data.success && response.data.token) {
            authToken = response.data.token;
            console.log('✅ Login successful');
            return true;
        } else {
            console.log('❌ Login failed:', response.data.message);
            return false;
        }
    } catch (error) {
        console.log('❌ Login error:', error.response?.data?.message || error.message);
        return false;
    }
}

/**
 * Make authenticated API request
 */
async function makeRequest(endpoint, params = {}) {
    try {
        const url = `${BASE_URL}/api/advanced-analytics${endpoint}`;
        const config = {
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            },
            params
        };

        console.log(`📡 Testing: ${endpoint}`);
        const response = await axios.get(url, config);
        
        if (response.data.success) {
            console.log(`✅ ${endpoint} - Success`);
            return { success: true, data: response.data };
        } else {
            console.log(`❌ ${endpoint} - Failed:`, response.data.message);
            return { success: false, error: response.data.message };
        }
    } catch (error) {
        const errorMsg = error.response?.data?.message || error.message;
        console.log(`❌ ${endpoint} - Error:`, errorMsg);
        return { success: false, error: errorMsg };
    }
}

/**
 * Test all analytics endpoints
 */
async function testAnalyticsEndpoints() {
    console.log('\n🧪 Testing Analytics Endpoints...\n');

    const tests = [
        // Basic endpoints that don't require user_id
        {
            name: 'Sample Data',
            endpoint: '/test/sample-data',
            params: {}
        },
        {
            name: 'Test Endpoints Validation',
            endpoint: '/test/endpoints',
            params: { user_id: TEST_CONFIG.test_user_id }
        },
        
        // Performance Analytics
        {
            name: 'Performance Time Series',
            endpoint: '/performance/time-series',
            params: { 
                time_period: '30d', 
                aggregation: 'daily',
                subject_id: TEST_CONFIG.test_subject_id
            }
        },
        {
            name: 'Score Distribution',
            endpoint: '/performance/score-distribution',
            params: { 
                bins: 10,
                subject_id: TEST_CONFIG.test_subject_id
            }
        },
        {
            name: 'Learning Outcomes Comparison',
            endpoint: '/performance/learning-outcomes',
            params: { 
                comparison_type: 'average',
                subject_id: TEST_CONFIG.test_subject_id
            }
        },
        {
            name: 'Completion Funnel',
            endpoint: '/performance/completion-funnel',
            params: { 
                time_period: '30d',
                subject_id: TEST_CONFIG.test_subject_id
            }
        },

        // Difficulty Analysis
        {
            name: 'Difficulty Heatmap',
            endpoint: '/difficulty/heatmap',
            params: { 
                time_period: '30d',
                subject_id: TEST_CONFIG.test_subject_id
            }
        },
        {
            name: 'Time Score Correlation',
            endpoint: '/difficulty/time-score-correlation',
            params: { 
                time_period: '30d',
                subject_id: TEST_CONFIG.test_subject_id
            }
        },

        // Student Behavior Analytics
        {
            name: 'Activity Timeline',
            endpoint: '/behavior/activity-timeline',
            params: { 
                user_id: TEST_CONFIG.test_user_id,
                time_period: '30d',
                granularity: 'daily'
            }
        },
        {
            name: 'Learning Flow',
            endpoint: '/behavior/learning-flow',
            params: { 
                user_id: TEST_CONFIG.test_user_id,
                time_period: '30d'
            }
        },

        // Student Score Analysis
        {
            name: 'Student Score Analysis',
            endpoint: '/student/score-analysis',
            params: { 
                user_id: TEST_CONFIG.test_user_id,
                time_period: '3m',
                include_comparison: true
            }
        },
        {
            name: 'Learning Outcome Mastery',
            endpoint: '/student/learning-outcome-mastery',
            params: { 
                user_id: TEST_CONFIG.test_user_id,
                mastery_threshold: 0.7
            }
        },
        {
            name: 'Improvement Suggestions',
            endpoint: '/student/improvement-suggestions',
            params: { 
                user_id: TEST_CONFIG.test_user_id,
                suggestion_depth: 'detailed'
            }
        },

        // Predictive Analytics
        {
            name: 'Completion Probability',
            endpoint: '/predictive/completion-probability',
            params: { 
                user_id: TEST_CONFIG.test_user_id,
                prediction_horizon: '3m'
            }
        },
        {
            name: 'Risk Assessment',
            endpoint: '/predictive/risk-assessment',
            params: { 
                subject_id: TEST_CONFIG.test_subject_id,
                risk_threshold: 0.3
            }
        }
    ];

    const results = {
        total: tests.length,
        passed: 0,
        failed: 0,
        errors: []
    };

    for (const test of tests) {
        const result = await makeRequest(test.endpoint, test.params);
        
        if (result.success) {
            results.passed++;
        } else {
            results.failed++;
            results.errors.push({
                test: test.name,
                endpoint: test.endpoint,
                error: result.error
            });
        }

        // Small delay between requests
        await new Promise(resolve => setTimeout(resolve, 100));
    }

    return results;
}

/**
 * Main test function
 */
async function runTests() {
    console.log('🚀 Starting Advanced Analytics API Tests\n');
    console.log('Configuration:');
    console.log(`- Base URL: ${BASE_URL}`);
    console.log(`- Test User ID: ${TEST_CONFIG.test_user_id}`);
    console.log(`- Test Subject ID: ${TEST_CONFIG.test_subject_id}`);
    console.log('=' .repeat(50));

    // Step 1: Login
    const loginSuccess = await login();
    if (!loginSuccess) {
        console.log('\n❌ Cannot proceed without authentication');
        process.exit(1);
    }

    // Step 2: Test all endpoints
    const results = await testAnalyticsEndpoints();

    // Step 3: Display results
    console.log('\n' + '='.repeat(50));
    console.log('📊 TEST RESULTS SUMMARY');
    console.log('='.repeat(50));
    console.log(`Total Tests: ${results.total}`);
    console.log(`✅ Passed: ${results.passed}`);
    console.log(`❌ Failed: ${results.failed}`);
    console.log(`Success Rate: ${((results.passed / results.total) * 100).toFixed(2)}%`);

    if (results.errors.length > 0) {
        console.log('\n❌ FAILED TESTS:');
        results.errors.forEach((error, index) => {
            console.log(`${index + 1}. ${error.test}`);
            console.log(`   Endpoint: ${error.endpoint}`);
            console.log(`   Error: ${error.error}`);
            console.log('');
        });
    }

    if (results.passed === results.total) {
        console.log('\n🎉 All tests passed! Analytics APIs are working correctly.');
    } else if (results.passed > results.failed) {
        console.log('\n⚠️  Most tests passed, but some issues need attention.');
    } else {
        console.log('\n🚨 Many tests failed. Please check the implementation.');
    }

    console.log('\n✨ Test completed!');
}

// Run the tests
if (require.main === module) {
    runTests().catch(error => {
        console.error('💥 Test runner error:', error.message);
        process.exit(1);
    });
}

module.exports = { runTests, makeRequest, login };
