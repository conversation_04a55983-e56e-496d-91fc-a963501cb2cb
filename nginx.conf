events {}

http {
  # <PERSON><PERSON><PERSON><PERSON> hướng HTTP sang HTTPS
  server {
      listen 80;
      server_name stardust.id.vn www.stardust.id.vn;
      return 301 https://$host$request_uri;
  }

  # Server block cho HTTPS
  server {
      listen 443 ssl;
      server_name stardust.id.vn www.stardust.id.vn;

      # Đ<PERSON>ờng dẫn đến chứng chỉ SSL từ Let's Encrypt
      ssl_certificate /etc/letsencrypt/live/stardust.id.vn/fullchain.pem;
      ssl_certificate_key /etc/letsencrypt/live/stardust.id.vn/privkey.pem;

      # Tối ưu bảo mật SSL
      ssl_protocols TLSv1.2 TLSv1.3;
      ssl_prefer_server_ciphers on;
      ssl_ciphers EECDH+AESGCM:EDH+AESGCM:AES256+EECDH:AES256+EDH;

      # Bật HSTS để buộc HTTPS
      add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

      # <PERSON><PERSON><PERSON> tuyến cho frontend Next.js
      location / {
          proxy_pass http://frontend:3000;
          proxy_set_header Host $host;
          proxy_set_header X-Real-IP $remote_addr;
          proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
          proxy_set_header X-Forwarded-Proto $scheme;
      }

      # Định tuyến cho backend Node.js (API)
      location /api/ {
          proxy_pass http://backend:8888/api/;
          proxy_set_header Host $host;
          proxy_set_header X-Real-IP $remote_addr;
          proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
          proxy_set_header X-Forwarded-Proto $scheme;
      }

      # Định tuyến cho WebSocket (WSS)
      location /socket.io/ {
          proxy_pass http://backend:8888/socket.io/;
          proxy_set_header Host $host;
          proxy_set_header X-Real-IP $remote_addr;
          proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
          proxy_set_header X-Forwarded-Proto $scheme;
          proxy_http_version 1.1;
          proxy_set_header Upgrade $http_upgrade;
          proxy_set_header Connection "upgrade";
      }
  }
}
