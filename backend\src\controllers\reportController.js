const {
    StudentProgramProgress,
    SubjectOutcomeAnalysis,
    ProgramOutcomeTracking,
    LearningAnalytics,
    Program,
    Subject,
    User,
    PO,
    PLO,
    Course,
    QuizResult,
    UserQuestionHistory,
    Question,
    LO
} = require('../models');
const { Op } = require('sequelize');

// <PERSON><PERSON>o cáo tổng quan chương trình
const getProgramOverviewReport = async (req, res) => {
    try {
        const { program_id } = req.params;
        const { semester, academic_year } = req.query;

        // Validate program
        const program = await Program.findByPk(program_id);
        if (!program) {
            return res.status(404).json({ error: 'Program not found' });
        }

        // L<PERSON>y thống kê tổng quan
        const totalStudents = await StudentProgramProgress.count({
            where: { program_id }
        });

        const activeStudents = await StudentProgramProgress.count({
            where: {
                program_id,
                student_status: 'active'
            }
        });

        const graduatedStudents = await StudentProgramProgress.count({
            where: {
                program_id,
                student_status: 'graduated'
            }
        });

        // L<PERSON>y điểm trung bình chương trình
        const studentProgresses = await StudentProgramProgress.findAll({
            where: { program_id },
            attributes: ['overall_progress']
        });

        const averageGPA = studentProgresses.length > 0
            ? studentProgresses.reduce((sum, progress) =>
                sum + (progress.overall_progress.gpa || 0), 0) / studentProgresses.length
            : 0;

        // Lấy phân tích PO/PLO
        const poAnalysis = await getPOAnalysisByProgram(program_id);
        const ploAnalysis = await getPLOAnalysisByProgram(program_id);

        // Lấy phân tích môn học
        const subjectAnalysis = await getSubjectAnalysisByProgram(program_id, semester, academic_year);

        res.json({
            program: {
                program_id: program.program_id,
                name: program.name,
                description: program.description
            },
            overview: {
                total_students: totalStudents,
                active_students: activeStudents,
                graduated_students: graduatedStudents,
                graduation_rate: totalStudents > 0 ? (graduatedStudents / totalStudents * 100) : 0,
                average_gpa: Math.round(averageGPA * 100) / 100
            },
            po_analysis: poAnalysis,
            plo_analysis: ploAnalysis,
            subject_analysis: subjectAnalysis,
            generated_at: new Date()
        });

    } catch (error) {
        console.error('Error generating program overview report:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
};

// Báo cáo chi tiết sinh viên
const getStudentDetailReport = async (req, res) => {
    try {
        const { user_id, program_id } = req.params;

        // Lấy thông tin sinh viên
        const student = await User.findByPk(user_id, {
            attributes: ['user_id', 'name', 'email']
        });

        if (!student) {
            return res.status(404).json({ error: 'Student not found' });
        }

        // Lấy tiến độ chương trình
        const progress = await StudentProgramProgress.findOne({
            where: { user_id, program_id },
            include: [{
                model: Program,
                as: 'Program',
                attributes: ['program_id', 'name']
            }]
        });

        if (!progress) {
            return res.status(404).json({ error: 'Student progress not found' });
        }

        // Lấy tracking PO/PLO
        const outcomeTracking = await ProgramOutcomeTracking.findAll({
            where: { user_id, program_id, is_active: true },
            include: [{
                model: PO,
                as: 'PO',
                attributes: ['po_id', 'name', 'description']
            }, {
                model: PLO,
                as: 'PLO',
                attributes: ['plo_id', 'description']
            }]
        });

        // Lấy lịch sử quiz
        const quizHistory = await getStudentQuizHistory(user_id, program_id);

        // Tính toán radar chart data
        const radarData = await calculateStudentRadarData(user_id, program_id);

        res.json({
            student: student,
            program: progress.Program,
            progress: progress,
            outcome_tracking: outcomeTracking,
            quiz_history: quizHistory,
            radar_data: radarData,
            performance_summary: {
                overall_gpa: progress.overall_progress.gpa,
                completion_rate: progress.overall_progress.completion_percentage,
                credits_earned: progress.overall_progress.credits_earned,
                total_credits: progress.overall_progress.total_credits_required,
                status: progress.student_status
            },
            generated_at: new Date()
        });

    } catch (error) {
        console.error('Error generating student detail report:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
};

// Báo cáo so sánh môn học
const getSubjectComparisonReport = async (req, res) => {
    try {
        const { program_id } = req.params;
        const { semester, academic_year, subject_ids } = req.query;

        let whereClause = { program_id };
        if (semester) whereClause.analysis_semester = semester;
        if (academic_year) whereClause.academic_year = academic_year;
        if (subject_ids) {
            const subjectIdArray = subject_ids.split(',').map(id => parseInt(id));
            whereClause.subject_id = { [Op.in]: subjectIdArray };
        }

        const subjectAnalyses = await SubjectOutcomeAnalysis.findAll({
            where: whereClause,
            include: [{
                model: Subject,
                as: 'Subject',
                attributes: ['subject_id', 'name', 'description']
            }],
            order: [['analysis_date', 'DESC']]
        });

        const comparison = {
            subjects: [],
            comparative_metrics: {
                highest_average_score: 0,
                lowest_average_score: 100,
                highest_completion_rate: 0,
                lowest_completion_rate: 100,
                most_challenging_subject: null,
                easiest_subject: null
            }
        };

        subjectAnalyses.forEach(analysis => {
            const subjectData = {
                subject: analysis.Subject,
                statistics: analysis.subject_statistics,
                po_achievement: analysis.po_achievement,
                plo_achievement: analysis.plo_achievement,
                difficulty_analysis: analysis.difficulty_analysis,
                improvement_recommendations: analysis.improvement_recommendations
            };

            comparison.subjects.push(subjectData);

            // Update comparative metrics
            const avgScore = analysis.subject_statistics.average_score;
            const completionRate = analysis.subject_statistics.completion_rate;

            if (avgScore > comparison.comparative_metrics.highest_average_score) {
                comparison.comparative_metrics.highest_average_score = avgScore;
                comparison.comparative_metrics.easiest_subject = analysis.Subject.name;
            }

            if (avgScore < comparison.comparative_metrics.lowest_average_score) {
                comparison.comparative_metrics.lowest_average_score = avgScore;
                comparison.comparative_metrics.most_challenging_subject = analysis.Subject.name;
            }

            if (completionRate > comparison.comparative_metrics.highest_completion_rate) {
                comparison.comparative_metrics.highest_completion_rate = completionRate;
            }

            if (completionRate < comparison.comparative_metrics.lowest_completion_rate) {
                comparison.comparative_metrics.lowest_completion_rate = completionRate;
            }
        });

        res.json({
            program_id,
            filter_criteria: { semester, academic_year, subject_ids },
            comparison,
            generated_at: new Date()
        });

    } catch (error) {
        console.error('Error generating subject comparison report:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
};

// Helper functions
const getPOAnalysisByProgram = async (program_id) => {
    const poTracking = await ProgramOutcomeTracking.findAll({
        where: { program_id, outcome_type: 'PO', is_active: true },
        include: [{
            model: PO,
            as: 'PO',
            attributes: ['po_id', 'name', 'description']
        }]
    });

    const analysis = {};
    poTracking.forEach(tracking => {
        const po_id = tracking.po_id;
        if (!analysis[po_id]) {
            analysis[po_id] = {
                po: tracking.PO,
                students_count: 0,
                average_score: 0,
                achievement_rate: 0,
                scores: []
            };
        }
        analysis[po_id].students_count++;
        analysis[po_id].scores.push(tracking.current_score);
    });

    // Calculate averages
    Object.keys(analysis).forEach(po_id => {
        const data = analysis[po_id];
        data.average_score = data.scores.reduce((sum, score) => sum + score, 0) / data.scores.length;
        data.achievement_rate = (data.scores.filter(score => score >= 70).length / data.scores.length) * 100;
        delete data.scores;
    });

    return analysis;
};

const getPLOAnalysisByProgram = async (program_id) => {
    const ploTracking = await ProgramOutcomeTracking.findAll({
        where: { program_id, outcome_type: 'PLO', is_active: true },
        include: [{
            model: PLO,
            as: 'PLO',
            attributes: ['plo_id', 'description']
        }]
    });

    const analysis = {};
    ploTracking.forEach(tracking => {
        const plo_id = tracking.plo_id;
        if (!analysis[plo_id]) {
            analysis[plo_id] = {
                plo: tracking.PLO,
                students_count: 0,
                average_score: 0,
                achievement_rate: 0,
                scores: []
            };
        }
        analysis[plo_id].students_count++;
        analysis[plo_id].scores.push(tracking.current_score);
    });

    // Calculate averages
    Object.keys(analysis).forEach(plo_id => {
        const data = analysis[plo_id];
        data.average_score = data.scores.reduce((sum, score) => sum + score, 0) / data.scores.length;
        data.achievement_rate = (data.scores.filter(score => score >= 70).length / data.scores.length) * 100;
        delete data.scores;
    });

    return analysis;
};

const getSubjectAnalysisByProgram = async (program_id, semester, academic_year) => {
    let whereClause = { program_id };
    if (semester) whereClause.analysis_semester = semester;
    if (academic_year) whereClause.academic_year = academic_year;

    const analyses = await SubjectOutcomeAnalysis.findAll({
        where: whereClause,
        include: [{
            model: Subject,
            as: 'Subject',
            attributes: ['subject_id', 'name']
        }],
        limit: 10,
        order: [['analysis_date', 'DESC']]
    });

    return analyses.map(analysis => ({
        subject: analysis.Subject,
        average_score: analysis.subject_statistics.average_score,
        completion_rate: analysis.subject_statistics.completion_rate,
        pass_rate: analysis.subject_statistics.pass_rate
    }));
};

const getStudentQuizHistory = async (user_id, program_id) => {
    const quizResults = await QuizResult.findAll({
        where: { user_id },
        include: [{
            model: Quiz,
            include: [{
                model: Subject,
                include: [{
                    model: Course,
                    where: { program_id }
                }]
            }]
        }],
        order: [['createdAt', 'DESC']],
        limit: 20
    });

    return quizResults.map(result => ({
        quiz_id: result.quiz_id,
        quiz_name: result.Quiz?.name,
        subject_name: result.Quiz?.Subject?.name,
        score: result.score,
        completed_at: result.createdAt
    }));
};

const calculateStudentRadarData = async (user_id, program_id) => {
    // Simplified radar data calculation
    const outcomeTracking = await ProgramOutcomeTracking.findAll({
        where: { user_id, program_id, is_active: true }
    });

    const radarData = {
        po_scores: {},
        plo_scores: {}
    };

    outcomeTracking.forEach(tracking => {
        if (tracking.outcome_type === 'PO') {
            radarData.po_scores[tracking.po_id] = tracking.current_score;
        } else if (tracking.outcome_type === 'PLO') {
            radarData.plo_scores[tracking.plo_id] = tracking.current_score;
        }
    });

    return radarData;
};

module.exports = {
    getProgramOverviewReport,
    getStudentDetailReport,
    getSubjectComparisonReport
};
