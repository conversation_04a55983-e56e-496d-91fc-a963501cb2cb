'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
    class PLO extends Model {
        static associate(models) {
            PLO.belongsTo(models.Program, { foreignKey: 'program_id' });
            PLO.belongsToMany(models.PO, { through: models.POsPLOs, foreignKey: 'plo_id' });
            PLO.hasMany(models.Subject, { foreignKey: 'plo_id' });

        }
    }

    PLO.init(
        {
            plo_id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true,
                autoIncrementIdentity: true
            },
            description: {
                type: DataTypes.TEXT,
                allowNull: false,
            },
            program_id: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: 'Programs',
                    key: 'program_id',
                },
            },
        },
        {
            sequelize,
            modelName: 'PLO',
            tableName: 'PLOs',
        }
    );

    return PLO;
};