'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
    class Answer extends Model {
        static associate(models) {
            Answer.belongsTo(models.Question, { foreignKey: 'question_id' });
        }
    }

    Answer.init(
        {
            answer_id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true,
                autoIncrementIdentity: true
            },
            question_id: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: 'Questions',
                    key: 'question_id',
                },
            },
            answer_text: {
                type: DataTypes.TEXT,
                allowNull: false,
            },
            iscorrect: {
                type: DataTypes.BOOLEAN,
                allowNull: false,
            },
        },
        {
            sequelize,
            modelName: 'Answer',
            tableName: 'Answers',
        }
    );

    return Answer;
};